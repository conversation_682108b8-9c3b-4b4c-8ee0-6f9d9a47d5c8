files:
  "/etc/cwagentconfig.json":
    mode: "000644"
    owner: "root"
    group: "root"
    content: |
      {
        "agent":{
            "metrics_collection_interval": 60,
            "run_as_user": "root"
        },
        "metrics":{
            "append_dimensions":{
              "AutoScalingGroupName":"${aws:AutoScalingGroupName}",
              "InstanceId":"${aws:InstanceId}"
            },
            "metrics_collected":{
              "mem":{
                  "measurement":[
                    "mem_used_percent"
                  ],
                  "metrics_collection_interval": 60
              }
            }
        },
        "logs": {
          "logs_collected": {
            "files": {
              "collect_list": []
            }
          }
        }
      }

  "/etc/cwagentconfig-worker.json":
    mode: "000644"
    owner: "root"
    group: "root"
    content: |
      {
        "agent": {
          "metrics_collection_interval": 60,
          "run_as_user": "root"
        },
        "metrics":{
            "append_dimensions":{
              "AutoScalingGroupName":"${aws:AutoScalingGroupName}",
              "InstanceId":"${aws:InstanceId}"
            },
            "metrics_collected":{
              "mem":{
                  "measurement":[
                    "mem_used_percent"
                  ],
                  "metrics_collection_interval": 60
              }
            }
        },
        "logs": {
          "logs_collected": {
            "files": {
              "collect_list": []
            }
          }
        }
      }

  "/etc/cwagentconfig-intg-worker.json":
    mode: "000644"
    owner: "root"
    group: "root"
    content: |
      {
        "agent": {
          "metrics_collection_interval": 60,
          "run_as_user": "root"
        },
        "metrics":{
            "append_dimensions":{
              "AutoScalingGroupName":"${aws:AutoScalingGroupName}",
              "InstanceId":"${aws:InstanceId}"
            },
            "metrics_collected":{
              "mem":{
                  "measurement":[
                    "mem_used_percent"
                  ],
                  "metrics_collection_interval": 60
              }
            }
        },
        "logs": {
          "logs_collected": {
            "files": {
              "collect_list": []
            }
          }
        }
      }

# Re-enable Instructions:
# In order to configure CloudWatch logs again, remove the (&& false) check from the main if condition ("$RAILS_ENV" == "production" && false)
# Add the desired log group objects(commented at the end of this file) to collect_list array in the above json configuration

container_commands:
  01_check_env_type:
    command: |
      if [ "$RAILS_ENV" == "production" && false ]; then
        if [ "$WORKER" ]; then
          if [ "$INTEGRATION_WORKER" ]; then
            sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/etc/cwagentconfig-intg-worker.json -s
          else
            sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/etc/cwagentconfig-worker.json -s
          fi
        else
          sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/etc/cwagentconfig.json -s
        fi
      fi


# TODO: Backup For Removed Logs
# File Name: "/etc/cwagentconfig.json"
# Logs:
#  {
#    "file_path": "/var/app/current/log/production.log",
#    "log_group_name": "Genuity-Web-production.log",
#    "log_stream_name": "{instance_id}",
#    "retention_in_days": 3
#  }

# File Name: "/etc/cwagentconfig-worker.json"
# Logs:
#   {
#     "file_path": "/var/app/current/log/sidekiq_default.log",
#     "log_group_name": "Genuity-Worker-sidekiq_default.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_import.log",
#     "log_group_name": "Genuity-Worker-sidekiq_import.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_critical.log",
#     "log_group_name": "Genuity-Worker-sidekiq_critical.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_automated_tasks.log",
#     "log_group_name": "Genuity-Worker-sidekiq_automated_tasks.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_email.log",
#     "log_group_name": "Genuity-Worker-sidekiq_email.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_at_actions.log",
#     "log_group_name": "Genuity-Worker-sidekiq_at_actions.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   }

# File Name: "/etc/cwagentconfig-intg-worker.json"
# Logs:
#   {
#     "file_path": "/var/app/current/log/sidekiq_integrations.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_integrations.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_field_permissions.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_field_permissions.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_assets.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_assets.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_at_actions_2.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_at_actions_2.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_automated_tasks_2.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_automated_tasks_2.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   },

#   {
#     "file_path": "/var/app/current/log/sidekiq_critical_2.log",
#     "log_group_name": "Genuity-Intg-Worker-sidekiq_critical_2.log",
#     "log_stream_name": "{instance_id}",
#     "retention_in_days": 3
#   }
