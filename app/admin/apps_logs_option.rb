ActiveAdmin.register AppsLogsOption do
  menu parent: 'App', label: "Apps logs Options"
  breadcrumb do
    ['admin', 'apps']
  end

  actions :all, :except => [:destroy]
  permit_params :company_id, :enabled

  filter :company, collection: proc { Company.order("lower(name) ASC").all }
  filter :company_subdomain_contains, :as => :string, collection: proc { Company.order("lower(subdomain) ASC").all }
  filter :enabled
  
  index do
    id_column
    column :company_name do |apps_logs_option|
      apps_logs_option.company.name
    end

    column :company_subdomain do |apps_logs_option|
      apps_logs_option.company.subdomain
    end
    
    column ("Enabled"), sortable: :enabled do |apps_logs_option|
      apps_logs_option.enabled
    end
    column "Actions" do |apps_logs_option|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(apps_logs_option), :class => "member_link edit_link"
      links
    end
  end

  controller do
    include MultiCompany::GeneralScoping
    
    before_action :verify_super_admin_mfa

    def scoped_collection
      super.eager_load :company
    end
  end
end
