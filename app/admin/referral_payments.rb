ActiveAdmin.register ReferralPayment do
  menu parent: 'Companies', label: "Referral Payment", priority: 4
  breadcrumb do
    ['admin', 'companies']
  end
  permit_params :amount, :date_sent, :notes, :sent_to_id
  filter :date_sent

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa

    def scoped_collection
      super.includes(:referrals)
    end
  end

  index do
    selectable_column
    id_column
    column :amount
    column :date_sent
    column ("Sent to") { |referral_payment| referral_payment.referrals.first.referrer.email }
    column :notes
    actions defaults: false do |referral_payment|
      item "Edit", edit_admin_referral_payment_path(referral_payment), class: "member_link"
    end
  end

  form do |f|
    f.inputs 'Details' do
      f.input :amount
      f.input :date_sent
      f.input :sent_to_id, label: 'Sent to', as: :select, collection: referral_payment.referrals&.first&.referrer&.user_locations&.map {|loc| ["#{loc.address_line_1}, #{loc.address_line_2} #{loc.city} #{loc.state} #{loc.zip}", loc.id]}
      f.input :notes
    end
    f.actions
  end
end