ActiveAdmin.register ScrapHpAsset do
  menu parent: "Miscellaneous", priority: 2, label: "Scrap HP Assets"
  breadcrumb do
    ['admin', 'miscellaneous']
  end

  action_item :fetch_serial_numbers, only: :index do
    link_to 'Download Serial Numbers', fetch_serial_numbers_admin_scrap_hp_assets_path, data: { turbolinks: false, method: :get, target: '_blank'}
  end

  collection_action :fetch_serial_numbers do
    manufacturers = ["Hp","hP","HP","hp"]
    machine_serial_numbers = ManagedAsset.where(manufacturer: manufacturers, merged: false).where.not(machine_serial_number: '').pluck(:machine_serial_number).uniq.compact.to_a

    machine_serial_numbers_array = machine_serial_numbers.map { |serial_number| "'#{serial_number}'" }
    txt_data = machine_serial_numbers_array.join(", ")
    send_data txt_data, filename: "assets_serial_numbers.text", disposition: 'attachment'
  end

  form do |f|
    f.inputs "Scrap Details" do
      f.input :warranty_file, as: :file
    end
    f.actions
  end

  show do
    attributes_table do
      row :warranty_file do |hp_warranty|
        link_to "Download Warranty", hp_warranty.warranty_file.url
      end
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa

    def create
      if (params[:scrap_hp_asset].present?) && (params[:scrap_hp_asset][:warranty_file].present?)
        @hp_warranty = ScrapHpAsset.new(hp_warranty_params)
        if @hp_warranty.save
          file_path = @hp_warranty.warranty_file.blob.key
          UpdateWarrantyWorker.perform_async(file_path)
          redirect_to admin_scrap_hp_asset_path(@hp_warranty), notice: "HP warranty was successfully created."
        else
          render :new
        end
      else
        redirect_to new_admin_scrap_hp_asset_path, alert: "Please upload a Hp warranty file."
      end
    end

    private

    def hp_warranty_params
      params.require(:scrap_hp_asset).permit(:warranty_file)
    end
  end
end
