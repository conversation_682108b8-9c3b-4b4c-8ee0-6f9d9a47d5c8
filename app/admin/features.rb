ActiveAdmin.register ServiceOption, as: 'features' do
  menu parent: 'Manage Services', priority: 3
  breadcrumb do
    ['admin', 'manage services']
  end

  filter :service_name
  filter :status, as: :select, collection: [['Enabled', false], ['Disabled', true]]

  actions :all, except: [:new, :destroy]
  config.batch_actions = false

  action_item :toggle_feature, only: :index do
    link_to 'Disable/Enable', toggle_feature_admin_features_path, method: :post, 
            data: { confirm: 'Are you sure you want to disable/enable this feature?' }, class: 'button'
  end

  index do
    column :service_name
    column('Status') { |option| option.status ? 'Disabled' : 'Enabled' }
    column :created_at
    column :updated_at
    actions defaults: false do |service_option|
      item 'View', admin_feature_path(service_option), class: 'member_link'
      item 'Edit', edit_admin_feature_path(service_option), class: 'member_link'
    end
  end

  show do
    attributes_table do
      row ('service name') { |option| option.service_name }
      row ('type') { |option| option.service_type }
      row ('status') { |option| option.status ? 'Disabled' : 'Enabled' }
      row ('created_at') { |option| option.created_at }
      row ('updated_at') { |option| option.updated_at }
    end
  end

  form do |f|
    f.inputs do
      f.input :service_name, input_html: { disabled: true }
      f.input :service_type, as: :select, input_html: { disabled: true }
      f.input :status
    end
    f.actions
  end

  collection_action :toggle_feature, method: :post do
    flash[:notice] = 'Feature visibility updated successfully.'
    redirect_to admin_features_path
  end

  controller do
    def permitted_params
      params.permit(service_option: [:status])
    end

    def scoped_collection
      super.where(service_type: 'feature')
    end
  end
end
