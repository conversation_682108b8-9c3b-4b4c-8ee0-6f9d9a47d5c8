ActiveAdmin.register FeatureRequest do
  menu parent: "Modules Data", label: "Feature Requests", priority: 5
  permit_params :id, :title, :description, :module_type, :feature_state
  breadcrumb do
    ['admin', 'modules data']
  end
  
  form do |f|
    f.inputs 'Details' do
      f.semantic_errors
      f.inputs do
        f.has_many :feature_request_image, heading: 'Cover Photo' do |image|
          if image.object.attachment.attached?
            span image.object.attachment.filename.to_s, class: 'image-file-name'
          end
          image.input :attachment, :as => :file, input_html: { accept: ".jpg, .jpeg, .png" }
        end
      end
      f.input :title
      f.input :description
      f.input :module_type, include_blank: false
      f.input :feature_state, include_blank: false
    end

    f.actions
  end

  index do
    id_column
    column :title do |feature_request|
      truncate(feature_request.title, omision: "...", length: 50)
    end
    column :description do |feature_request|
      truncate(feature_request.description, omision: "...", length: 100)
    end
    column :likes do |feature_request|
      feature_request.feature_request_votes.where(vote_type: :like).count
    end
    column :dislikes do |feature_request|
      feature_request.feature_request_votes.where(vote_type: :dislike).count
    end
    column :module_type do |feature_request|
      feature_request.module_type.try(:titleize)
    end
    column :company_name do |feature_request|
      feature_request.creator&.company&.name
    end
    actions
  end

  filter :feature_state, :as => :select, :collection => FeatureRequest.feature_states.map { |state| [state[0].titleize, state[1]] }

  show do
    panel "Feature Request Details" do
      attributes_table_for feature_request do
        row :id
        row :title
        row :description
        row :likes do
          feature_request.feature_request_votes.where(vote_type: :like).count
        end
        row :dislikes do
          feature_request.feature_request_votes.where(vote_type: :dislike).count
        end
        row :module_type do
          feature_request.module_type.try(:titleize)
        end
        row :feature_state do
          feature_request.feature_state.try(:titleize)
        end
        row :cover_photo do
          if feature_request.feature_request_image.present?
            feature_request.feature_request_image.attachment.filename.to_s
          else
            'No Cover Photo'
          end
        end
      end
    end

    panel "Feature Request Comments" do
      table_for feature_request.feature_request_comments do
        column :id
        column (:comment_body) { |feature_request_comment| raw(feature_request_comment.comment_body) }
        column :creator
      end
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def create
      @feature_request = FeatureRequest.new(feature_request_params)
      if @feature_request.save
        flash[:success] = "Feature is added successfully."
        redirect_to admin_feature_requests_path
      else
        flash[:error] = "Unable to add feature. #{@feature_request.errors.full_messages.to_sentence}"
        redirect_back(fallback_location: root_path)
      end
    end

    def update
      @feature_request = FeatureRequest.find_by(id: params[:id])
      if @feature_request.present?
        if @feature_request.update(feature_request_params)
          flash[:success] = "Feature request has been updated"
          redirect_to admin_feature_requests_path
        else
          flash[:error] = "Feature request didn't updated.\n#{@feature_request.errors.full_messages.to_sentence}"
          redirect_back(fallback_location: root_path)
        end
      else
        flash[:error] = "Unable to find feature request"
        redirect_back(fallback_location: root_path)
      end
    end

    def destroy
      @feature_request = FeatureRequest.where(id: params[:id]).first
      if @feature_request.try(:destroy!)
        flash[:success] = "Feature request deleted successfully"
        redirect_to admin_feature_requests_path
      else
        flash[:error] = "Feature request deletion failed"
        redirect_to admin_feature_requests_path
      end
    rescue => e
      flash[:error] = "Feature request deletion failed: #{e.message}"
      redirect_to admin_feature_requests_path
    end

    def feature_request_params
      params.require(:feature_request).permit(
        :id,
        :title,
        :description,
        :module_type,
        :feature_state,
        feature_request_image_attributes: [ :attachment ]
      )
    end
  end
end
