ActiveAdmin.register CompanyIntegration, as: "Manage Integrations" do
  menu parent: 'Manage Services', label: "Manage Integrations", priority: 4
  breadcrumb do
    ['admin', 'manage services']
  end
  
  actions :all, except: [:new, :destroy]
  filter :company_id, as: :searchable_select, 
                      collection: -> { Company.pluck(:name, :id) }, 
                      label: "Search by Company"
  filter :is_force_stopped, as: :select, 
                            collection: [['True', true], ['False', false]], 
                            label: "Disabled"
  filter :integrable_type, as: :searchable_select, 
                           collection: -> { CompanyIntegration.distinct.pluck(:integrable_type) }, 
                           label: "Integration Type"

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]
  end

  index title: "Manage Integrations" do
    selectable_column
    column :company do |integration|
      integration.company.name
    end
    column :integrable_type
    column :status
    column :sync_status
    column  ('Disabled') ,:is_force_stopped do |integration|
      status_tag integration.is_force_stopped ? 'Yes' : 'No'
    end
    column :actions do |integration|
      link_to(
        integration.is_force_stopped ? 'Enable' : 'Disable',
        toggle_force_stop_admin_manage_integration_path(integration),
        data: { confirm: "Are you sure you want to #{integration.is_force_stopped ? 'enable': 'disable'} this integration?" },
        method: :patch,
        class: 'button'
      )
    end
  end

  batch_action :toggle_integrations, confirm: "Are you sure you want to change the status of the selected integrations?" do |ids|
    integrations = CompanyIntegration.find(ids)
    integrations.each do |integration|
      new_state = !integration.is_force_stopped
      integration.update_column(:is_force_stopped, new_state)
    end
    flash[:notice] = "Successfully updated the status of selected integrations."
    redirect_to collection_path
  end

  member_action :toggle_force_stop, method: :patch do
    integration = CompanyIntegration.find_by(id:params[:id])
    if integration
      new_state = !integration.is_force_stopped
      integration.update_column(:is_force_stopped, new_state)
      flash[:notice] = "#{integration.integrable_type} is now #{new_state ? 'disabled' : 'enabled'}."
    else
      flash[:error] = "Integration not found."
    end
    redirect_back(fallback_location: admin_manage_integrations_path)
  end
end
