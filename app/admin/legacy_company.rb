ActiveAdmin.register Company, as: "Legacy Companies" do
  menu parent: 'Companies', priority: 7

  breadcrumb do
    ['admin', 'companies']
  end

  filter :name_or_subdomain_contains, as: :string

  scope :all, default: true
  scope("Legacy Companies") { |scope| scope.where(is_legacy_company: true, show_new_plans: false) }
  scope("Non-Legacy Companies") { |scope| scope.where(is_legacy_company: false, show_new_plans: true) }

  actions :all, except: [:new, :create]

  form do |f|
    f.inputs 'Details' do
      f.input :name, input_html: { disabled: true }
      f.input :subdomain, input_html: { disabled: true }
      f.input :is_legacy_company
    end

    f.actions
  end

  index do
    column :name
    column :subdomain
    column ("Email") { |company| company.admin_users.first&.email }
    column "Legacy Company", :is_legacy_company

    column "Actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(resource), class: "member_link edit_link"
      links
    end
  end

  controller do
    def update
      @company = Company.find_by_cache(id: params[:id])
      if @company
        @company.update_columns(
          is_legacy_company: params[:company][:is_legacy_company] == "1",
          show_new_plans: params[:company][:is_legacy_company] != "1"
        )
        flash[:success] = "Company has been updated"
        redirect_to admin_legacy_companies_path
      else
        flash[:error] = "Unable to update company"
        redirect_back(fallback_location: root_path)
      end
    end
  end
end
