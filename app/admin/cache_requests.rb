ActiveAdmin.register ServiceOption, as: 'cache_requests' do
  menu parent: 'Manage Services', priority: 1
  breadcrumb do
    ['admin', 'manage services']
  end

  filter :service_name
  filter :status, as: :select, collection: [['Enabled', true], ['Disabled', false]]

  actions :all, except: [:new, :destroy]
  config.batch_actions = false

  action_item :clear_cache, only: :index do
    link_to 'Clear Cache', clear_cache_admin_cache_requests_path, method: :post, 
            data: { confirm: 'Are you sure you want to clear the cache?' }, class: 'button'
  end

  index do
    column :service_name
    column('Status') { |option| option.status ? 'Enabled' : 'Disabled' }
    column :created_at
    column :updated_at
    actions defaults: false do |service_option|
      item 'View', admin_cache_request_path(service_option), class: 'member_link'
      item 'Edit', edit_admin_cache_request_path(service_option), class: 'member_link'
    end
  end

  show do
    attributes_table do
      row ('service name') { |option| option.service_name }
      row ('type') { |option| option.service_type }
      row ('status') { |option| option.status ? 'Enabled' : 'Disabled' }
      row ('created_at') { |option| option.created_at }
      row ('updated_at') { |option| option.updated_at }
    end
  end

  form do |f|
    f.inputs do
      f.input :service_name, input_html: { disabled: true }
      f.input :service_type, as: :select, input_html: { disabled: true }
      f.input :status
    end
    f.actions
  end

  collection_action :clear_cache, method: :post do
    Rails.cache.clear
    flash[:notice] = 'Cache cleared successfully.'
    redirect_to admin_cache_requests_path
  end

  controller do
    def permitted_params
      params.permit(service_option: [:status])
    end

    def scoped_collection
      super.where(service_type: 'cache_type')
    end
  end
end
