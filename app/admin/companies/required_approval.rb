include DatetimesHelper

ActiveAdmin.register Company, as: "Required Approval" do
  menu parent: 'Companies', priority: 6
  breadcrumb do
    ['admin', 'companies']
  end
  permit_params :id, :name, :subdomain, :email, company_users_attributes: [ :granted_access_at, user_attributes: [:id, :email, :password, :first_name, :last_name] ]

  filter :name_or_subdomain_contains, :as => :string

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :subdomain
      f.input :email
      f.input :verified
    end

    f.actions
  end

  index do
    column :name do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end
        
    column :subdomain
    column :url
    column ("Email") { |company| "#{ company.admin_users.first&.email}" }
    column ("Review Status") { |company| company.verified? ? "Approved" : "Pending" }

    column "Actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), '#', :class => "member_link delete_link", onclick: 'deleteCompany(event)', 'data-companyId': resource.id
      links
    end
  end

  controller do
    before_action :set_user, only: [:create]

    def index
      scope = Company.where(verified: false).order("last_logged_in_at DESC nulls last").paginate(page: params[:page], per_page: 10)
      @collection = scope.page(params[:page]) if params[:q].blank?
      super
    end

    def update
      @company = Company.find_by_cache(id: params[:id])
      if @company.present?
        if @company.update(company_params)
          if @company.saved_changes[:verified]
            cu = @company.admin_company_users.first
            access = company_params[:verified] == '0' ? nil : DateTime.now
            if cu.update_columns(granted_access_at: access)
              access ? add_access_to_sample_company(cu) : remove_access_from_sample_company(cu)
            end
          end
          flash[:success] = "company has been updated"
          redirect_to admin_companies_path
        else
          flash[:error] = "company has not updated. It has some invalid values.\n#{@company.errors.full_messages.to_sentence}"
          redirect_back(fallback_location: root_path)
        end
      else
        flash[:error] = "company didn't updated"
        redirect_back(fallback_location: root_path)
      end
    end

    def destroy
      begin
        @company = Company.unscoped.find_by(id: params[:id], name: params[:company_name])
        if @company
          CompanyNuker.perform_async(@company.id)
          flash[:alert] = "Company deletion initiated."
          render json: {}, status: :ok
        end
      rescue => e
        flash[:error] = "Company deletion failed: #{e.message}"
        render json: {}, status: :unprocessable_entity
      end
    end

    private

    def add_access_to_sample_company(company_user)
      AssignAdminsToSampleCompaniesWorker.perform_async(company_user.id)
    end

    def remove_access_from_sample_company(company_user)
      DeleteAdminsAccessFromSampleCompaniesWorker.perform_async(company_user.id, @company.id)
    end

    def create_company_user(admin)
      admin.create_company_user(company_id: admin.company.id, invitation_delivered: true)
    end

    def company_params
      params.require(:company).permit(
        :id,
        :name,
        :subdomain,
        :email,
        :free_trial_days,
        :verified,
        company_users_attributes:
        [
          :granted_access_at,
          user_attributes: [
            :id,
            :email,
            :password,
            :first_name,
            :last_name
          ]
        ]
      )
    end

    def company_without_user_params
      params.require(:company).permit(
        :id,
        :name,
        :subdomain,
        :phone_number,
        :email
      )
    end

    def set_user
      @user = User.find_by_cache(email: params[:company][:company_users_attributes]["0"][:user_attributes][:email])
    end
  end

  action_item :edit, only: :edit do
    link_to 'Change Admin', reset_admin_admin_company_path(resource)
  end
end
