include DatetimesHelper

ActiveAdmin.register Company, as: "Microsoft SSO Configuration" do
  menu parent: 'Companies', priority: 6, label: "Microsoft SSO Configuration"
  breadcrumb do
    ['admin', 'companies']
  end
  actions :all, except: [:destroy, :create, :new]
  permit_params :id, :microsoft_sso_enabled

  filter :name_or_subdomain_contains, :as => :string
  filter :microsoft_sso_enabled

  form do |f|
    f.inputs 'Details' do
      f.input :name, input_html: { disabled: true }
      f.input :microsoft_sso_enabled
    end

    f.actions
  end

  index do
    column :name
    column :subdomain
    column ("SSO Enabled") do |company|
      company.microsoft_sso_enabled
    end

    column "Actions" do |company|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(company), :class => "member_link edit_link"
      links
    end
  end

  controller do
    def update
      @company = Company.find_by_cache(id: params[:id])
      if @company.present?
        if @company.update(company_params)
          flash[:success] = "Microsoft SSO is enabled"
        else
          flash[:error] = "company has not updated. It has some invalid values.\n#{@company.errors.full_messages.to_sentence}"
        end
      else
        flash[:error] = "Company not"
      end
      redirect_to admin_microsoft_sso_configurations_path
    end

    private

    def company_params
      params.require(:company).permit(
        :microsoft_sso_enabled
      )
    end
  end
end
