include DatetimesHelper

ActiveAdmin.register Company, as: "Company" do
  # See permitted parameters documentation:
  # https://github.com/activeadmin/activeadmin/blob/master/docs/2-resource-customization.md#setting-up-strong-parameters
  menu :priority => 1
  permit_params :id, :name, :subdomain, :last_logged_in_at, :phone_number, :email, locations_attributes: [:id, :company_id, :name, :address, :city, :state, :phone_number, :zip], company_users_attributes: [ :granted_access_at, user_attributes: [:id, :email, :password, :first_name, :last_name] ]

  filter :name_or_subdomain_contains, :as => :string

  config.remove_action_item(:new)

  csv do
    column :id
    column :name
    column :subdomain
    column ("email") { |company| "#{company.admins&.company_users&.first.try(:user).try(:email)}" }
    column(:Paid) { |company| "#{company.subscriptions&.where(status: "active").any?}" }
    column "Status" do |company|
      company_user = company.admins&.company_users&.first
      if company_user&.user&.has_confirmed_email?
        "Confirmed"
      else
        "Pending"
      end
    end
    column :free_trial_days
    column :is_reseller_company
    column :is_sample_company
    column :prtg_username
    column :prtg_passhash
    column :startup_info_completed
    column :location_wizard
    column :guid
    column :show_discovered_services
    column :help_ticket_number
    column :timezone
    column :stripe_id
    column :default_logo_url
    column :original_logo_url
    column :fiscal_year
    column :app_direct_uuid
    column :app_direct_admin_uuid
    column :reseller_company_id
    column :monitoring_ready
    column :logo_file_name
    column :phone_number
    column :phone_number_extension
    column :phone_number_country_code
    column :phone_number_country_code_number
    column :url
    column :last_logged_in_at
    column ("Last sign in at") { |company| "#{company.last_logged_in_at.to_written_time}" }
    column ("created_at") { |company| "#{company.created_at.to_written_time}" }
    column :updated_at
  end

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :subdomain
      f.input :phone_number
      f.input :email
      f.input :free_trial_days
      if f.object.reseller_company_id?
        f.input :reseller_company_id,
                label: 'Parent is already set',
                :as => :select, collection: Company.where(id: f.object.reseller_company_id),
                include_blank: false
      else
        f.input :reseller_company_id,
                label: 'Set parent company',
                :as => :select,
                collection: Company.where(reseller_company_id: nil).where.not(subdomain: 'sample').pluck(:name, :id).unshift(['Choose Parent Company', nil]),
                selected: 'Choose Parent Company', include_blank: false
      end
      f.input :verified
    end

    if f.object.locations.blank?
      locations = f.object.locations.build
    end

    if ! f.object.admins&.company_users.present?
      f.inputs :company_users, for: [ :company_users, CompanyUser.new ] do |company_user|
        user = company_user.object.build_user

        company_user.inputs :user, for: [ :user, User.new ] do |c_user|
          company_user.input :granted_access_at, :as => :hidden, input_html: { :value => DateTime.now }
          c_user.input :first_name
          c_user.input :last_name
          c_user.input :email
          c_user.input :password
        end
      end
    end
    f.actions
  end

  index do
    column :name do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end

    column :subdomain
    column "Email" do |company|
      if company.admins
        "#{ company.admins.company_users.first.try(:user).try(:email)}"
      end
    end
    column ("Paid") { |company| "#{ company.has_paid_subscription? ? "Yes" : "No" }" }
    column ("Subscription Plan") { |company| "#{ company.has_active_subscription? ? "Active" : "Cancelled" }" }

    column "Email Status" do |company|
      admins = company.admins
      if admins
        company_user = admins.company_users.first
        if company_user&.user&.has_confirmed_email?
          "Confirmed"
        else
          "Pending"
        end
      end
    end

    column "Actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link view_link"
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), '#', :class => "member_link delete_link", onclick: 'deleteCompany(event)', 'data-companyId': resource.id
      links
    end
    column ("Last SignIn at") { |company| "#{company.last_logged_in_at.to_written_time}" }
    column ("Created at") { |company| "#{company.created_at.to_written_time}" }
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    before_action :set_user, only: [:create]

    def index
      scope = Company.order("last_logged_in_at DESC nulls last").paginate(page: params[:page], per_page: 10)
      @collection = scope.page(params[:page]) if params[:q].blank?
      super
    end

    def update
      @company = Company.find_by_cache(id: params[:id])
      if @company.present?
        cu = @company.company_users.first
        cu.granted_access_at = company_params[:verified] == '0' ? nil : DateTime.now
        cu.save!
        if @company.update(company_params)
          if @company.saved_changes[:verified]
            admin_ids = @company.admin_company_users.ids
            access = company_params[:verified] == '0' ? nil : DateTime.now
            @company.admin_company_users.update_all(granted_access_at: access)
            access ? add_access_to_sample_company(admin_ids) : remove_access_from_sample_company(admin_ids)
          end
          if @company.saved_changes[:reseller_company_id]
            set_parent_company
          end
          flash[:success] = "company has been updated"
          redirect_to admin_companies_path
        else
          flash[:error] = "company has not updated. It has some invalid values.\n#{@company.errors.full_messages.to_sentence}"
          redirect_back(fallback_location: root_path)
        end
      else
        flash[:error] = "company didn't updated"
        redirect_back(fallback_location: root_path)
      end
    end

    def destroy
      begin
        @company = Company.unscoped.find_by(id: params[:id])
        if @company
          raise "This company still has access and can't be deleted." if @company.allow_access?

          CompanyNuker.perform_async(@company.id)
          flash[:alert] = "Company deletion initiated."
          render json: {}, status: :ok
        end
      rescue => e
        flash[:error] = "Company deletion failed: #{e.message}"
        render json: {}, status: :unprocessable_entity
      end
    end

    private

    def add_access_to_sample_company(cu_ids)
      AssignAdminsToSampleCompaniesWorker.perform_async(cu_ids)
    end

    def remove_access_from_sample_company(cu_ids)
      DeleteAdminsAccessFromSampleCompaniesWorker.perform_async(cu_ids, @company.id)
    end

    def create_company_user(admin)
      admin.create_company_user(company_id: admin.company.id, invitation_delivered: true)
    end

    def company_params
      params.require(:company).permit(
        :id,
        :name,
        :subdomain,
        :phone_number,
        :email,
        :free_trial_days,
        :reseller_company_id,
        :verified,
        :default_logo_url,
        :original_logo_url,
        :phone_number_country_code,
        :phone_number_country_code_number,
        company_users_attributes:
        [
          :granted_access_at,
          user_attributes: [
            :id,
            :email,
            :password,
            :first_name,
            :last_name
          ]
        ]
      )
    end

    def company_without_user_params
      params.require(:company).permit(
        :id,
        :name,
        :subdomain,
        :phone_number,
        :email
      )
    end

    def set_user
      @user = User.find_by_cache(email: params[:company][:company_users_attributes]["0"][:user_attributes][:email])
    end

    def set_parent_company
      company_id = params['company']['reseller_company_id']
      parent_company = Company.find_cache(id: company_id)
      child_company = @company
      if LinkCompanies.new(parent_company, child_company).update_companies
        CompanyAssociationEventLog.create_association_log(
          child_company.id,
          parent_company.id,
          'super amdin',
          current_user.email
        )
      end
    end
  end

  action_item :view, only: :show do
    link_to 'Change Admin', reset_admin_admin_company_path(company)
  end

  action_item :edit, only: :edit do
    link_to 'Change Admin', reset_admin_admin_company_path(company)
  end

  member_action :reset_admin, method: [:get, :post] do
    @company = Company.find_by_cache(id: params[:id])
    if @company.present?
      if request.post?
        @user = User.find_by admin_id: @company.id
        @new_admin = User.find_by_cache(id: params[:admin])
        if @user.present? and ( @user.id != @new_admin.id )
          if ! @user.company_users.present?
            @user.create_company_user(company_id: @company.company.id)
            if @user.update( admin_id: nil, first_name: nil, last_name: nil ) && @new_admin.update(admin_id: @company.id)
              begin
                @new_admin.invite!( current_user )
              rescue Exception => e
                flash[:error] = "#{e.message}"
                return redirect_to admin_company_path(@company)
              end
              flash[:success] = "Invitaiton email to reset password has been delivered"
              return redirect_to admin_company_path(@company)
            else
              flash[:error] = "Error while updating admin"
              return redirect_to admin_company_path(@company)
            end
          end
        else
          return redirect_to admin_company_path(@company)
        end
      else
        render :reset_admin
      end
    else
      flash[:error] = "company not present."
      redirect_to admin_companies_path
    end
  end
end
