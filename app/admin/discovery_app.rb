ActiveAdmin.register DiscoveryApp do
  menu parent: 'App', priority: 2
  breadcrumb do
    ['admin', 'apps']
  end

  filter :app_type
  filter :name
  filter :version
  permit_params :name, :version, :description, :discovery_app_link, :app_type

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :version
      f.input :description
      f.input :app_type
      f.input :discovery_app_link
    end
    f.actions
  end

  index do
    id_column
    column :version
    column :name
    column :description
    column :app_type
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end

  action_item :download, only: :index do
    link_to 'Download Latest Discovery App', download_discovery_admin_discovery_apps_path
  end

  collection_action :download_discovery, method: [:get] do
    @discovery = DiscoveryApp.get_latest_version
    import_zip = FileManipulationService.new()
    data = import_zip.manipulate_zip "#{Rails.root}/tmp/discovery.zip"
    if ( data.present? )
      File.open("#{Rails.root}/tmp/discovery.zip", 'r') do |f|
        begin
          send_data(
            f.read,
            :filename => "DiscoveryTool"+".zip",
            :disposition => 'attachment',
            :url_based_filename => true
          )
        rescue Exception => e
          flash[:error] = "#{e.message}"
          redirect_back(fallback_location: root_path)
        end
      end
      File.delete("#{Rails.root}/tmp/discovery.zip")
    else
      flash[:error] = "Tool is not present. Please contact admin."
      redirect_back(fallback_location: root_path)
    end
  end
end
