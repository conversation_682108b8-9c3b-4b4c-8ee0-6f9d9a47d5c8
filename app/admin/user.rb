ActiveAdmin.register User do
  menu parent: 'Modules Data', label: "User", priority: 1
  breadcrumb do
    ['admin', 'modules data']
  end

  actions :index
  index do
    id_column
    column :email
    column :confirmation_token
  end

  filter :email

  controller do
    include MultiCompany::GeneralScoping
  
    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]
  end
end
