ActiveAdmin.register WindowScript do
  menu parent: 'App', priority: 3
  breadcrumb do
    ['admin', 'apps']
  end

  filter :name
  filter :version
  permit_params :name, :version, :description, :script_link

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :version
      f.input :description
      f.input :script_link
    end
    f.actions
  end

  index do
    id_column
    column :version
    column :name
    column :description
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  action_item :download, only: :index do
    link_to 'Download Latest script', download_script_admin_window_scripts_path
  end

  collection_action :download_script, method: [:get] do
    import_file = FileManipulationService.new()
    data = import_file.manipulate_file "tmp/script.vbs", 0
    if ( data.present? )
      File.open("tmp/script.vbs", 'r') do |f|
        begin
          send_data(
            f.read,
            :filename => "Script"+".vbs",
            :disposition => 'attachment',
            :url_based_filename => true
          )
        rescue Exception => e
          flash[:error] = "#{e.message}"
          redirect_back(fallback_location: root_path)
        end
      end
      File.delete("tmp/script.vbs")
    else
      flash[:error] = "Script file is not present. Please contact admin."
      redirect_back(fallback_location: root_path)
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
