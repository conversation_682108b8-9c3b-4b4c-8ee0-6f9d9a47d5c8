ActiveAdmin.register_page "Referrals" do
  menu parent: "Referrals", label: "Referrals", priority: 1
  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa

    def scoped_collection
      super.includes(:user)
    end

    def index
      @earned = Referral.earned.group_by(&:referrer_id)
      @paid = Referral.paid.group_by(&:referrer_id)
    end
  end

  content title: "Referrals" do
    tabs do
      tab :pending do
        render partial: 'admin/referrals/pending'
      end

      tab :paid do
        render partial: 'admin/referrals/paid'
      end
    end
  end
end
