ActiveAdmin.register WindowsExe do
  menu parent: 'App', priority: 4
  breadcrumb do
    ['admin', 'apps']
  end

  filter :app_type
  filter :name
  filter :version
  filter :app_version
  permit_params :name, :version, :description, :exe_link, :app_type, :app_version

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :version
      f.input :app_version
      f.input :description
      f.input :app_type
      f.input :exe_link
    end
    f.actions
  end

  index do
    id_column
    column :version
    column :app_version
    column :name
    column :description
    column :app_type
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  action_item :download, only: :index do
    link_to 'Download Latest Exe', download_exe_admin_windows_exes_path
  end

  collection_action :download_exe, method: [:get] do
    import_file = FileManipulationService.new()
    data = import_file.manipulate_exe_zip "#{Rails.root}/tmp/WindowExe.zip", 0
    if ( data.present? )
      File.open("#{Rails.root}/tmp/WindowExe.zip", 'r') do |f|
        begin
          send_data(
            f.read,
            :filename => "WindowExe"+".zip",
            :disposition => 'attachment',
            :url_based_filename => true
          )
        rescue Exception => e
          flash[:error] = "#{e.message}"
          redirect_back(fallback_location: root_path)
        end
      end
      File.delete("#{Rails.root}/tmp/WindowExe.zip")
    else
      flash[:error] = "Tool is not present. Please contact admin."
      redirect_back(fallback_location: root_path)
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
