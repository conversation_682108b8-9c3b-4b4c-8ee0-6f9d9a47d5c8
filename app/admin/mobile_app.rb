ActiveAdmin.register User, as: 'mobile_app' do
  menu parent: "App", label: "Mobile Devices", priority: 5
  breadcrumb do
    ['admin', 'apps']
  end

  actions :index, :show

  scope("Users with Devices", default: true) do |scope|
    scope.where(id: UserDevice.select(:user_id).distinct)
  end

  index do
    id_column
    column :email
    column :full_name
    column ("No. of User Devices") { |user| "#{user.user_devices.count}" }
    column("App Versions") do |user|
      user.user_devices.distinct.pluck(:app_version).map do |version|
        version && version.split('.').map(&:to_i).tap { |v| v << 0 until v.length >= 3 }.join('.')
      end.join(', ')
    end

    actions defaults: true do |user|
      link_to 'View Devices', admin_mobile_app_path(user)
    end
  end

  filter :email

  show do
    attributes_table do
      table_for mobile_app.user_devices do
        column :id
        column ("OS") { |user_device| "#{user_device.os}" }
        column :screen_size
        column :device_type
        column :app_version
        column :created_at
      end
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]
  end
end
