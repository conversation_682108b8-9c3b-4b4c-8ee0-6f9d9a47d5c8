ActiveAdmin.register AppVersion do
  menu parent: 'App', priority: 1, label: 'Mac Agent'
  breadcrumb do
    ['admin', 'apps']
  end

  filter :app_type
  filter :name
  filter :version
  filter :app_version
  filter :is_admin_app
  filter :is_pkg_build
  permit_params :name, :version,:app_version, :description, :installer_link, :is_admin_app, :is_pkg_build, :db_present, :app_type

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :version
      f.input :app_version
      f.input :is_admin_app
      f.input :is_pkg_build
      f.input :db_present
      f.input :description
      f.input :app_type
      f.input :installer_link
    end
    f.actions
  end

  index do
    id_column
    column :version
    column :app_version
    column :name
    column :is_admin_app
    column :is_pkg_build
    column :db_present
    column :description
    column :app_type
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  action_item :download, only: :index do
    @version = AppVersion.where(db_present: true).order("created_at").last
    if @version.present?
      link_to 'Download Latest Installer', @version.installer_link.url
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
