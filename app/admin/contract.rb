ActiveAdmin.register Contract do
  menu parent: 'Modules Data', label: "Contract", priority: 2
  breadcrumb do
    ['admin', 'modules data']
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def scoped_collection
      Contract.unscoped
    end
  end

  scope :all do |contracts|
    contracts.all
  end

  scope :ended_previous_3_months do |contracts|
    contracts.where(end_date: Date.today.beginning_of_day-3.months..Date.today.beginning_of_day)
  end

  scope :ends_next_3_months do |contracts|
    contracts.where(end_date: Date.today.beginning_of_day..Date.today.end_of_day+3.months)
  end

  scope :ends_next_4_months do |contracts|
    contracts.where(end_date: Date.today.beginning_of_day..Date.today.end_of_day+4.months)
  end

  scope :ends_next_5_months do |contracts|
    contracts.where(end_date: Date.today.beginning_of_day..Date.today.end_of_day+5.months)
  end

  index do
    id_column
    column ("Company") { |contract| "#{contract.company.name}" }
    column :supplier
    column ("Alert Dates") { |contract| "#{contract.alert_dates.present? ? contract.alert_dates.map {|a| a.date.strftime('%b %d, %Y')} : 'Not set'}" }
    column ("Start Date") { |contract| "#{contract.start_date.present? ? contract.start_date.strftime('%b %d, %Y') : 'Not set'}" }
    column ("End Date")   { |contract| "#{contract.end_date.present? ? contract.end_date.strftime('%b %d, %Y') : 'Not set'}" }
  end

  filter :supplier
  filter :alert_dates
  filter :start_date
  filter :end_date
end
