ActiveAdmin.register HelpTicket do
  menu parent: "Modules Data", label: "Help Tickets", priority: 4
  breadcrumb do
    ['admin', 'modules data']
  end
	filter :name
	filter :email
	filter :company

  controller do
    around_action :set_read_replica_db, only: [:index, :show]
  end

  index do
    id_column
    column :ticket_number
    column :name
    column :email
    column ("Creator") { |help_ticket| "#{help_ticket.creators.is_a?(String) ? help_ticket.creators : help_ticket.creators.map { |cu| cu.full_name }.join(', ')}" }
    column ("Location") { |help_ticket| "#{help_ticket.location&.address}" }
    column ("Company") { |help_ticket| "#{help_ticket.company&.name}" }
    column ("Followers") { |help_ticket| "#{help_ticket.followers.map { |c_id| Contributor.find(c_id)&.name if c_id }.join(', ')}" }
    column ("Assigned to") { |help_ticket| "#{help_ticket.assigned_users.map { |c_id| Contributor.find(c_id)&.name if c_id }.join(', ')}" }
    column :archived
    column ("created_at") { |event| "#{event.created_at.to_written_time}" }
    column ("updated_at") { |event| "#{event.updated_at.to_written_time}" }
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
