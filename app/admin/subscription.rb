ActiveAdmin.register Subscription do
  menu parent: 'Companies', label: "Subscription", priority: 2
  breadcrumb do
    ['admin', 'companies']
  end
  permit_params :end_date, :status
  actions :index, :show, :edit, :update

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    def scoped_collection
      Subscription.unscoped.includes(:company, :subscription_plan)
    end
  end

  filter :company_name, as: :string, collection: Company.order("name ASC").map { |comp| [comp.name, comp.id] }
  filter :subscription_plan_id, as: :select, collection: -> { SubscriptionPlan.group(:name).select('name, MIN(id) AS id').order(:name).map { |plan| [plan.name, plan.id] } }

  index do
    id_column
    column ("Company") { |subscription| "#{subscription.company.name}" }
    column ("Start Date") { |subscription| "#{subscription.start_date&.strftime('%b %d, %Y')}" }
    column ("End Date")   { |subscription| "#{subscription.end_date&.strftime('%b %d, %Y') }" }
    column ("Subscription Plan") { |subscription| subscription.subscription_plan.name }
  end

  form do |f|
    f.inputs 'Details' do
      f.input :end_date
      f.input :status
    end
    f.actions
  end
end
