ActiveAdmin.register DiscoveredAssetType, as: "Discovered Type" do
  menu parent: 'App', priority: 7
  breadcrumb do
    ['admin', 'apps']
  end
  filter :name

  index do
    id_column
    column ("Asset Type Name") { |discovered_type| "#{discovered_type.name}" }
    column ("Discovered Assets Count") { |discovered_type| "#{discovered_type.discovered_assets.count}" }
    column ("Companies") { |discovered_type| "#{(discovered_type.discovered_assets.map do |da|
        da.company.name  
                                                 end).uniq&.join(", ")
      }"
    }
  end
  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
