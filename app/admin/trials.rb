ActiveAdmin.register Company, as: 'trials' do
  # menu parent: 'Subscription', label: "Trials", priority: 2
  menu parent: 'Companies', label: "Trials", priority: 1
  breadcrumb do
    ['admin', 'companies']
  end
  permit_params :id, :name, :subdomain, :last_logged_in_at, :company_phone, :contact_email, locations_attributes: [:id, :company_id, :name, :address, :city, :state, :phone_number, :zip], company_users_attributes: [ :granted_access_at, user_attributes: [:id, :email, :password, :first_name, :last_name] ]

  filter :name_contains, :as => :string

  config.clear_action_items!

  index do
    column :name do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end
    column "admin" do |company|
      company.admin_users.first.full_name
    end
    column "trial end at" do |company|
      company.created_at.to_date + company.free_trial_days.days
    end
  end

  controller do
    include MultiCompany::GeneralScoping
  
    before_action :verify_super_admin_mfa
    before_action :set_user, only: [:create]

    def index
      scope = Company.joins("LEFT JOIN subscriptions ON companies.id = subscriptions.company_id").where("subscriptions.company_id IS NULL").order("last_logged_in_at DESC nulls last").paginate(page: params[:page], per_page: 10)
      @collection = scope.page(params[:page]) if params[:q].blank?
      super
    end
  end
end