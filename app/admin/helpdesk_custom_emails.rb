ActiveAdmin.register HelpdeskCustomEmail do
  menu parent: 'Modules Data', label: "Helpdesk Custom Emails", priority: 4

  permit_params :email, :company_id, :verified

  actions :all, except: [:new, :create]
  filter :email
  index do
    column :email
    column :verified
    actions defaults: false do |email|
      if !email.verified
        item "Edit", edit_admin_helpdesk_custom_email_path(email)
      end
    end
  end

  form do |f|
    f.inputs 'Details' do
      f.input :email
      f.input :verified
    end
    f.actions
  end

  show do
    attributes_table do
      row :email
      row :verified
    end
  end

  controller do
    around_action :set_read_replica_db, only: [:index, :show]

    def update
      if EmailIdentityService.new.check_email_verification?(email_params[:email])
        if email.update_column(:verified, email_params[:verified])
          flash[:success] = "Helpdesk custom email verified successfully."
          redirect_to admin_helpdesk_custom_emails_path
        else
          flash[:error] = "Unable to update helpdesk custom email. #{email.errors.full_messages.to_sentence}"
          redirect_to admin_helpdesk_custom_emails_path
        end
      else
        flash[:error] = "Email verification failed: The email address is not verified on AWS"
        redirect_to admin_helpdesk_custom_emails_path
      end
    end

    private

    def email
      @email ||= HelpdeskCustomEmail.find(params[:id])
    end

    def scoped_collection
      super.where.not(email: [nil, ''])
    end

    def email_params
      params.require(:helpdesk_custom_email).permit(:email, :verified, :company_id)
    end
  end
end
