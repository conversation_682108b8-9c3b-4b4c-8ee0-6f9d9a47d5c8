ActiveAdmin.register Prompt, as: 'ticket_summary_prompts' do
  menu parent: 'Manage Services', priority: 7
  breadcrumb do
    ['admin', 'manage services']
  end

  actions :all
  config.batch_actions = false

  index do
    column :name
    column('Status') { |option| option.is_active ? 'Enabled' : 'Disabled' }
    column :model
    column :prompts_template
    column :created_at
    column :updated_at
    actions defaults: false do |prompt|
      item 'View', admin_ticket_summary_prompt_path(prompt), class: 'member_link'
      item 'Edit', edit_admin_ticket_summary_prompt_path(prompt), class: 'member_link'
      item 'Delete', admin_ticket_summary_prompt_path(prompt),
           method: :delete,
           data: { confirm: 'Are you sure you want to delete this prompt?' },
           class: 'member_link delete_link'
    end
  end

  form do |f|
    f.inputs 'Prompt Details' do
      f.input :name
      f.input :model, as: :select, collection: ['gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano']
      f.input :is_active, label: 'Status', as: :boolean, input_html: { class: 'toggle-status' }
      f.input :prompts_template, as: :select, collection: PromptsTemplate.all.pluck(:name, :id)
      f.input :response_format, input_html: { class: 'handle-textarea-height' }
      f.input :fields_description, input_html: { class: 'handle-textarea-height' }
      f.input :example_output, input_html: { class: 'handle-textarea-height' }
      f.input :additional_instructions, input_html: { class: 'handle-textarea-height' }
    end
    f.actions
  end

  controller do
    def destroy
      prompt = Prompt.find(params[:id])
      if prompt.destroy
        redirect_to admin_ticket_summary_prompts_path, notice: "Prompt was successfully deleted."
      else
        redirect_to admin_ticket_summary_prompt_path(prompt), alert: prompt.errors.full_messages.to_sentence
      end
    end

    def permitted_params
      params.permit(prompt: [
        :name,
        :model,
        :is_active,
        :response_format,
        :fields_description,
        :example_output,
        :prompts_template_id,
        :additional_instructions
      ])
    end
  end
end
