ActiveAdmin.register EmailDomain, as: "Email Domain" do
  menu parent: "Miscellaneous", priority: 1
  breadcrumb do
    ['admin', 'miscellaneous']
  end

  permit_params :domain, :status
  filter :domain_contains, :as => :string
  filter :status, :as => :select, :collection => EmailDomain.statuses.map { |status| [status[0].titleize, status[1]] }

  index do
    column :domain
    column :status
    actions
  end

  controller do
    include MultiCompany::GeneralScoping
    
    before_action :verify_super_admin_mfa
  end
end
