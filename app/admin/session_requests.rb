ActiveAdmin.register ServiceOption, as: 'session_requests' do
  menu parent: 'Manage Services', priority: 2
  breadcrumb do
    ['admin', 'manage services']
  end

  filter :service_name
  filter :status, as: :select, collection: [['Enabled', true], ['Disabled', false]]

  actions :all, except: [:new, :destroy]
  config.batch_actions = false

  index do
    column :service_name
    column('Status') { |option| option.status ? 'Enabled' : 'Disabled' }
    column :created_at
    column :updated_at
    actions defaults: false do |service_option|
      item 'View', admin_session_request_path(service_option), class: 'member_link'
      item 'Edit', edit_admin_session_request_path(service_option), class: 'member_link'
    end
  end

  show do
    attributes_table do
      row ('service name') { |option| option.service_name }
      row ('type') { |option| option.service_type }
      row ('status') { |option| option.status ? 'Enabled' : 'Disabled' }
      row ('created_at') { |option| option.created_at }
      row ('updated_at') { |option| option.updated_at }
    end
  end

  form do |f|
    f.inputs do
      f.input :service_name, input_html: { disabled: true }
      f.input :service_type, as: :select, input_html: { disabled: true }
      f.input :status
    end
    f.actions
  end

  controller do
    def permitted_params
      params.permit(service_option: [:status])
    end

    def scoped_collection
      super.where(service_type: 'session_request')
    end
  end
end
