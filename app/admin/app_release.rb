ActiveAdmin.register AppRelease do
  menu parent: 'App', priority: 9, label: 'Desktop App Releases'
  breadcrumb do
    ['admin', 'apps']
  end

  filter :app_type, as: :select, collection: AppRelease.app_types.keys
  filter :app_name, as: :select, collection: -> { AppRelease.distinct.pluck(:app_name).compact }
  filter :version, as: :select, collection: -> { AppRelease.distinct.pluck(:version).compact }
  filter :build_version, as: :select, collection: -> { AppRelease.distinct.pluck(:build_version).compact }
  filter :is_admin_app
  filter :is_package_build
  permit_params :app_name, :app_enabled, :app_type, :version, :description, :is_package_build, :is_admin_app, :installer_link, :build_version

  form do |f|
    f.inputs 'Details' do
      f.input :app_name
      f.input :app_enabled
      f.input :app_type
      f.input :version
      f.input :description
      f.input :is_package_build
      f.input :is_admin_app
      f.input :installer_link
      f.input :build_version
    end
    f.actions
  end

  index do
    id_column
    column :app_name
    column :app_enabled
    column :app_type
    column :version
    column :description
    column :is_package_build
    column :is_admin_app
    column :installer_link
    column :build_version
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
