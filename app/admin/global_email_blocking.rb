ActiveAdmin.register Company, as: "Global Email Blocking" do
  menu parent: 'Manage Services', label: "Global Email Blocking", priority: 5
  breadcrumb do
    ['admin', 'global email blocking']
  end

  actions :index, :update
  filter :name, as: :searchable_select, label: "Search by Company"

  controller do
    def blocking_record
      @blocking_record ||= GlobalEmailBlocking.first_or_create
    end
    helper_method :blocking_record

    def update
      company = Company.find(params[:id])
      if params[:revoke].present?
        block_access(company)
        block_email(company)
      else
        block_email(company)
      end
    end

    def block_access(company)
      company.subscriptions.each do |sub|
        sub.update(status: "insolvent")
      end
      company.update_column(:free_trial_days, -1)
      key = { name: "check_free_trial_days", company_id: company.id }
      if Rails.cache.exist?(key)
        Rails.cache.delete(key)
      end
    end

    def block_email(company)
      blocking_record.with_lock do
        if blocking_record.company_ids.include?(company.id) && params[:revoke].nil?
          blocking_record.update!(company_ids: blocking_record.company_ids - [company.id])
          flash[:notice] = "#{company.name} unblocked successfully"
        else
          blocking_record.update!(company_ids: (blocking_record.company_ids + [company.id]).uniq)
          flash[:notice] = "#{company.name} blocked successfully"
        end
      end
      redirect_to admin_global_email_blockings_path
    end
  end

  index title: "Global Email Blockings" do
    column "Company Name", :name

    column "Block Status" do |company|
      if blocking_record.company_ids.include?(company.id)
        status_tag "BLOCKED", class: "red"
      else
        status_tag "ALLOWED", class: "green"
      end
    end

    column "Actions" do |company|
      links = ''.html_safe
      if blocking_record.company_ids.include?(company.id)
        links += link_to "UNBLOCK EMAILS", admin_global_email_blocking_path(company), class: "button button--danger", method: :put, remote: true, data: { confirm: "Unblock #{company.name}?" }
      else
        links += link_to "BLOCK EMAILS", admin_global_email_blocking_path(company), class: "button button--success", method: :put, remote: true, data: { confirm: "Block #{company.name}?" }
      end
      links += link_to "REVOKE ACCESS", admin_global_email_blocking_path(company, revoke: true), class: "button button--danger", style: "margin-left: 10px;",  method: :put, remote: true, data: { confirm: "Revoke #{company.name}?" }
      links
    end
  end
end
