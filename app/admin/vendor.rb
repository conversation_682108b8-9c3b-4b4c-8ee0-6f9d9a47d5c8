ActiveAdmin.register Vendor do
  menu parent: 'Modules Data', label: "Vendor", priority: 1
  breadcrumb do
    ['admin', 'modules data']
  end
  filter :name
  permit_params :id, :name, :vendor_source_type, :department, :phone_number, :company_id, :address1, :city, :state, :zip, :url, :email, contacts_attributes: [
        :first_name,
        :last_name,
        :phone_number,
        :department,
        :email
      ]

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :department
      f.input :phone_number
      f.input :address1
      f.input :city
      f.input :state
      f.input :zip
      f.input :url
      f.input :email
    end

    if f.object.contacts.blank?
      vendors = f.object.contacts.build
    end

    f.has_many :contacts do |c_vendor|
      c_vendor.inputs "Vendor Contacts" do
        c_vendor.input :first_name
        c_vendor.input :last_name
        c_vendor.input :phone_number
        c_vendor.input :department
        c_vendor.input :email
      end
    end
    f.actions
  end

  index do
    column :name
    column :url
    actions
  end

  show do
    panel "Vendor Details" do
      attributes_table_for vendor do
        row :id
        row :name do
          vendor.name.try(:titleize)
        end
        row :vendor_source_type do
          vendor.vendor_source_type.try(:titleize)
        end
        row :department
        row :phone_number
        row :company do
          vendor.company.try(:name)
        end
        row :address1
        row :city
        row :state
        row :zip
        row :url
        row :email
      end
    end

    panel "Vendor Contacts" do
      table_for vendor.vendor_contacts do
        column :first_name
        column :last_name
        column :phone_number
        column :department
        column :email
      end
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def scoped_collection
      # Vendor.where(vendor_type: '1')
      Vendor.unscoped
    end

    def create
      @vendor = Vendor.new(vendor_params)
      if @vendor.save
        flash[:success] = "Vendor information is added successfully."
      else
        flash[:error] = "Unable to add vendor information. #{@vendor.errors.full_messages.to_sentence}"
      end
      redirect_back(fallback_location: root_path)
    end

    def update
      @vendor = Vendor.find_by id: params[:id]
      if @vendor.present?
        if @vendor.update(vendor_params)
          flash[:success] = "Vendor has been updated"
          redirect_to admin_vendors_path
        else
          flash[:error] = "Vendor has not updated. It has some invalid values.\n#{@vendor.errors.full_messages.to_sentence}"
          redirect_back(fallback_location: root_path)
        end
      else
        flash[:error] = "Vendor didn't updated"
        redirect_back(fallback_location: root_path)
      end
    end

    def destroy
      begin
        @vendor = Vendor.unscoped.where(id: params[:id]).first
        if @vendor.try(:destroy!)
          flash[:success] = "Vendor deleted successfully"
          redirect_to admin_vendors_path
        else
          flash[:error] = "Vendor deletion failed"
          redirect_to admin_vendors_path
        end
      rescue
        flash[:error] = "Vendor deletion failed"
        redirect_to admin_vendors_path
      end
    end

    def vendor_params
    params.require(:vendor).permit(
      :id,
      :name,
      :department,
      :phone_number,
      :address1,
      :city,
      :state,
      :zip,
      :url,
      :email,
      :phone_number_country_code,
      :phone_number_country_code_number,
      contacts_attributes: [
        :vendor_id,
        :id,
        :first_name,
        :last_name,
        :phone_number,
        :department,
        :email,
        :_destroy
      ]
    )
    end
  end
end
