<template>
  <div :class="!fullWidth ? 'col-6 col-lg-4 mb-4 col-xxl-wide-theme-3' : 'w-100'">
    <div
      v-if="disableSelection !== true"
      class="checkholder"
    >
      <label
        class="clickable mb-0"
        @click.stop.prevent="selectAsset"
      >
        <input
          :checked="isSelected"
          type="checkbox"
        >
        <i
          class="nulodgicon-checkmark checkbox"
          :data-tc-checkbox="asset.name"
        />
      </label>
    </div>
    <div
      v-if="newWindow"
      class="external-link-hover"
    >
      <i class="nulodgicon-external-link ml-2" />
    </div>
    <a
      :href="`${asset.id}`"
      class="box box--with-hover asset-link align-content-start"
      :class="{ 'box--selected': isSelected }"
      :data-tc-asset-box-link="asset.name"
      @click.stop.prevent="routeOrSelect"
    >
      <div class="box__inner">
        <span
          v-if="
            asset.sources &&
              asset.sources.length == 1 &&
              selectedItemsLookUp['source']
          "
          class="box__source text-muted"
        >
          {{ sourceName(asset.sources[0]) }}
        </span>
        <span
          v-if="asset.archived && selectedItemsLookUp['archived_tag']"
          class="badge badge--archived font-weight-semi-bold"
          data-tc-archived-badge
        >
          Archived
        </span>
        <span
          v-else-if="
            asset.sources &&
              asset.sources.length > 1 &&
              selectedItemsLookUp['source']
          "
          v-tooltip="assetSources"
          class="box__source text-muted"
        >
          Multiple sources
        </span>
        <span
          v-else-if="selectedItemsLookUp['updated_at']"
          v-tooltip="`Updated at ${updatedAt}`"
          class="box__source left_box text-muted"
        >
          {{ updatedAt }}
        </span>

        <div class="row">
          <div class="col-auto">
            <icon-badge
              v-if="imageSrc"
              :tooltip-message="asset.assetType"
              :img-src="imageSrc"
              :img-height-pixels="52"
              :img-padding-pixels="6"
              :background-color-class="`bg-themed-icon-badge-bg mt-n2 ml-n2`"
            />
          </div>
          <div class="col">
            <h6 class="my-0 asset-name">
              <span
                v-if="assetNameLength(asset)"
                data-tc-asset-name
                :data-tc-view-name="asset.name"
              >
                {{ asset.name }}
              </span>
              <span
                v-else-if="asset.name"
                v-tooltip="asset.name"
              >
                {{ truncate(asset.name, 28) }}
              </span>
              <span
                v-else
                data-tc-asset-name
              >No name present</span>
              <a
                v-if="showTicketBug(asset)"
                v-tooltip="helpTicketTooltip(asset)"
                class="ml-1 asset-item__ticket-warning"
                :href="`/help_tickets?managed_asset_id=${asset.id}&managed_asset_name=${asset.name}`"
                target="_blank"
                @click.stop
              >
                <i class="nulodgicon-bug small position-absolute" />
              </a>
            </h6>
            <p
              v-if="asset.fullname"
              class="mt-1 small text-muted mb-0"
            >
              <span
                class="mr-2"
                data-tc-view="asset fullname"
              >
                {{ asset.fullname }}
              </span>
            </p>
            <p class="mt-1 small text-muted mb-0">
              <span
                class="mr-2"
                data-tc-view="asset type"
              >
                <span :class="`assettype--${toSnakecase(asset.assetType)}`">&bull;</span>
                {{ asset.assetType }}
              </span>
            </p>
            <warranty-item
              v-if="selectedItemsLookUp['warranty_expiration']"
              class="small text-muted"
              :asset="asset"
            />
            <span v-if="selectedItemsLookUp['tags']">
              <tag
                v-for="tag in asset.tags"
                :key="tag.id"
                class="px-2 mb-0 mr-1 font-weight-bold mb-1"
                :data-tc-view-tag="tag.name"
              >
                {{ tag.name }}
              </tag>
            </span>
          </div>
        </div>
      </div>
    </a>
  </div>
</template>

<script>
import { mapMutations } from "vuex";
import dates from "mixins/dates";
import strings from "mixins/string";
import assetImages from "mixins/asset_images";
import permissionsHelper from "mixins/permissions_helper";
import assetHelper from "mixins/assets/asset_helper";
import IconBadge from "components/shared/icon_badge.vue";
import Tag from "../shared/tag.vue";
import WarrantyItem from "./warranty_item.vue";

export default {
  components: {
    Tag,
    WarrantyItem,
    IconBadge,
  },
  mixins: [dates, strings, assetImages, assetHelper, permissionsHelper],
  props: {
    asset: {
      type: Object,
      required: true,
    },
    selectedAssetIdsArr: {
      type: Array,
      required: false,
      default: () => [],
    },
    disableSelection: {
      type: Boolean,
      required: false,
      default: false,
    },
    fullWidth: {
      type: Boolean,
      required: false,
      default: false,
    },
    newWindow: {
      type: Boolean,
      required: false,
      default: false,
    },
    isBulkSelected: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isSelected() {
      return this.selectedAssetIdsArr?.includes(this.asset.id);
    },
    imageSrc() {
      return this.asset.imageThumbUrl
        ? this.asset.imageThumbUrl
        : this.assetTypeImageSrc(this.asset);
    },
    assetSources() {
      return this.asset.sources
        .map((source) => this.toTitle(source))
        .join(", ");
    },
  },
  methods: {
    ...mapMutations(["setCurrentAsset", "updateAsset"]),
    onWorkspaceChange() {
      if (this.fullWidth && this.newWindow) {
        this.$store.dispatch("GlobalStore/fetchAssetSelectedDataCall");
      }
    },
    helpTicketTooltip(asset) {
      let label = "tickets";
      if (asset.helpTickets) {
        if (asset.helpTickets.length === 1) {
          label = "ticket";
        }
        return `${asset.helpTickets.length} help ${label}`;
      }
      return null;
    },
    routeOrSelect(e) {
      if (this.isBulkSelected) {
        this.selectAsset();
      } else {
        const url = `/managed_assets/${this.asset.id}`;
        if (e.shiftKey && !this.disableSelection) {
          this.selectAsset();
        } else if (this.asset.companyId !== getCompanyFromStorage().id) {
          this.$emit("child-asset-opener", this.asset);
        } else if (e.ctrlKey || e.metaKey || this.newWindow) {
          window.open(url, "_blank");
        } else {
          this.$router.push({ path: `/${this.asset.id}` });
        }
      }
    },
    selectAsset() {
      this.$emit("select-asset", this.asset.id);
    },
    showTicketBug(asset) {
      if (asset.helpTickets) {
        return asset.helpTickets.length > 0;
      }
      return false;
    },
    sourceName(source) {
      if (source === "selfonboarding") {
        return "Self-Onboarding";
      }
      return this.toTitle(source);
    },
    assetNameLength(asset) {
      return asset.name ? asset.name.length <= 28 : false;
    },
  },
};
</script>

<style scoped lang="scss">
[type="checkbox"] {
  display: none;
}

.asset-name {
  max-width: 230px;
}

.asset-image {
  display: block;
  max-width: 96px;
  max-height: 75px;
  width: auto;
  height: auto;
}

.asset-item:hover {
  .external-link-hover {
    display: block !important;
  }
}

.badge--archived {
  display: inline-block;
  background-color: color.adjust(red, $alpha: -0.75);
  padding: 2px 8px;
  border-radius: 0 $border-radius 0 calc($border-radius / 2);
  position: absolute;
  right: 0;
  top: 0;
}

.checkholder {
  position: absolute;
  top: 1rem;
  right: 2rem;
  z-index: map-get($zIndex, "above");
}

:checked~.checkbox {
  background-color: $themed-link;
  border-color: $themed-link;
}

.external-link-hover {
  color: $primary;
  display: none;
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: map-get($zIndex, "above");
}
</style>
