# This controller receives import requests for contracts, inheriting from the base ImportXlsController.
class Imports::ContractsXlsController < ImportXlsController
  def import_class
    ImportExport::ContractImport
  end

  def import_module
    "contracts"
  end

  def options
    {
      contract_type: params['term'] == 'fixed_term' ? 'fixed_term_contract' : 'open_ended_contract',
      record_type: params['term']
    }
  end
end
