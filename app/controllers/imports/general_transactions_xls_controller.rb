# This controller receives import requests for general transactions, inheriting from the base ImportXlsController.
class Imports::GeneralTransactionsXlsController < ImportXlsController
  def import_class
    ImportExport::GeneralTransactionImport
  end

  def import_module
    "general_transactions"
  end

  def options
    {
      transaction_type: params['term'] == 'non_recurring' ? 'non_recurring_transaction' : 'recurring_transaction',
      record_type: params['term']
    }
  end
end
