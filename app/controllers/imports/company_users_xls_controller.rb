# This controller receives import requests for company users, inheriting from the base ImportXlsController.
class Imports::CompanyUsersXlsController < ImportXlsController
  set_privilege_name("CompanyUser")

  def import_class
    ImportExport::CompanyModuleImport
  end

  def import_module
    "company_users"
  end

  def options
    user_options = {
      company_module: "company_user",
      class: "CompanyUser",
    }
  end
end
