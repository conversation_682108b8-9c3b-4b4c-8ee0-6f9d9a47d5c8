class DiscoveredAssetsController < AuthenticatedController
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include DiscoveredAssetLogs

  before_action :authorize_read, only: [:index, :show, :admin_assets, :discovered_assets]
  before_action :authorize_write, except: [:index, :show, :admin_assets, :discovered_assets]
  before_action :set_resource, only: [:show]
  after_action :create_asset_history, except: [:index, :show, :admin_assets, :discovered_assets]

  def index
    respond_to do |format|
      format.html
      format.json do
        json_response = {
          discovered_assets: discovered_assets,
          filtered_assets_count: @filtered_assets.count,
          page_count: @page_count,
          channel_key: "#{current_company.id}assets"
        }
        json_response[:filtered_assets] = @filtered_assets if params[:include_filtered_assets].present?
  
        render json: json_response
      end
    end
  end

  def show
    respond_to do |format|
      format.html {  }
      format.json { render json: @discovered_asset }
    end
  end

  def import
    if params[:asset].present?
      assets_service = BulkDiscoveredAssetsUpdate.new(params[:asset], current_company, scoped_company_user).import_assets
      render json: {}, status: :ok
    else
      render json: {}, status: :bad_request
    end
    Pusher.trigger("#{current_company.id}assets",'discovered-assets', {})
  end

  def bulk_archive
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "archive", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Ignoring all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).archive_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to archive." }, status: :bad_request
      end
    end
  end

  def bulk_delete
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "delete", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Deleting all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).delete_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to delete." }, status: :bad_request
      end
    end
  end

  def bulk_unarchive
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "unarchive", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Unignoring all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).unarchive_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to archive." }, status: :bad_request
      end
    end
  end

  def import_bulk
    assets = params[:discovered_assets]
    if assets.present?
      if select_all
        BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "import", assets.as_json, params[:update_all_fields].as_json, params[:source].as_json, params[:search].as_json)
        render json: { message: 'Importing all the assets, this might take a while' }
      else
        duplicate_asset_ids = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).import_assets
        render json: { duplicate_asset_ids: duplicate_asset_ids }, status: :ok
      end
    else
      render json: {}, status: :bad_request
    end
  end

  def admin_assets
    assets = current_company.discovered_assets
    assets = assets.where(source: params[:source]) if params[:source].present?
    assets = assets.search_text(params[:search]) if params[:search].present?
    assets = assets.order(updated_at: :desc)

    @discovered_asset_count = assets.count
    @page_count = (assets.length / params[:page_size].to_f).ceil
    assets = assets.paginate(page: params[:page], per_page: params[:page_size]&.to_f)

    assets.map { |da| da.as_json.merge(
      asset_type: (da.asset_type.blank? ? 'Other' : da.asset_type))
    }

    render json: { discovered_assets: assets, discovered_asset_count: @discovered_asset_count, page_count: @page_count}
  end

  def discovered_assets
    assets = current_company.discovered_assets
    assets = assets.where(source: params[:source]) if params[:source].present?
    if params[:asset_type].present?
      if params[:asset_type] == "Other"
        assets = assets.where("asset_type = ? OR asset_type IS NULL OR asset_type = ?", "Other", "")
      else
        assets = assets.where(asset_type: params[:asset_type])
      end
    end

    if params[:location_id].present?
      assets = assets.includes(:integration_location)
      .where(:integrations_locations => {location_id: params[:location_id]})
    end
    assets = assets.search_text(params[:search]) if params[:search].present?

    if params[:active_order].present?
      active_order = JSON.parse(params[:active_order])
      sort_type = active_order["activeSort"].underscore
      if sort_type == "integrations_locations_id"
        order_condition = "integrations_locations.description #{active_order['activeSortDirection']}, id desc"
        assets = assets.where(status: status).left_joins(:integration_location).order(order_condition)
      else
        sort_type = "source" if sort_type == "asset_sources"
        sort_type = "display_name" if sort_type == "name"
        sort_type = "COALESCE(last_synced_at, updated_at)" if sort_type == 'last_synced_at'
        assets = assets.where(status: status).order(Arel.sql("#{sort_type} #{active_order["activeSortDirection"].underscore.to_sym}, id desc")) if assets.any?
      end
    else
      assets = assets.where(status: status).order(discovered_asset_type: :asc, id: :desc)
    end

    @filtered_assets = assets
    paginated_assets = assets.offset((page - 1) * page_size).limit(page_size)

    if params[:select_all_assets] && params[:offset]
      paginated_assets = assets.offset(params[:offset]).limit(params[:per_page])
    else
      @page_count = (assets.size / params[:page_size].to_f).ceil
    end
    
    paginated_assets = paginated_assets.includes(:asset_softwares, :asset_sources, :discovered_assets_hardware_detail, :cloud_asset_attributes, :integration_location)

    paginated_assets.map do |da|
      used_by_contributor = get_contributor(da.used_by)

      da.as_json.merge(
        location_description: da.integration_location&.description,
        location_id: da.integration_location&.location_id,
        asset_type: (da.asset_type.blank? ? 'Other' : da.asset_type),
        used_by_contributor_id: used_by_contributor&.id,
        used_by_contributor: used_by_contributor,
        managed_by_contributor_id: get_contributor(da.managed_by),
        department: nil,
        impact: nil)
    end
  end

  def select_all
    params[:select_all]
  end

  def status
    params[:status]
  end

  def page
    (params[:page] || 1).to_i
  end

  def page_size
    (params[:page_size] || 25).to_i
  end

  def asset_count_by_type  
    render json: { asset_counts: asset_counts }
  end

  private
  def set_resource
    @discovered_asset = current_company.discovered_assets.find(params[:id])
  rescue ActiveRecord::RecordNotFound => e
    respond_to do |format|
      format.json { render json: { message: "Asset was not found." }, status: :not_found }
      format.html { render 'shared/not_found' }
    end
  end

  def get_contributor(owner_detail)
    if owner_detail.present?
      return Contributor.where(company_id: current_company.id).find_users_by_email(owner_detail).first
    end
  end

  def create_asset_history
    create_user_event_asset_log(params)
  end

  #add the name of the integrations which have the client's info to show
  def client_disc_asset_type_connectors?
    ["meraki", "ubiquiti"].include?(params[:source])
  end
  
  def asset_counts
    group_fields = [:asset_type]
    group_fields << :discovered_asset_type if client_disc_asset_type_connectors?
    group_fields.reverse!
    grouped_counts = current_company.discovered_assets
                                     .where(source: params[:source])
                                     .group(*group_fields)
                                     .count
    return grouped_counts unless client_disc_asset_type_connectors?
  
    asset_counts = {}
    grouped_counts.each do |(discovered_asset_type, asset_type), count|
      key = %w[device sm_device].include?(discovered_asset_type) ? "device" : discovered_asset_type
      asset_counts[key] ||= {}
      asset_counts[key][asset_type || "Other"] ||= 0
      asset_counts[key][asset_type || "Other"] += count
    end
    asset_counts
  end
end
