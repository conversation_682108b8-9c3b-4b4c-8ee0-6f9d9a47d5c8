module FilterWorkspaces
  extend ActiveSupport::Concern

  def filter_workspace(workspace, scoped_company_user)
    workspace.privileges.any? do |priv|
      contributor = priv.contributor
      if contributor.contributor_type == "Group"
        return true if contributor.group.name == 'Everyone'
        contributor.group.contributors.joins(:company_user)
          .pluck('company_users.id')
          .include?(scoped_company_user.id)
      else
        contributor.company_user.id == scoped_company_user.id
      end
    end
  end
end
