module ValidateCacheKeyRequirements
  
  def validate_cache_key_requirements(is_help_desk_module = false)
    if is_help_desk_module
      raise 'Workspace is not present for cache key.' if scoped_workspace.blank?
    elsif scoped_company.default_workspace.blank?
      raise 'Workspace is not present for cache key.'
    end

    if scoped_company.blank?
      raise 'Scoped company is not present for cache key.'
    elsif params[:action].blank? || params[:controller].blank?
      raise 'Action/Controller information is missing in parameters for cache key.'
    end

    true
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
    false
  end
end
