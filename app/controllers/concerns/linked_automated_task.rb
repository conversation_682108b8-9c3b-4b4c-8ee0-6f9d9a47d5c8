module LinkedAutomatedTask
  extend ActiveSupport::Concern

  def check_linked_automated_tasks(custom_form)
    event_details_values = AutomatedTasks::EventDetail.joins(task_event: :automated_task)
                                                     .where(automated_tasks_automated_tasks: { workspace_id: custom_form.workspace_id })
                                                     .where.not(automated_tasks_event_details: { value: nil })
                                                     .pluck(:value)

    linked_forms = event_details_values.flat_map { |value| JSON.parse(value)['custom_forms'] }
                                      .compact
                                      .select { |form| form['id'] == custom_form.id }

    linked_forms.present?
  end
end
