module Integrations::Helpdesk::Slack::HelpTickets
  extend ActiveSupport::Concern
  include MultiCompany::HelpTicketScoping

  private

  def create_ticket
    initiate_slack_ticket_creation

    log_event('Slack ticket creation initiated', 'create_ticket', :success)
    render json: {}, status: 200
  end

  def edit_ticket
    slack_service.open_view('edit_ticket', ticket_json, current_message_body)

    log_event('Opened edit ticket form', 'edit_ticket', :success)
    render json: {}, status: 200
  end

  def update_ticket
    if help_ticket.blank?
      log_event('Ticket not found', 'update_ticket', :success)
      return help_ticket_not_found
    end

    log_event('Slack ticket updation initiated', 'update_ticket', :success)
    initiate_slack_ticket_updation

    render json: {}, status: 200
  end

  def send_slack_text_notification(text)
    SlackTextNotificationWorker.perform_async(@slack_config.id, text)
  end

  def initiate_slack_ticket_creation
    CreateSlackTicketWorker.perform_async(@slack_config.id, payload.as_json)
  end

  def initiate_slack_ticket_updation
    UpdateSlackTicketWorker.perform_async(@slack_config.id, help_ticket.id, payload.as_json, thread_id)   
  end

  def ticket_json
    @ticket_json ||= begin
      json = {}
      help_ticket.custom_form_fields
        .select{ |field| HelpTicket::SLACK_FIELDS.include?(field.name) }
        .map do |field|
          cfv = field.custom_form_values.where(module_id: help_ticket.id, module_type: 'HelpTicket')
          if field.name == 'assigned_to'
            contributor_ids = cfv.pluck(:value_int)
            contributors = Contributor.includes(:group, company_user: :user).where(id: contributor_ids)
            if contributors.present?
              json["selected_#{field.name}"] = contributors.map { |c| { id: c.id, name: c.name }}
            end
          elsif field.name == 'description'
            description = cfv.pluck(:value_str).to_sentence.gsub('<div><!--block-->', '').gsub('</div>', '')
            json[field.name] = HtmlToMarkdownConverter.new(parse_type: 'slack', content: description).call
          elsif field.name != 'created_by'
            json[field.name] = cfv.pluck(:value_str).to_sentence
          end
        end
      json[:ticket_id] = help_ticket.id
      json[:trigger_id] = payload['trigger_id']
      json[:channel_id] = payload['channel']['id']
      json[:thread_ts] = payload['container']['message_ts']
      json[:is_dm_request] = is_dm_request?
      json
    end
  end

  def help_ticket
    return nil if request_type == 'shortcut'
    @help_ticket ||= begin
      if private_metadata && private_metadata['help_ticket_id']
        @help_ticket = HelpTicket.find_by_id(private_metadata['help_ticket_id'])
      end
      
      @help_ticket ||= HelpTicket.find_by('? = ANY (slack_message_ids)', thread_id) if thread_id
      
      if @help_ticket.blank? && payload['actions']
        value = JSON.parse(payload['actions'].first['value'])
        value = value['ticket_id'] if value.is_a?(Hash)
        @help_ticket = HelpTicket.find_by_id(value)
      end
      
      if @help_ticket.blank? && payload['view']
        @help_ticket = HelpTicket.find_by_id(JSON.parse(payload['view']['private_metadata'])['help_ticket_id'].to_i)
      end
      @help_ticket
    end
  end

  def current_message_body
    blocks = payload["message"]["blocks"] if payload["message"].present?
    current_message_blocks = []
    blocks&.each do |blk|
      break if blk['type'] == 'divider'
      current_message_blocks << blk
    end
    current_message_blocks
  end
end
