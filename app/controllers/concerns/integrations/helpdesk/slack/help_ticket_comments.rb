module Integrations::Helpdesk::Slack::HelpTicketComments
  extend ActiveSupport::Concern

  private

  def new_comment
    slack_service.open_view('new_comment', new_comment_data)

    log_event('Open view for new comment', 'new_comment', :success)
    head :ok
  end

  def new_private_comment
    log_event('Open view for new private comment', 'new_private_comment', :success)
    slack_service.open_view('new_private_comment', new_comment_data)

    head :ok
  end

  def create_new_channel
    initiate_create_new_channel_worker
  end

  def create_help_ticket_comment
    add_comment
  end

  def new_comment_data
    { 
      ticket_id: help_ticket.id,
      trigger_id: payload['trigger_id'],
      channel_id: payload['channel']['id'],
      thread_ts: payload['container']['message_ts'],
      is_dm_request: is_dm_request?,
    }
  end

  def add_comment
    payload['private_comment'] = false
    initiate_create_comment_worker

    log_event('Initiated create comment worker', 'add_comment', :success, { thread_id: thread_id })
    head :ok
  end

  def add_private_comment
    payload['private_comment'] = true
    initiate_create_comment_worker

    head :ok
  end

  def initiate_create_comment_worker
      CreateSlackTicketCommentWorker.perform_async(@slack_config.id, thread_id, false, nil, nil, help_ticket.id, payload.as_json)
  end

  def initiate_create_new_channel_worker
    CreateNewSlackChannelWorker.perform_async(@slack_config.id, help_ticket.id, payload.as_json, thread_id)
  end

  def send_slack_text_notification(text)
    SlackTextNotificationWorker.perform_async(@slack_config.id, text)
  end
end
