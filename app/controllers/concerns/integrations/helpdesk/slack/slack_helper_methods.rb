module Integrations::Helpdesk::Slack::SlackHelperMethods
  extend ActiveSupport::Concern
  COLOR_MAP = {
    "blue" => "#26a8ff",
    "yellow" => "#ffb648",
    "red" => "#e14144"
  }.freeze

  private

  def slack_client
    @slack_client ||= Integrations::Slack::Client.new(config)
  end

  def comment_params(is_generated_channel)
    {
      source: :slack,
      comment_body: "<div><!--block-->#{message_body}</div>",
      email: user_email,
      private_flag: is_generated_channel ? false : private_comment,
      private_contributor_ids: is_generated_channel ?  [] :  assigned_users,
      contributor_id: user&.contributor_id,
    }
  end

  def description(ticket)
    value = ticket_description(ticket)
    return unless value

    value = value.gsub('<div><!--block-->', '').gsub('</div>', '')
    HtmlToMarkdownConverter.new(parse_type: 'slack', content: value).call
  end

  def error_event_params(error, detail, api_type)
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: config.id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: '422',
      created_at: DateTime.now
    }
  end

  def user_email
    @user_email ||= slack_client.get_user_email(user_id || payload['user']['id'])
  end

  def user
    @user ||= begin
      user = User.find_by_cache(email: user_email)
      cu = CompanyUser.find_by_cache(user_id: user.id, company_id: @ticket.company_id) if user.present?
      cu || Guest.find_by(email: user_email, company_id: @ticket.company_id)
    end
  end

  def slack_feature_access_pending?(company_id)
    @feature_access_pending ||= begin
     Rails.application.credentials.slack_approval_pending && !allowed_company_ids.include?(company_id)
    end
  end
    
  def allowed_company_ids
    @allowed_company_ids ||= Rails.application.credentials.allowed_company_ids.to_s.split(',').map(&:to_i)
  end

  def attachments(blocks, color = "yellow")
    [
      {
        "color": COLOR_MAP[color.downcase],
        "blocks": blocks
      }
    ]
  end
end

