module DiscoveryToolsLogs
  extend ActiveSupport::Concern

  def create_asset_connector_log(action, connector_name, data, status, company_user_id, company_id)
    AssetConnectorLog.create!(
      action: action,
      connector_name: connector_name,
      data: data,
      status: status,
      company_user_id: company_user_id,
      company_id: company_id
    )
  end

  def element_data(key, prev_data, current_data)
    return {
      key: key,
      prev_data: prev_data,
      current_data: current_data,
    }
  end

  def mask_value(value)
    return nil if value.blank?
    visible_part = value[-4..]
    asterisks = '*' * (value.length - 4)
    asterisks + visible_part
  end
end
