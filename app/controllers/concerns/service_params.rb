module ServiceParams

  def resource_params
    params.require(:telecom_service).permit(
      permit_fields.map do |field|
        field.is_a?(Hash) ? field : field.to_sym
      end
    )
  end

  def permit_fields
    [
      :name,
      :telecom_provider_id,
      :product_id,
      :service_type,
      :category_id,
      :account_number,
      :pin,
      :contract_id,
      :monthly_cost,
      :auto_monthly_cost,
      :description,
      :all_locations,
      :connection,
      :circuit_id,
      :priority,
      :network_speed,
      :equipment,
      :number_of_lines,
      :product_id,
      :subnet,
      :gateway,
      :dns1,
      :dns2,
      location_ids: [],
      subtypes: []
    ]
  end

end