module CustomFieldActivityHelper
  extend ActiveSupport::Concern

  def create_activity
    if module_name == "HelpTicket"
      if @new_value&.module&.respond_to?(:create_activity)
        @new_value.module.create_activity(activity_params, current_value, previous_value)
      elsif @previous_value&.module&.respond_to?(:create_activity)
        @previous_value.module.create_activity(activity_params, current_value, previous_value)
      end
    elsif module_name == "CompanyUser"
      custom_entity.activities.create(activity_params)
    end
  end

  def activity_type
    if module_name == "HelpTicket"
      activity_field_type
    elsif module_name == "CompanyUser"
      if current_value.present? && previous_value.present?
        activity_type = CompanyUserActivity.activity_types["updated"]
      elsif current_value.present?
        activity_type = CompanyUserActivity.activity_types["added"]
      else
        activity_type = CompanyUserActivity.activity_types["removed"]
      end
    end
  end

  def current_value
    @new_value&.value.presence
  end

  def previous_value
    @previous_value&.value.presence
  end

  def activity_label
    @new_value&.custom_form_field&.label.presence ||
    @previous_value&.custom_form_field&.label.presence
  end

  def activity_field_type
    @new_value&.custom_form_field&.field_attribute_type.presence ||
    @previous_value&.custom_form_field&.field_attribute_type.presence
  end
end
