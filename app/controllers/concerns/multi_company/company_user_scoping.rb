module MultiCompany
  module CompanyUserScoping
    extend ActiveSupport::Concern
    include GeneralScoping

    def scoped_resource
      @company_user ||= CompanyUser.find_by(id: company_user_id, company_id: current_user.companies.pluck(:id))
    rescue ActiveRecord::RecordNotFound => e
      respond_to do |format|
        format.json { render json: { message: "Company User was not found." }, status: 404 }
        format.html { render 'shared/not_found' }
      end
    end

    def company_user_id
      params[:company_user_id]
    end
  end
end
