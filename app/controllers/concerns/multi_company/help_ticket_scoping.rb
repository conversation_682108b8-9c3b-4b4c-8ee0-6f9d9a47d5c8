module MultiCompany
  module HelpTicketScoping
    extend ActiveSupport::Concern
    include MultiCompany::GeneralScoping

    def help_ticket
      @help_ticket ||= find_help_ticket
    rescue ActiveRecord::RecordNotFound => e
      respond_to do |format|
        format.json { render json: { message: "Help ticket was not found." }, status: 404 }
        format.html { render 'shared/not_found' }
      end
    end

    def find_help_ticket
      HelpTicket.where(workspace_id: scoped_workspace_ids).find(ticket_id)
    end

    def selected_company_id
      if params[:company_id] && current_user.companies.pluck(:id).include?(params[:company_id].to_i)
        params[:company_id].to_i
      else
        nil
      end
    end

    def selected_company
      @selected_company ||= selected_company_id ? Company.find_by_cache(id: params[:company_id]) : nil
    end

    def selected_workspace_id
      if params[:workspace_id] && current_user.companies.pluck(:id).include?(params[:workspace_id].to_i)
        params[:workspace_id].to_i
      else
        nil
      end
    end

    def selected_workspace
      @selected_workspace ||= selected_workspace_id ? Workspace.where(id: params[:workspace_id]) : nil
    end

    def ticket_id
      params[:ticket_id]
    end

    def scoped_resource
      help_ticket
    end

    def authorize_read_ticket
      authorize_read(help_ticket)
    end

    def authorize_write_ticket
      authorize_write(help_ticket)
    rescue => e
      render json: { message: "Sorry, you're not authorized to perform this action." }, status: 401
    end

    def authorize_write_comment
      @comment = help_ticket.help_ticket_comments.find(params[:id])
      @comment.company_user == scoped_company_user || authorize_write(help_ticket)
    rescue ActiveRecord::RecordNotFound => e
      render json: { message: "Comment not found. Please refresh page." }, status: 404
    rescue => e
      render json: { message: "Sorry, you're not authorized to perform this action." }, status: 401
    end
  end
end
