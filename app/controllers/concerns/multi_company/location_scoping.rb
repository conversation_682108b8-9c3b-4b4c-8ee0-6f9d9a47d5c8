module MultiCompany
  module LocationScoping
    extend ActiveSupport::Concern
    include GeneralScoping

    def scoped_resource
      @location ||= Location.where(company_id: current_user.companies.pluck(:id)).find(location_id)
    rescue ActiveRecord::RecordNotFound => e
      respond_to do |format|
        format.json { render json: { message: "Location was not found." }, status: 404 }
        format.html { render 'shared/not_found' }
      end
    end

    def location_id
      params[:location_id]
    end
  end
end
