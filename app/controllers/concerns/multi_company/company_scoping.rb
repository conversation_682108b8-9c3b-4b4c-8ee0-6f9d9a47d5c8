module MultiCompany
  module CompanyScoping
    extend ActiveSupport::Concern
    include MultiCompany::GeneralScoping

    def scoped_company_id
      params[:company_id]
    end

    def company_workspace_ids
      @company_workspace_ids ||= Workspace.find_by(company_id: scoped_company_id).pluck(:id)
    end

    def all_workspace_ids
      @all_workspace_ids ||= begin
        contributor_ids = current_company_user.user.company_users.map(&:contributor_id)
        ExpandedPrivilege.where(contributor_id: contributor_ids).pluck(:workspace_id).uniq
      end
    end
  end
end
