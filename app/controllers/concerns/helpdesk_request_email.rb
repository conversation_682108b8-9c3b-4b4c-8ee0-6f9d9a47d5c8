module HelpdeskRequestEmail
  extend ActiveSupport::Concern

  def request_email(fallback_workspace = nil)
    workspace = respond_to?(:scoped_workspace) && scoped_workspace.present? ? scoped_workspace : fallback_workspace
    default_form = workspace.custom_forms.find_by(default: true)
    helpdesk_custom_form = default_form.helpdesk_custom_form
    if helpdesk_custom_form&.helpdesk_custom_email&.email.present? && helpdesk_custom_form.helpdesk_custom_email.verified
      return helpdesk_custom_form.helpdesk_custom_email.email 
    elsif helpdesk_custom_form.email.present? && helpdesk_custom_form.email.split('@').first != 'null'
      return helpdesk_custom_form.email
    end
    return "helpdesk@#{company_host}"
  end
end
