module SlackOptions
  extend ActiveSupport::Concern
  include ContributorO<PERSON><PERSON><PERSON><PERSON><PERSON>

  def custom_fields_options
    @custom_fields_options ||= begin
      fields_with_options = custom_form.custom_form_fields.where(name: ['status', 'priority']).select(:name, :options)
      options = {}
      fields_with_options.each do |field|
        options["#{field.name}_options"] = map_options(field)
      end
      options
    end
  end

  def map_options(field)
    options = JSON.parse(field.options)

    case field.name
    when 'status'
      return options.map do |option|
        {
          "text": {
            "type": "plain_text",
            "text": option['name'],
            "emoji": true
          },
          "value": option['name']
        }
      end
    when 'priority'
      return options.map do |option|
        {
          "text": {
            "type": "plain_text",
            "text": option['name'].humanize,
            "emoji": true
          },
          "value": option['name']
        }
      end
    end
  end
end
