module DeleteAutomatedTask<PERSON><PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern

  def delete_task_recipients_keys
    if self.class.name == 'AutomatedTasks::AutomatedTask'
      actions = automated_task_actions
    elsif self.class.name == 'AutomatedTasks::TaskAction'
      actions = [self]
    else
      company = if ['Group', 'CompanyMember'].include?(self.class.name)
                  self.company
                elsif self.class.name == 'User'
                  Company.find_by_cache(id: activity_company_id)
                elsif self.class.name == 'GroupMember'
                  group.company
                end
      @workspace_ids = company.workspaces.pluck(:id)
      actions = company_task_actions
    end

    parsed_keys = actions.map do |action|
      { name: 'task_recipients', action_id: action.id }
    end

    parsed_keys.each do |parsed_key|
      next unless Rails.cache.exist?(parsed_key)

      Rails.cache.delete(parsed_key)
    end
  end

  def company_task_actions
    AutomatedTasks::TaskAction.includes(:action_type, :automated_task).where(automated_task: { workspace_id: @workspace_ids },
                                                                             action_type: { action_class: "SendEmail" })
  end

  def automated_task_actions
    AutomatedTasks::TaskAction.includes(:action_type).where(automated_task_id: id, action_type: { action_class: "SendEmail" })
  end
end
