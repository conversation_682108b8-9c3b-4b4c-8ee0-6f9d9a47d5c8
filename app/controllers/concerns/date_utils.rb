module DateUtils
  extend ActiveSupport::Concern

  def get_interval( date, interval, format )
    return {
      eDate: date.strftime(format),
      sDate: (date - interval).strftime("%Y-%m-%d")
    }
  end

  def date_range(date_string, interval, span)
    e_date = DateTime.strptime(date_string, '%m/%d/%Y %I:%M:%S %p')
    s_date = e_date - interval
    dates = create_range s_date, e_date, span
    return dates
  end

  def create_range s_date, e_date, span
    date = s_date
    dates = []
    loop do
      dates << date.strftime('%I:%M:%S %p')
      date += 15.minutes
      break unless date <= e_date
    end
    return dates
  end
  
  def self.parse_time_zone_and_start_time(time_zone, start_time)
    hours, minutes = start_time.split(":").map(&:to_i)
    zone = ActiveSupport::TimeZone[time_zone]

    [hours, minutes, zone]
  end
end
