module RakeTaskExecutor
  extend ActiveSupport::Concern
  require 'rake'

  included do
    unless Rake.application.instance_variable_get(:@tasks_loaded)
      Rails.application.load_tasks
      Rake.application.instance_variable_set(:@tasks_loaded, true)
    end
  end

  def execute_rake_task(task_name, *args)
    task = Rake::Task[task_name]
    task.invoke(*args.map(&:to_s))
  rescue => e
    Rails.logger.error("Failed to execute Rake task #{task_name}: #{e.message}")
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  ensure
    Rake::Task[task_name].reenable
  end
end
