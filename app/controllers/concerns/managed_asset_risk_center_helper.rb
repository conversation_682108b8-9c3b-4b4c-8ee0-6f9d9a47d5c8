module ManagedAssetRiskCenterHelper
  extend ActiveSupport::Concern
  include ManagedAssetHelper
  include PostgresHelper

  included do
    def summary_assets_by_filter(params, scoped_company)
      assets = filtered_assets(params, scoped_company)
      @total_records_summary ||= assets.length
      risk_center_json(assets, scoped_company)
    end

    def filtered_assets(params, scoped_company)
      return filtered_assets_by_type(params, scoped_company) if params[:filter_type] === 'asset_type'

      get_base_filter_asset_ids(params, scoped_company)

      if ['Antivirus', 'RMM Software'].include?(params[:filter_name])
        selected_options = get_selected_options(params[:filter_name], scoped_company.id)
      end

      non_protected_assets = case params[:filter_name]
                             when 'Firewall'
                               assets_with_firewall_not_enabled(scoped_company)
                             when 'Data Encryption'
                               get_non_encrypted_assets(scoped_company)
                             else
                               get_non_protected_assets(selected_options, scoped_company)
                             end

      @total_records_summary = non_protected_assets.count

      non_protected_assets = paginate_assets(non_protected_assets, params)
      non_protected_assets
    end

    def filtered_assets_by_type(params, scoped_company)
      type_assets = assets_by_type(params, scoped_company)
      @total_records_summary = type_assets.count

      type_assets = paginate_assets(type_assets, params)

      return type_assets
    end

    def get_non_protected_assets(selected_options, scoped_company)
      @non_protected_assets ||= begin
        non_protected_asset_ids = @asset_ids - get_protected_assets(selected_options).pluck(:id)
        scoped_company.managed_assets.where(id: non_protected_asset_ids)
      end
    end

    def assets_with_firewall_not_enabled(scoped_company)
      @firewall_not_enabled ||= begin
        non_protected_asset_ids = @asset_ids - firewall_protected_assets.pluck(:id)
        scoped_company.managed_assets.where(id: non_protected_asset_ids)
      end
    end

    def get_protected_assets(selected_options)
      @protected_assets ||= set_read_replica_db do
        @base_filtered_assets.joins(:asset_softwares)
                             .where(asset_softwares: { name: selected_options, archived_at: nil })
                             .distinct
      end
    end

    def firewall_protected_assets
      @firewall_protected_assets ||= @base_filtered_assets.joins(:system_details)
                                      .where(system_details: { detail_category: ['application_layer_firewall_status', 'firewall_info'] })
                                      .where("system_details.detail_data ->> 'status' IN (?)", ['enabled', 'Enabled'])
    end

    def get_encrypted_assets
      @encrypted_assets ||= @base_filtered_assets
                              .joins(ManagedAssets::AssetsQuery::QueryConstants::COMPUTER_DETAILS_JOIN)
                              .where("LOWER(computer_details.disk_encryption) = ?", "on")
    end

    def get_non_encrypted_assets(scoped_company)
      @non_encrypted_assets ||= begin
        non_protected_asset_ids = @asset_ids - get_encrypted_assets.pluck(:id)
        scoped_company.managed_assets.where(id: non_protected_asset_ids)
      end
    end

    def assets_by_risk(params, scoped_company)
      get_base_filter_asset_ids(params, scoped_company)
      selected_options = get_selected_options(params[:filter_type], scoped_company.id)

      assets = get_risk_assets(params, selected_options, scoped_company)

      @total_records = assets.count
      paginate_assets(assets, params)
    end

    def get_risk_assets(params, selected_options, scoped_company)
      if params[:filter_name].blank?
        @base_filtered_assets
      else
        case params[:filter_type]
        when 'Firewall'
          if params[:filter_name] == 'Devices with firewall enabled'
            firewall_protected_assets
          else
            assets_with_firewall_not_enabled(scoped_company)
          end
        when 'Data Encryption'
          if params[:filter_name] == 'Devices with encryption'
            get_encrypted_assets
          else
            get_non_encrypted_assets(scoped_company)
          end
        else
          protected_label = ManagedAsset::LABELS_FOR_WIDGETS.dig(params[:filter_type], 0)
          if params[:filter_name] == protected_label || params[:filter_name] == 'Installed'
            get_protected_assets(selected_options)
          else
            get_non_protected_assets(selected_options, scoped_company)
          end
        end
      end
    end

    def get_base_filter_asset_ids(params, scoped_company)
      where_sql, sql_params, joins = build_where_sql(params)
      asset_scope = filtered_base_assets(scoped_company, joins)
      scoped_assets = asset_scope.where(where_sql, *sql_params)
      @base_filtered_assets = scoped_assets
      @asset_ids = scoped_assets.pluck(:id)
    end

    def assets_by_type(params, scoped_company)
      asset_ids = get_base_filter_asset_ids(params, scoped_company)
      assets = filtered_base_assets(scoped_company, nil).where(id: asset_ids)
      assets = assets.where(company_asset_types: { name: params[:filter_name] }) if params[:filter_name].present?
      assets
    end

    def filtered_base_assets(scoped_company, joins)
      asset_scope = get_base_assets(scoped_company)
      asset_scope = asset_scope.joins(joins) if joins.present?
      asset_scope
    end

    def get_base_assets scoped_company
      @base_assets ||= scoped_company.managed_assets.joins(:asset_type).where(company_asset_types: { name: ['Desktop', 'Laptop', 'Server'] })
    end

    def paginate_assets(scope, params)
      return scope unless params[:page_size].present? && params[:page_index].present?
    
      page_size = extract_page_size(params)
      page_index = extract_page_index(params)
    
      scope.limit(page_size).offset(page_size * page_index)
    end

    def extract_page_size(params)
      params[:page_size].to_i
    end

    def extract_page_index(params)
      params[:page_index].to_i
    end

    def build_where_sql(params)
      archived_filter = [*params['archives'], params['archived']].compact
      warranty_filter = [*params[:warranty_statuses], params[:warranty_status]].compact.uniq
      tag_filter = [*params[:active_tags], params[:tag]].compact.uniq
      department_filter = [*params[:department_ids], params[:asset_department_id]].compact.uniq
      type_filter = [*params[:company_asset_type_ids], params[:company_asset_type_id]].compact.uniq
      status_filter = [*params[:statuses], params[:status]].compact.uniq
      location_filter = [*params[:location_ids], params[:location_id]].compact.uniq
      source_filter = [*params[:sources], params[:source]].compact.uniq
      joins = []

      # Build joins based on params
      joins << "INNER JOIN asset_sources ON asset_sources.managed_asset_id = managed_assets.id" if source_filter.length > 0
      if tag_filter.length > 0
        joins << "INNER JOIN managed_asset_tags ON managed_asset_tags.managed_asset_id = managed_assets.id
                  INNER JOIN company_asset_tags ON company_asset_tags.id = managed_asset_tags.company_asset_tag_id"
      end
      joins << "LEFT JOIN company_asset_statuses cas ON cas.id = managed_assets.company_asset_status_id" if status_filter.length > 0

      # Assignment information join
      joins << "INNER JOIN assignment_informations ON assignment_informations.managed_asset_id = managed_assets.id" if department_filter.length > 0

      sql_params = []
      where_sql = 'managed_assets.merged = false'
      if location_filter.length > 0
        placeholders = location_filter.map { "?" }.join(", ")
        where_sql += " AND managed_assets.location_id IN (#{placeholders})"
        sql_params.concat(location_filter)
      elsif (params[:search])
        terms = params[:search].split
        terms = terms.map {|t| "%#{t}%"}
        terms.each do |t|
          where_sql += " AND managed_assets.name ilike ? "
          sql_params << t
        end
      end

      if tag_filter.length > 0
        placeholders = tag_filter.map { "?" }.join(", ")
        where_sql += " AND company_asset_tags.id IN (#{placeholders})"
        sql_params.concat(tag_filter)
      end

      if department_filter.length > 0
        placeholders = department_filter.map { "?" }.join(", ")
        where_sql += " AND assignment_informations.department_id IN (#{placeholders})"
        sql_params.concat(department_filter)
      end

      if type_filter.length > 0
        placeholders = type_filter.map { "?" }.join(", ")
        where_sql += " AND managed_assets.company_asset_type_id IN (#{placeholders})"
        sql_params.concat(type_filter)
      end

      if source_filter.length > 0
        source_enum_vals = source_filter.map { |s| AssetSource.sources[s] }.compact
        placeholders = source_enum_vals.map { "?" }.join(", ")
        where_sql += " AND asset_sources.source IN (#{placeholders})"
        sql_params.concat(source_enum_vals)
      end

      if archived_filter.length < 3 && archived_filter[0] != 'all'
        value = archived_filter[0] == 'true'
        where_sql += " AND managed_assets.archived = ? "
        sql_params << value
      end

      if warranty_filter.length > 0
        warranty_conditions = []
        
        warranty_filter.each do |status|
          case status
          when "expired_warranty"
            warranty_conditions << "warranty_expiration <= ?"
            sql_params << Date.today
          when "in_warranty"
            warranty_conditions << "warranty_expiration > ?"
            sql_params << Date.today + ManagedAsset::EXPIRING_SOON_MARK
          when "expiring_warranty"
            warranty_conditions << "(warranty_expiration > ? AND warranty_expiration < ?)"
            sql_params << Date.today
            sql_params << Date.today + ManagedAsset::EXPIRING_SOON_MARK - 1.minute
          when "no_warranty"
            warranty_conditions << "warranty_expiration IS NULL"
          end
        end
      
        if warranty_conditions.any?
          where_sql += " AND (#{warranty_conditions.join(' OR ')})"
        end
      end

      if status_filter.length > 0
        placeholders = status_filter.map { "?" }.join(", ")
        where_sql += " AND managed_assets.company_asset_status_id IN (#{placeholders})"
        sql_params.concat(status_filter)
      end

      [where_sql, sql_params, joins]
    end

    def risk_center_json(assets, scoped_company)
      tags = asset_tags(assets.pluck(:id))
      departments = scoped_company.departments.pluck(:id, :name)
      assets.map do |asset|
        assignment_info = asset.assignment_information

        obj = {
          id: asset['id'],
          name: asset['name'],
          asset_type: asset.asset_type.name,
          tags: tags[asset['id']],
          purchase_price: asset.cost&.purchase_price,
          firmware: asset['firmware'],
          source: asset['source'],
          warranty_status: asset.warranty_status,
          warranty_expiration: asset['warranty_expiration'],
          image_thumb_url: thumb_url(asset['id'], asset.asset_type.name),
          image_filename: asset&.image&.filename&.to_s,
          description: asset['description'],
          location_id: asset['location_id'],
          department: departments.find { |id, name| id == assignment_info&.department_id }&.last || "",
          manufacturer: asset['manufacturer'],
          impact: asset['impact'],
          serial_number: asset['machine_serial_number'],
          model: asset['model'],
          operating_systems: asset_softwares_data(asset, 'Operating System'),
          machine_serial_number: asset['machine_serial_number'],
          product_number: asset['product_number'],
          vendor_id: asset['vendor_id'],
          hardware_detail_type: asset['hardware_detail_type'],
          status: asset.custom_status.name,
          ip_address: asset['ip_address'],
          mac_addresses: asset['mac_addresses'],
          sources: sources_str(asset['sources'], asset['source']),
          asset_tag: asset['asset_tag'],
          acquisition_date: asset['acquisition_date'],
          archived: asset['archived'],
          install_date: asset['install_date'],
          universal_links_count: asset['universal_links_count'],
          applications: asset_softwares_data(asset, 'Application'),
          updated_at: asset.updated_at,
        }

        if assignment_info&.used_by_contributor_id.present?
          obj['used_by'] = {
            id: assignment_info.used_by_contributor_id,
            full_name: assignment_info.user&.name,
          }
        end

        if assignment_info&.managed_by_contributor_id.present?
          obj['managed_by'] = {
            id: assignment_info.managed_by_contributor_id,
            full_name: assignment_info.manager&.name,
          }
        end

        obj
      end
    end

    def get_selected_options(name, scoped_company_id)
      AssetRiskCenterWidget.find_by(name: name, company_id: scoped_company_id)&.selected_options
    end
  end
end
