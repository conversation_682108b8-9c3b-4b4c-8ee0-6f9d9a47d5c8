module BaseTicketsHelper
  include Utilities::Domains
  include HelpTicketsHelper
  include MultiCompany::GeneralScoping

  PER_PAGE_DEFAULT = 25

  def workspace_ids
    @workspace_ids ||= begin
      @base_ticket.workspace_ids
    end
  end

  def set_base_ticket
    @base_ticket ||= HelpTickets::BaseTicket.new(scoped_company, scoped_company_user, current_user, scoped_workspace, params, ticket_filtering_params)
  end

  def ticket_filtering_params
    params.permit(:search_terms,
                  :page,
                  :per_page,
                  :company_id,
                  :workspace_id,
                  :managed_by_id,
                  :format,
                  created_by_ids: [],
                  created_by_emails: [],
                  statuses: [],
                  assigned_to_ids: [],
                  priorities: [])
  end

  def tickets
    @tickets ||= HelpDesk::AbbreviatedTicketLoad.new(scoped_company_user, ticket_columns, @base_ticket.default_ticket_columns[:names], params[:is_read_only]).call(limited_ticket_ids, params[:sort])
  end

  def ticket_columns
    @ticket_columns ||= begin
      @base_ticket.ticket_columns
    end
  end

  def limited_ticket_ids
    return ticket_ids.uniq if params[:kanban_view]
    if query_params[:ticket_limit].blank? && query_params[:ticket_offset].blank?
      return ticket_ids
    elsif ticket_ids&.length > 0
      return ticket_ids[ticket_offset..]&.first(ticket_limit)
    end
    return []
  end

  def query_params
    @query_params ||= begin
      @base_ticket.query_params
    end
    set_involved_contributor if has_write_permission
    return @query_params
  end

  # Checks if the current user has write permission in all the workspaces that are included in the query.
  def has_write_permission
    return true if scoped_company_user.is_admin? || scoped_company_user.super_admin?

    workspace_ids = @base_ticket.query_params[:workspaces].pluck(:id)
    write_permission_count = all_expanded_privileges.where(workspace_id: workspace_ids, permission_type: 'write').count
    result = write_permission_count == workspace_ids.count
    workspace_ids = nil
    write_permission_count = nil
    result
  end

  # Sets involved_contributor in the query params to true if scoped_company_user has permissions to all workspaces
  def set_involved_contributor
    @query_params[:involved_contributor_id] = scoped_company_user.contributor_id
  end

  def ticket_ids
    is_dashboard_route = request.url.include?('dashboard/tickets')
    if ENV["TICKETS_CACHE_ENABLED"] == "true"
      excluded_params = ['window_guid', 'action', 'controller', 'format']
      filtered_params = params.except(*excluded_params)
      key = [name: 'ticket_ids', workspace_id: scoped_workspace.id, params: filtered_params.to_json]
      HelpDesk::TicketIdsCache.new(key, scoped_workspace, scoped_company).call
      Rails.cache.fetch(key, :expires_in => 8.hours) do
        fetch_ticket_ids(is_dashboard_route)
      end
    else
      fetch_ticket_ids(is_dashboard_route)
    end
  end

  def fetch_ticket_ids(is_dashboard_route)
    if params[:kanban_view]
      @ticket_ids ||= []
      @kanban_tickets_count = {}
      @all_kanban_tickets_ids = []

      key, values = fetch_kanban_columns

      status_filter = query_params[:statuses] || []
      priority_filter = query_params[:priorities] || []
      sla_filter = query_params[:sla_filter] || []
      recent_filters = ["recently closed", "almost due", "closed this week", "closed this month"]

      values.each do |value|
        modified_params = query_params.dup

        case key
        when "status"
          next unless apply_status_filter(value["name"], status_filter, :statuses, modified_params)
        when "priority"
          next unless apply_priority_filter(value["name"], priority_filter, :priorities, modified_params)
        when "sla"
          next unless apply_sla_filter(value[:name], sla_filter, modified_params)
        when "recent timeframe"
          next unless apply_recent_timeframe_filter(value[:name], modified_params, recent_filters, query_params)
        end

        tkts = HelpDesk::TicketQuery::TicketIds.new(modified_params, is_dashboard_route, scoped_company_user, scoped_company).call.uniq

        name = value["name"] || value[:name]
        @kanban_tickets_count[name] = tkts.count

        paginate_and_concat_tickets(tkts)
      end
    else
      @ticket_ids ||= HelpDesk::TicketQuery::TicketIds.new(query_params, is_dashboard_route, scoped_company_user, scoped_company).call
    end
    @ticket_ids
  end

  def apply_priority_filter(name, filter, param_key, modified_params)
    if filter.blank? || filter.include?(name)
      modified_params[param_key] = [name]
      return true
    end

    false
  end

  def apply_status_filter(name, filter, param_key, modified_params)
    general_filters = ["Active", "New", "Unread", "Archived"]
    service_option = ServiceOption.find_by(service_name: "help_tickets/drafts", status: true)
    general_filters << "Drafts" if !service_option.present?
    if filter.blank? || filter.include?(name)
      matching_filters = filter & general_filters

      modified_params[param_key] = matching_filters + [name]
      modified_params[:kanban_view] = true if matching_filters.any?

      return true
    end

    return false if (filter - general_filters).any?

    matching_filters = filter & general_filters
    if matching_filters.any?
      modified_params[:kanban_view] = true
      status_filters = name === "Closed" && matching_filters.include?("Active") ? matching_filters : matching_filters + [name]
      modified_params[param_key] = status_filters
      return true
    end

    false
  end

  def apply_sla_filter(name, filter, modified_params)
    formatted_sla_name = name.split.map(&:capitalize).join(" ")
    if filter.blank? || filter.include?(formatted_sla_name)
      modified_params[:sla_filter] = [formatted_sla_name]
      true
    else
      false
    end
  end

  def apply_recent_timeframe_filter(name, modified_params, recent_filters, query_params)
    modified_params[:filter_type] = name.tr(" ", "_") if query_params[:filter_type].blank? && !recent_filters.include?(name)

    if query_params[:filter_type].blank? && query_params[:due_soon_filter].blank? &&
       query_params[:recently_closed_filter].blank? && query_params[:completed_ticket_filter].blank?

      case name
      when "created today"
        modified_params[:date_filter] = 2.to_s
      when "created yesterday"
        modified_params[:date_filter] = 8.to_s
      when "created this week"
        modified_params[:date_filter] = 9.to_s
      when "almost due"
        modified_params[:due_soon_filter] = 4.to_s
      when "recently closed"
        modified_params[:recently_closed_filter] = 3.to_s
      when "closed this week"
        modified_params[:completed_ticket_filter] = "completed_this_week"
      when "created this month"
        modified_params[:date_filter] = 10.to_s
      when "closed this month"
        modified_params[:completed_ticket_filter] = "completed_this_month"
      end
    else
      case name
      when "created today"
        return false if modified_params[:date_filter] != 2.to_s
      when "created yesterday"
        return false if modified_params[:date_filter] != 8.to_s
      when "created this week"
        return false if modified_params[:date_filter] != 9.to_s
      when "almost due"
        return false if modified_params[:due_soon_filter] != 4.to_s
      when "recently closed"
        return false if modified_params[:recently_closed_filter] != 3.to_s
      when "closed this week"
        return false if modified_params[:completed_ticket_filter] != "completed_this_week"
      when "created this month"
        return false if modified_params[:date_filter] != 10.to_s
      when "closed this month"
        return false if modified_params[:completed_ticket_filter] != "completed_this_month"
      end
    end

    true
  end

  def paginate_and_concat_tickets(tkts)
    length = params[:per_page].to_i || 5
    page = params[:page].to_i || 1
    offset = (page - 1) * length
    paginated_tickets = tkts[offset, length]

    @all_kanban_tickets_ids.concat(tkts.uniq)
    @ticket_ids.concat(paginated_tickets) if paginated_tickets.present?
  end

  def fetch_kanban_columns
    return unless params[:kanban_view]

    kanban_view = JSON.parse(params[:kanban_view])
    key, value = kanban_view.first

    response = HelpTickets::KanbanColumns.call(scoped_company, value)[key]
    [key, response]
  end

  def ticket_offset
    query_params[:ticket_offset]
  end

  def ticket_limit
    query_params[:ticket_limit]
  end

  # Fetching all the contributor ids of the current user
  def current_contributor_ids
    current_contributor_ids = CompanyUser.where(user_id: scoped_company_user.user_id).pluck(:contributor_id)
    groups_contributor_ids = CompanyUser.joins(:groups).where(user_id: scoped_company_user.user_id).pluck('groups.contributor_id')
    @current_contributor_ids = current_contributor_ids.concat(groups_contributor_ids)
    current_contributor_ids = []
    groups_contributor_ids = []
  end

  def page_count
    (total_ticket_count / (params[:per_page].try(:to_f) || PER_PAGE_DEFAULT)).ceil
  end

  def total_ticket_count
    if params[:kanban_view]
      @all_kanban_tickets_ids.uniq.length
    else
      @total_ticket_count ||= ticket_ids.length
    end
  end

  def unread_comments_count
    @tickets.sum { |t| t[:unread_comments_count] || 0 }
  end

  def kanban_column_tickets_count
    @kanban_tickets_count
  end

  # Aggregates and returns updates for filters and IDs based on request parameters.
  def updated_filters
    filters = params[:filter_by_field] || []
    created_by_ids = params[:created_by_ids] || []
    assigned_to_ids = params[:assigned_to_ids] || []
    workspace_ids = params[:workspace_id] || []

    @filter_by_field_updates = process_filter_updates(filters)
    @created_by_ids_updates = update_ids_with_contributor(created_by_ids) if !created_by_ids.empty?
    @assigned_to_ids_updates = update_ids_with_contributor(assigned_to_ids) if !assigned_to_ids.empty?
    @worspace_ids_updates = update_ids_with_workspaces(workspace_ids) if !workspace_ids.empty?
    {
      filter_by_field: @filter_by_field_updates,
      created_by_ids: @created_by_ids_updates.nil? ? [] : @created_by_ids_updates,
      assigned_to_ids: @assigned_to_ids_updates.nil? ? [] : @assigned_to_ids_updates,
      workspace_ids: @worspace_ids_updates.nil? ? [] : @worspace_ids_updates
    }
  end

  # Identifies and updates filters if the associated record’s name has changed.
  def process_filter_updates(filters)
    filters.each_with_object([]) do |filter, updated|
      parsed_filter = JSON.parse(filter)
      id = parsed_filter["id"]
      original_name = parsed_filter["name"]

      next unless original_name.present?

      prefix = original_name.split(" ").first
      model_name = get_model_for_prefix(prefix)
      next unless model_name

      record = model_name.find_by(id: id)
      update_filter(record, original_name, prefix, updated)
    end
  end

  def get_model_for_prefix(prefix)
    {
      "Asset" => ManagedAsset,
      "Contract" => Contract,
      "Telecom" => TelecomService,
      "Vendor" => Vendor,
      "Location" => Location,
      "People" => Contributor
    }[prefix]
  end

  # Updates filter details if a record’s name differs from its original.
  def update_filter(record, original_name, prefix, updated)
    if record.present?
      updated_name = record.name
      stripped_name = original_name.sub(/^#{Regexp.escape(prefix)}\s+is\s*/, '')

      if updated_name.present? && updated_name != stripped_name
        updated << {
          id: record.id,
          old_name: original_name,
          updated_name: "#{prefix} is #{updated_name}",
          status: "updated"
        }
      end
    else
      updated << {
        old_name: original_name,
        updated_name: "#{original_name} (deleted)",
        status: "deleted"
      }
    end
  end

  # Updates contributor details or marks IDs as deleted if records are missing.
  def update_ids_with_contributor(ids)
    ids.each_with_object([]) do |id, updated|
      contributor = Contributor.find_by(id: id)

      if contributor.nil?
        updated << { id: id, status: "deleted" }
      else
        updated << { id: id, updated_name: contributor.name, status: "updated" }
      end
    end
  end

  def update_ids_with_workspaces(ids)
    ids = ids.is_a?(String) ? ids.split(",") : ids
    ids.each_with_object([]) do |id, updated|
      workspace = Workspace.find_by(id: id)

      if workspace.nil?
        updated << { id: id, status: "deleted" }
      else
        updated << { id: id, updated_name: workspace.name, status: "updated" }
      end
    end
  end

  def ticket_struct
    OpenStruct.new(id: params[:id])
  end

  def authorize_ticket
    authorize_read
  end

  def is_multi_company?
    params[:assigned_to_ids].present? && params[:my_tickets] == 'true' &&
    (current_company.is_reseller_company || current_company.reseller_company_id.present?)
  end

  # Sets assigned_to_ids to contributor IDs based on the specified company or defaults to the current user’s company. 
  def update_assigned_to_ids
    return if params[:company_id].to_i == current_company.id
    company_ids = params[:company_id] || params[:company_ids]

    if company_ids.present?
      params[:assigned_to_ids] = current_user.company_users.where(company_id: company_ids).pluck(:contributor_id)
    else
      params[:assigned_to_ids] = current_user.company_users.pluck(:contributor_id)
    end

    company_ids = []
  end
end
