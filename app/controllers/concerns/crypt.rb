# frozen_string_literal: true

module Crypt
  extend ActiveSupport::Concern

  KEY = Rails.application.credentials.crypt_key
  IV = "123abcd456feg789"
 
  def Crypt.decrypt(encrypted_data, cipher_type)
    encrypted_data_decode = Base64.decode64(encrypted_data)
    aes = OpenSSL::Cipher::Cipher.new(cipher_type)
    aes.decrypt
    aes.key = KEY
    aes.iv = IV
    aes.update(encrypted_data_decode) + aes.final
  rescue
    encrypted_data
  end

  def Crypt.encrypt(data, cipher_type)
    aes = OpenSSL::Cipher::Cipher.new(cipher_type)
    aes.encrypt
    aes.key = KEY
    aes.iv = IV
    Base64.encode64(aes.update(data) + aes.final).gsub("\n", "")
  end

end
