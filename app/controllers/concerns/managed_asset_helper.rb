module ManagedAssetHelper
  extend ActiveSupport::Concern
  include PostgresHelper
  include ReadReplicaDb
  include ManagedAssetInsightsHelper

  included do
    def managed_assets_count
      sql_query = """
        SELECT COUNT(managed_assets.id) AS count,
        company_asset_types.name AS asset_name
        FROM company_asset_types
        INNER JOIN managed_assets
        ON managed_assets.company_asset_type_id = company_asset_types.id
        AND managed_assets.merged = false
        WHERE managed_assets.company_id = :current_company
        AND managed_assets.archived = false
        GROUP BY company_asset_types.name
        ORDER BY COUNT(managed_assets.id) DESC
      """

      sql_params = { current_company: scoped_company.id }
      result = query_result(sql_query, sql_params)

      {
        data: result.map { |row| row['count'] },
        labels: result.map { |row| row['asset_name'] }
      }
    end

    def asset_softwares_data(asset, software_type)
      set_read_replica_db do
        asset.asset_softwares.where(software_type: software_type)
      end
    end

    def sources_str(sources, source)
      srcs_arr = []
      pg_arr  = parse_psql_array(sources)
      if pg_arr.present?
        pg_arr.each do |val|
          srcs_arr << AssetSource.sources.keys[val.to_i]
        end
        srcs_arr << ManagedAsset.sources.keys[source]
        return srcs_arr.flatten.compact.uniq
      end
    end

    def thumb_url(id, asset_type)
      managed_asset = ManagedAsset.find_by(id: id)
      if managed_asset.image.attached?
        managed_asset.image.variant(:thumb).processed.url.to_s
      else
        asset_type_name = scoped_company.asset_types.find_by_name(asset_type)
        if asset_type_name&.image&.filename.present?
          return asset_type_name&.image&.url
        end
        return nil
      end
    end

    def asset_tags(assets_ids)
      data = ManagedAssetTag.where(managed_asset_id: assets_ids).joins(:company_asset_tag).pluck(:name, :id, :managed_asset_id)
      result = Hash.new { |hash, key| hash[key] = [] }
      data.each do |name, id, managed_asset_id|
        result[managed_asset_id] << { id: id, name: name }
      end
      result
    end
  end
end
