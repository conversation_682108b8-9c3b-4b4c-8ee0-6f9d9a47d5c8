module Api
  module V1
    module MobileScoping
      extend ActiveSupport::Concern

      def login_response(company_user, current_company, current_workspace, current_user, device_token, token = nil, refresh_token = nil)
        @login_response ||= begin
          obj = {
            multiple: false,
            is_email_verified: is_email_verified(current_user),
            current_workspace: current_workspace.slice(:id, :name, :company_id),
            is_company_agent: is_company_agent(company_user, current_company, current_user),
            is_mfa_verified: is_mfa_verified(current_company, company_user, current_user, device_token),
            current_company: current_company.slice(:id, :default_logo_url, :original_logo_url, :name, :timezone, :is_reseller_company),
            is_ensure_free_trial_or_subscribed: is_ensure_free_trial_or_subscribed(current_company, current_user),
            company_user: {
              **company_user.slice(:id, :contributor_id),
              user: current_user.slice(:super_admin, :timezone, :email, :full_name, :first_name, :last_name)
            }
          }

          obj[:mfa_settings] = current_company.mfa_setting.slice(:app_auth, :msg_auth, :email_auth) if current_company.mfa_enabled
          obj[:refresh_token] = refresh_token if refresh_token.present?
          obj[:token] = token if token.present?
          obj[:company_user][:user]['avatarUrl'] = company_user.avatar_url
          obj
        end
      end

      def scoped_mobile_company_user(current_company, current_user)
        @scoped_mobile_company_user ||= CompanyUser.find_by_cache(user_id: current_user.id, company_id: current_company.id)
      end

      def all_expanded_privileges(current_company, current_user)
        @all_expanded_privileges ||= begin
          id = scoped_mobile_company_user(current_company, current_user)&.contributor_id
          id.present? ? ExpandedPrivilege.where(contributor_id: id) : ExpandedPrivilege.none
        end
      end

      def is_company_agent(company_user, current_company, current_user)
        return true if current_user.super_admin?
        return company_user.contributor.expanded_privileges.find_by(name: "MobileApp")&.permission_type === "write"
      end

      def mfa_verification_required(current_company, current_user, device_token)
        company_user = scoped_mobile_company_user(current_company, current_user)
        return company_user.mfa_enabled unless device_token

        mfa_session_verified = UserDevice.find_by(token: device_token)&.mfa_session&.mfa_verified
        company_user.mfa_enabled && !mfa_session_verified
      end

      def is_mfa_verified(current_company, company_user, current_user, device_token)
        return true if current_user.super_admin?
        return !(current_company&.mfa_enabled && mfa_verification_required(current_company, current_user, device_token))
      end

      def is_email_verified current_user
        return current_user.blank? ||
          current_user.has_confirmed_email? ||
          current_user.created_at >= (Rails.application.credentials.unconfirmed_email_days_allowed || 5).to_i.days.ago
      end

      def is_ensure_free_trial_or_subscribed(current_company, current_user)
        return true if current_company.blank? || current_user.blank?
        return true if current_user.super_admin?

        if current_company.is_sample_company?
          return true if current_user.has_active_subscription?
        else
          return true if current_company.allow_access?
        end

        return false
      end

      def encoded_auth_token(user_id, expiration_duration)
        JsonWebToken.encode({ user_id: user_id }, expiration_duration)
      end

      def decoded_auth_token(token)
        JsonWebToken.decode(token)
      end

      def render_response(response)
        render json: response[:response_body], status: response[:response_status]
      end
    end
  end
end
