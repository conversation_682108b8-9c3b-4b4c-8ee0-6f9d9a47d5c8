module DomainChecker
  extend ActiveSupport::Concern

  def validate(record)
    if record.class.name == "HelpdeskSetting"
      email = record.selected_option
    elsif record.is_a?(String)
      email = record
    else
      email = record.email
    end

    if email.present?
      domain = email.split('@').last
      if email =~ (/\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\Z/i)
        if record.respond_to?(:email_domain_cache) && record.email_domain_cache.present?
          email_domain = record.email_domain_cache[domain]
        else
          email_domain = EmailDomain.find_by(domain: domain)
        end
        if email_domain.present?
          return email_domain.whitelist? ? nil : true
        end
        
        response = Truemail.validate(email)
        if response.result.success == false
          record.is_a?(String) ? true : record.errors.add("Email", "domain #{domain} does not exist. Please enter a valid email address.")
        elsif response.result.success == true
          return nil
        end
      else
        record.errors.add("Email", 'is not valid.') if !record.is_a?(String)
      end
    end
  end
end
