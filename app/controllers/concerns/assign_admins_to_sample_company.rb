module AssignAdminsToSampleCompany
  extend ActiveSupport::Concern

  def assign_admins(company_user_ids)
    company_users = CompanyUser.where(id: company_user_ids)
    sample_company = Company.find_by_cache(is_sample_company: true)
    read_only_group = sample_company.groups.find_by(name: 'Read Only Admins')

    if company_users.length > 0 && sample_company && read_only_group
      company_users.each do |company_user|
        cloned_cu = CompanyUser.find_by_cache(user_id: company_user.user_id, company_id: sample_company.id)
        cloned_cu ||= CompanyUser.new(user_id: company_user.user_id, company_id: sample_company.id)
        cloned_cu.is_sample_company_user = true
        cloned_cu.user.skip_validations = true
        cloned_cu.granted_access_at = DateTime.now if company_user.granted_access_at.present?
        if cloned_cu.save
          cloned_cu.create_contributor
          gm = read_only_group.group_members.find_or_initialize_by(contributor_id: cloned_cu.contributor_id)
          gm.skip_callbacks = true
          ExpandedPrivileges::Populate.new(cloned_cu.contributor).call if gm.save
        end
      end
    end
  end

  def is_sample_company_user(user)
    CompanyUser.find_by_cache(user_id: user.id, company_id: @sample_company.id)
  end
end
