module NotifyAdminsHelper
  include ReadReplicaDb

  def send_email_to_admins(current_company_id, sync_type, disabled_users)
    current_company, admin_users = nil
    set_read_replica_db do
      current_company = Company.find_cache(id: current_company_id)
      admin_users = current_company.admin_users
    end
    admin_users.each do |user|
      NotifyAdminsMailer.send_users_access_mail(
        user.email,
        user.full_name,
        sync_type,
        current_company,
        disabled_users
      ).deliver_now
    end
  end
end
