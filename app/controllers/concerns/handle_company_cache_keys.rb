module HandleCompanyCacheKeys
  extend ActiveSupport::Concern

  COMPANY_ID_LOOKUP = {
    'Company' => ->(obj) { obj.id },
    'GroupMember' => ->(obj) { obj.group.company_id },
    'LinkableLink' => ->(obj) { obj.source.company_id },
    'AssetUsageHistory' => ->(obj) { obj.managed_asset.company_id },
    'ContractContact' => ->(obj) { obj.contract.company_id },
    'Privilege' => ->(obj) { obj.contributor.company_id }
  }.freeze

  COMPANY_USER_GROUP_CACHE_OPTIONS = [
    "company_user_application_controller",
    "company_user_auth_controller",
    "contributor_company_user",
    "company_user_access_check",
    "contributor_type_group",
    "workspace_default_group",
    "contributor_find",
    "desktop_app_release",
    "company_user_find_by",
    "user_find_by",
    "company_find_by",
    "send_email_recipients_cache",
    "current_user_cache"
  ]

  def delete_subscription_cache_keys
    key_names = ["check_active_subscription", "check_expiring_subscription", "check_free_trial_days"]
    @company_id = self.class.name == "Company" ? self.id : company.id

    parsed_keys = key_names.map do |key_name|
      { name: key_name, company_id: @company_id }
    end

    parsed_keys.each do |parsed_key|
      next unless Rails.cache.exist?(parsed_key)

      Rails.cache.delete(parsed_key)
    end
  end

  def delete_options_cache_keys(entity = 'options', tracking_key = nil, is_reseller_company_change = false)
    if is_reseller_company_change
      reference_company_id = saved_changes[:reseller_company_id].last || saved_changes[:reseller_company_id].first
    end
    company_ids = if self.class.name == 'User'
                    self.company_ids
                  else
                    [
                      reference_company_id || COMPANY_ID_LOOKUP.fetch(self.class.name, lambda { |obj| 
                        obj.respond_to?(:company_id) ? obj.company_id : nil
                      }).call(self)
                    ]
                  end

    company_ids.each do |company_id|
      key = tracking_key || "#{entity}_cache_keys_of_#{company_id}"
      tracked_keys = Rails.cache.fetch(key) { [] }

      if tracked_keys.present?
        tracked_keys.each do |key|
          next unless Rails.cache.exist?(key)

          Rails.cache.delete(key)
        end

        Rails.cache.delete(key)
      end
      trigger_pusher(entity, company_id)
    end
  end

  def trigger_pusher(entity, company_id)
    if entity == 'options'
      pusher(company_guid(company_id), 'contributor_options')
    elsif entity == 'workspace_options'
      pusher(company_guid(company_id), 'workspace_options')
    end
  end

  def track_cached_keys(cache_key, entity = 'options', tracking_key = nil)
    key = tracking_key || "#{entity}_cache_keys_of_#{scoped_company.id}"
    tracked_keys = Rails.cache.fetch(key) { [] }
    return if tracked_keys.include?(cache_key)

    tracked_keys << cache_key
    Rails.cache.write(key, tracked_keys, expires_in: 8.hours)
  end

  def is_cache_enabled?(type = nil)
    if COMPANY_USER_GROUP_CACHE_OPTIONS.include?(type)
      Rails.cache.fetch(type, expires_in: 8.hours) do
        ServiceOption.find_by(service_name: type, service_type: 'cache_type')&.status
      end
    else
      cache_key = "cache_of_#{params[:controller]}##{params[:action]}"
      Rails.cache.fetch(cache_key, expires_in: 8.hours) do
        track_cached_keys(cache_key, nil, 'keys_of_cache_requests')
        ServiceOption.find_by(service_name: "#{params[:controller]}##{params[:action]}", service_type: 'cache_type')&.status
      end
    end
  end

  def delete_company_user_cache_keys(key = nil)
    if key.present?
      return unless Rails.cache.exist?(key)

      return Rails.cache.delete(key)
    end

    if self.class.name == "User"
      user_id = self.id
      company_ids = self.company_ids
    else
      user_id = self.user_id
      company_ids = [self.company_id]
    end

    company_ids.each do |company_id|
      company_user_keys = [
                            "company_user_#{user_id}_#{company_id}",
                            "current_company_user_#{user_id}_#{company_id}"
                          ]

      company_user_keys.each do |key|
        next unless Rails.cache.exist?(key)

        Rails.cache.delete(key)
      end
    end
  end

  def delete_company_cache_keys(keys = nil)
    if keys.present?
      keys.each do |key|
        Rails.cache.delete(key) if Rails.cache.exist?(key)
      end
      return
    end
    company_keys = company_find_by_cache_keys(self)
    company_keys.each { |key| Rails.cache.delete(key) if Rails.cache.exist?(key) }
  end

  def delete_company_user_find_cache_keys(keys = nil)
    if keys.present?
      keys.each do |key|
        Rails.cache.delete(key) if Rails.cache.exist?(key)
      end
      return
    end
    company_users = self.is_a?(User) ? self.company_users : [self]
    company_users.each do |company_user|
      company_user_keys = com_user_find_by_cache_keys(company_user, company_user.company_id)
      company_user_keys.each { |key| Rails.cache.delete(key) if Rails.cache.exist?(key) }
    end
  end

  def delete_user_cache_keys
    user_keys = user_find_by_cache_keys(self)
    user_keys.each { |key| Rails.cache.delete(key) if Rails.cache.exist?(key) }
  end

  def delete_contributor_company_user_cache_keys
    return if self.class.name == "SystemUser"

    contributor_ids = case self.class.name
                      when "Contributor"
                        [self.id]
                      when "CompanyMember", "CompanyUser"
                        [self.contributor_id]
                      when "User"
                        self.company_users.map { |cu| cu.contributor_id }
                      end

    contributor_ids.each do |contributor_id|
      key = "contributor_company_user_#{contributor_id}"
      next unless Rails.cache.exist?(key)

      Rails.cache.delete(key)
    end
  end

  def delete_contributor_cache_keys
    key_type = case self.class.name
               when 'Group'
                self.contributor_id
               when 'Contributor'
                self.id
               end
    key = "contributor_group_#{key_type}"
    return unless Rails.cache.exist?(key)

    Rails.cache.delete(key)
  end

  def delete_contributor_find_cache_keys
    key = "contributor_#{self.id}"
    return unless Rails.cache.exist?(key)

    Rails.cache.delete(key)
  end

  def delete_service_option_cache_key(key)
    return unless Rails.cache.exist?(key)

    Rails.cache.delete(key)
  end

  def delete_workspace_default_group_cache_keys
    key = "workspace_default_group_cache_#{self.id}"
    return unless Rails.cache.exist?(key)

    Rails.cache.delete(key)
  end

  def company_guid(id)
    Company.find_by_cache(id: id)&.guid
  end

  def pusher(channel_id, entity, retry_count: 0)
    begin
      Pusher.trigger(channel_id, entity, { is_cache_reset: true })
    rescue Pusher::HTTPError => e
      raise e if retry_count > 2
      pusher(channel_id, entity, retry_count: retry_count + 1)
    end
  end

  def find_contributor_by_id(contributor_id)
    if is_cache_enabled?('contributor_find')
      cache_key = "contributor_#{contributor_id}"
      Rails.cache.fetch(cache_key, expires_in: 8.hours, skip_nil: true) do
        Contributor.find_by(id: contributor_id)
      end
    else
      Contributor.find_by(id: contributor_id)
    end
  end

  def trigger_asset_management_cache_cleanup(cache_key)
    return if Rails.env.test?

    req_url = "#{Rails.application.credentials.asset_management_service_url}/api/v1/clear_cache_key.json?cache_key=#{cache_key}"
    RestClient.get(req_url)
  end

  def company_find_by_cache_keys(company)
    return [] unless company
  
    [
      "company_find_by_{:id=>'#{company.id}'}",
      "company_find_by_{:subdomain=>'#{company.subdomain}'}",
      "company_find_by_{:guid=>'#{company.guid}'}",
      "company_find_by_{:stripe_id=>'#{company.stripe_id}'}",
      "company_find_by_{:is_sample_company=>'#{company.is_sample_company}'}"
    ]
  end

  def generate_cache_key(workspace, company, with_guests = false, is_help_desk_module = false, additional_context = '')
    params_key = params.as_json.except('format').sort.to_json
    "#{company&.id}_#{workspace&.id}_#{with_guests}_#{is_help_desk_module}_#{params_key}_#{additional_context}"
  end

  def com_user_find_by_cache_keys(company_user, company_id)
    return [] unless company_user && company_id
  
    [
      "company_user_find_by_{:id=>'#{company_user.id}'}",
      "company_user_find_by_{:contributor_id=>'#{company_user.contributor_id}'}",
      "company_user_find_by_{:guid=>'#{company_user.guid}'}",
      "company_user_find_by_{:user_id=>'#{company_user.user_id}', :company_id=>'#{company_id}'}",
      "company_user_find_by_{:email=>'#{company_user.email}', :company_id=>'#{company_id}'}"
    ]
  end

  def user_find_by_cache_keys(user)
    return [] unless user
  
    [
      "user_find_by_{:id=>'#{user.id}'}",
      "user_find_by_{:email=>'#{user.email}'}",
      "user_find_by_{:guid=>'#{user.guid}'}",
      "current_user_cache_#{user.id}"
    ]
  end

  def company_user_cache(company, email)
    if is_cache_enabled?('company_user_find_by')
      key = "company_user_find_by_{:email=>#{email&.downcase}, :company_id=>#{company.id}}"
      Rails.cache.fetch(key, expires_in: 8.hours, skip_nil: true) do
        company.company_users.joins(:user).find_by('lower(users.email) = ?', email&.downcase)
      end
    else
      company.company_users.joins(:user).find_by('lower(users.email) = ?', email&.downcase)
    end
  end
end
