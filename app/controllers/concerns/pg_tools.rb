module PgTools
  extend ActiveSupport::Concern
  
  def search_path
   ActiveRecord::Base.connection.schema_search_path
  end

  def default_search_path
   @default_search_path ||= %{"$user", public}
  end

  def set_search_path(name, include_public = true)
   path_parts = [name.to_s, ("public" if include_public)].compact
   ActiveRecord::Base.connection.schema_search_path = path_parts.join(",") rescue nil
  end

  def restore_default_search_path
   ActiveRecord::Base.connection.schema_search_path = default_search_path
  end
end
