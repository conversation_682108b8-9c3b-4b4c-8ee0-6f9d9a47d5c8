module RevokeAdminAccess
  extend ActiveSupport::Concern

  def revoke_admin_access_sample_com(company_user)
    sample_company = Company.find_by_cache(is_sample_company: true)
    admin_found = false
    company_users = company_user.user.company_users.where.not(company_id: scoped_company.id)
    admin_found = company_users.any? { |cu| cu.is_admin? && cu.company_id != sample_company.id }
    unless admin_found
      sample_user = company_users.find_by(is_sample_company_user: true)
      DeleteAdminsAccessFromSampleCompaniesWorker.perform_async(company_user.id, company_user.company_id) if sample_user.present?
    end
  end
end
