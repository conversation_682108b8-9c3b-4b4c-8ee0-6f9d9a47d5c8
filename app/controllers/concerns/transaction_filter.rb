module TransactionFilter
  extend ActiveSupport::Concern

  def timespan_filter(transactions_set)
    return transactions_set if params['filter_type'].nil? or params['date_filter'].nil?
    transactions_set.where('transaction_date > ? AND transaction_date <= ?', minimum_transaction_date, maximum_transaction_date)
  end

  def maximum_transaction_date
    if params['filter_type'] == "All"
      Date.today.to_date
    elsif params['filter_type'] == "custom_date"
      params['endDate'].to_date
    else
      params['filter_type'] == "months" && params['date_filter'] == '1' ? 1.months.ago.end_of_month.to_date : Date.today.to_date
    end
  end

  def minimum_transaction_date
    if params['filter_type'] == "All"
      minimum = @vendor.general_transactions.order('transaction_date asc')&.first&.transaction_date&.to_date || @vendor.created_at
    elsif params['filter_type'] == "years"
      minimum = (params['date_filter'].to_i * 12).months.ago.beginning_of_month.to_date - 1.day
    elsif params['filter_type'] == "current_month"
      minimum = Date.today.beginning_of_month - 1.day
    elsif params['filter_type'] == "calendar_year"
      minimum = Date.today.beginning_of_year - 1.day - (params['date_filter'].to_i - 1).years
    elsif params['filter_type'] == "fiscal_year"
      minimum = (current_company.fiscal_year || Date.today.beginning_of_year)
      minimum = Time.new(Date.today.year, minimum.month, minimum.day) - 1.second
      minimum -= 1.year if minimum > Date.today
    elsif params['filter_type'] == "custom_date"
      minimum = params['startDate'].to_date
    else
      minimum = params['date_filter'].to_i.months.ago.to_date
      unless minimum == minimum.end_of_month.to_date
        minimum = params['date_filter'].to_i.months.ago.beginning_of_month.to_date - 1.day
      end
    end
    minimum
  end

  def time_range_filter(transactions_set)
    return transactions_set if params['start_date'].nil? or params['end_date'].nil?
    transactions = transactions_set.where('transaction_date >= ? AND transaction_date <= ?', Date.parse(params['start_date']), Date.parse(params['end_date']))
  end
end
