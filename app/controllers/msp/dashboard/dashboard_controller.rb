class Msp::Dashboard::DashboardController < AuthenticatedController
  around_action :set_read_replica_db

  def companies_overview
    render json: Msp::Dashboard::CompaniesOverview.new(current_user, selected_companies).call, status: :ok
  end

  def alerts
    render json: Msp::Dashboard::Alerts.new(current_user, selected_companies).call, status: :ok
  end

  def helpdesk_summary
    tickets_summary = Msp::Dashboard::TicketsSummary.new(current_user, selected_companies, params)
    if params['single_widget_data']
      render json: tickets_summary.tickets_insights, status: :ok
    else
      render json: tickets_summary.categorized_tickets, status: :ok
    end
  end

  def assets_summary
    render json: Msp::Dashboard::AssetsSummary.new(current_user, selected_companies).call, status: :ok
  end

  def contracts_summary
    render json: Msp::Dashboard::ContractsSummary.new(current_user, selected_companies).call, status: :ok
  end

  def vendors_and_sass_summary
    render json: Msp::Dashboard::VendorAndSaasSummary.new(current_user, selected_companies).call, status: :ok
  end

  def get_warranty_data
    render json: Msp::Dashboard::Warranties.new(current_user, selected_companies).call, status: :ok
  end

  def get_vendors_spending
    render json: Msp::Dashboard::VendorsSpending.new(current_user, selected_companies).call, status: :ok
  end

  def get_services_spending
    render json: Msp::Dashboard::ServicesSpending.new(current_user, selected_companies).call, status: :ok
  end

  def get_network_activity
    render json: Msp::Dashboard::NetworkActivity.new(current_user, selected_companies).call, status: :ok
  end

  private

  def selected_companies
    params[:companies] ? JSON.parse(params[:companies]) : []
  end
end
