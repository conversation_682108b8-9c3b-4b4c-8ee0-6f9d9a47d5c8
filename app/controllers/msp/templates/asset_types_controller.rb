class Msp::Templates::AssetTypesController < AuthenticatedController
  set_privilege_name "CompanyUser"
  include MspBuilds

  before_action :set_resource, only: [:update]

  def index
    msp_asset_types = reseller_company.msp_asset_types
    msp_asset_types = msp_asset_types.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_asset_types = msp_asset_types.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_asset_types = msp_asset_types.order(:name)
    
    render json: { msp_asset_types: msp_asset_types }
  end

  def create
    msp_asset_type = reseller_company.msp_asset_types.new(name: params["asset_type"])
    msp_asset_type.set_params(scoped_company_user.id, params)
    msp_asset_type.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_asset_type.last_updated_by_id = scoped_company_user.id
    if msp_asset_type.save
      render json: { message: "Asset type was successfully created"}
    else
      render json: { message: msp_asset_type.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    @msp_asset_type&.set_params(scoped_company_user.id, params)
    if @msp_asset_type.update(name: params["asset_type"], last_updated_by_id: scoped_company_user.id)
      render json: { message: "Asset type was successfully updated"}
    else
      render json: { message: @msp_asset_type.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def copy_existing_item
    msp_asset_type = reseller_company.msp_asset_types.find_by(id: params["id"])
    new_msp_type = msp_asset_type.dup
    new_msp_type.set_params(scoped_company_user.id, params)
    new_msp_type.name = "Copy - #{new_msp_type.name}"
    new_msp_type.last_updated_by_id = scoped_company_user.id
    if new_msp_type.save
      render json: { message: "Asset type was successfully copied"}, status: :ok
    else
      render json: new_msp_type.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_asset_types = reseller_company.msp_asset_types
    items.each do |item|
      if item["company_build_name"].blank?
        asset_type = msp_asset_types.find_by(id: item["id"])
        asset_type.set_params(scoped_company_user.id, params)
        asset_type&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        asset_type = msp_asset_types.find_or_initialize_by(name: item["name"], company_build_id: company_build_id)
        asset_type.last_updated_by_id = scoped_company_user.id
        asset_type.set_params(scoped_company_user.id, params)
        asset_type.save
      end
    end
    render json: { message: "Company asset types are added successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def set_resource
    @msp_asset_type = reseller_company.msp_asset_types.where(id: params["id"]).first
  end
end
