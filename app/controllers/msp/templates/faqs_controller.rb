class Msp::Templates::FaqsController < AuthenticatedController
  include MspBuilds
  set_privilege_name "CompanyUser"
  before_action :set_resource, only: [:update]

  def index
    msp_templates_helpdesk_faqs = reseller_company.msp_templates_helpdesk_faqs
    msp_templates_helpdesk_faqs = msp_templates_helpdesk_faqs.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_templates_helpdesk_faqs = msp_templates_helpdesk_faqs.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_templates_helpdesk_faqs = msp_templates_helpdesk_faqs.order("question_body ASC")
    render json: { msp_templates_helpdesk_faqs: msp_templates_helpdesk_faqs }
  end

  def create
    msp_template_faq = reseller_company.msp_templates_helpdesk_faqs.new(helpdesk_faq_params)
    msp_template_faq.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_template_faq.last_updated_by_id = scoped_company_user.id
    if msp_template_faq.save
      render json: { message: "Faq was successfully created"}, status: :ok
    else
      render json: { message: msp_template_faq.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    if @msp_template_helpdesk_faq.update(answer_body: params["helpdesk_faq"]["answer_body"], question_body: params["helpdesk_faq"]["question_body"], category_id: params["helpdesk_faq"]["category_id"], last_updated_by_id: scoped_company_user.id)
      render json: { message: "Faq was successfully updated"}, status: :ok
    else
      render json: { message: @msp_template_helpdesk_faq.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def copy_existing_item
    msp_template_helpdesk_faq = reseller_company.msp_templates_helpdesk_faqs.find_by(id: params["id"])
    new_msp_template_helpdesk_faq = msp_template_helpdesk_faq.dup
    new_msp_template_helpdesk_faq.last_updated_by_id = scoped_company_user.id
    if new_msp_template_helpdesk_faq.save
      render json: { message: "Faq was successfully copied"}, status: :ok
    else
      render json: new_msp_template_helpdesk_faq.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_template_faqs = reseller_company.msp_templates_helpdesk_faqs

    items.each do |item|
      if item["company_build_name"].blank?
        msp_template_helpdesk_faq = msp_template_faqs.find_by(id: item["id"])
        msp_template_helpdesk_faq&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        msp_template_helpdesk_faq = msp_template_faqs.new
        msp_template_helpdesk_faq.assign_attributes(
          question_body: item["question_body"],
          answer_body: item["answer_body"],
          category_id: item["category_id"],
          last_updated_by_id: scoped_company_user.id,
          company_build_id: company_build_id
        )
        msp_template_helpdesk_faq.save!
      end
    end
    render json: { message: "Company faqs are copied successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def set_resource
    @msp_template_helpdesk_faq = reseller_company.msp_templates_helpdesk_faqs.where(id: params["id"]).first
  end

  def helpdesk_faq_params
    params.require(:helpdesk_faq).permit(:question_body, :answer_body, :category_id, :company_id)
  end
end
