class Msp::Templates::AutomatedTasksController < AuthenticatedController
  set_privilege_name "CompanyUser"
  include MspBuilds

  def index
    respond_to do |format|
      format.json { render json: tasks_json }
    end
  end

  def show
    respond_to do |format|
      format.json { render json: task_json }
    end
  end

  def create
    task = reseller_company.msp_automated_tasks.new(name: task_name)
    task.set_params(scoped_company_user.id, params)
    task.company_build_id = params[:task][:company_build_id] if params[:task][:company_build_id].present?
    task.last_updated_by_id = scoped_company_user.id
    creator = Msp::AutomatedTaskCreate.new(task)
    task = creator.create(task_params)
    if task.save
      render json: { message: "Automated Task was successfully created"}
    else
      render json: { message: msp_automated_task.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    task = reseller_company.msp_automated_tasks.find_by(id: params[:id])
    if task
      task.set_params(scoped_company_user.id, params)
      task.last_updated_by_id = scoped_company_user.id
      updater = Msp::AutomatedTaskUpdate.new(task)
      task = updater.update(task_params)
      respond_to do |format|
        format.json { render json: task }
      end
    else
      respond_to do |format|
        format.json { render json: {} }
      end
    end
  end

  def copy_existing_item
    msp_task = reseller_company.msp_automated_tasks.find_by(id: params["id"])
    msp_task.set_params(scoped_company_user.id, params)
    new_msp_task = msp_task.dup
    new_msp_task.name = "Copy - #{new_msp_task.name}"
    new_msp_task.last_updated_by_id = scoped_company_user.id
    new_msp_task.task_events = events(msp_task)
    new_msp_task.task_actions = actions(msp_task)
    if new_msp_task.save
      render json: { message: "Automated Task was successfully copied"}, status: :ok
    else
      render json: new_msp_task.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    company_build_id = params["company_build_id"]
    items = params["selected_items"]
    msp_automated_tasks = reseller_company.msp_automated_tasks

    items.each do |item|
      if item["company_build_name"].blank?
        msp_task = msp_automated_tasks.find_by(id: item["id"])
        msp_task.set_params(scoped_company_user.id, params)
        msp_task&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        task = msp_automated_tasks.find_by(id: item[:id])
        msp_new_task = msp_automated_tasks.new
        msp_new_task.set_params(scoped_company_user.id, params)
        msp_new_task.name = item["name"]
        msp_new_task.company_build_id = company_build_id
        msp_new_task.last_updated_by_id = scoped_company_user.id
        creator = Msp::AutomatedTaskCreate.new(msp_new_task)
        msp_new_task = creator.create(task_json(task).as_json)
        msp_new_task.save!
      end
    end
    render json: { message: "Company automated tasks are cloned successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def task_params
    @task_params ||= params[:task].as_json
  end

  def task_name
    params["task"]["name"]
  end

  def load_resources
    msp_automated_tasks = reseller_company.msp_automated_tasks
    msp_automated_tasks = msp_automated_tasks.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_automated_tasks = msp_automated_tasks.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    resource_list = msp_automated_tasks.joins(task_events: :event_type).includes(task_events: { event_details: :event_subject_type }, task_actions: :action_type).order(:order)
    resource_list = resource_list.where('automated_tasks_event_types.module' => params[:mod]) if params[:mod]
    resource_list.uniq
  end

  def resources
    @resources ||= load_resources
  end

  def tasks_json
    resources.map do |task|
      task_json(task)
    end
  end

  def resource
    @resource ||= Msp::Templates::AutomatedTask.find_by(id: params[:id])
  end

  def task_json(task = resource)
    Msp::AutomatedTaskJsonOutput.new(reseller_company).json(task)
  end

  def events(task)
    @events = []
    task.task_events.each do |event|
      @event = Msp::Templates::TaskEvent.new(event_type_id: event.event_type_id)
      event.event_details.find_each do |event_detail|
        @event.event_details << event_detail(event_detail)
      end
      @events << @event
    end
    @events
  end

  def event_detail(event_detail)
    detail = Msp::Templates::EventDetail.new
    detail.value = event_detail.value
    detail.event_subject_type_id = event_detail.event_subject_type_id
    detail
  end

  def actions(task)
    @actions = []
    task.task_actions.each do |action|
      type_id = action.action_type_id
      value = action.value
      @actions << Msp::Templates::TaskAction.new(action_type_id: type_id, value: value)
    end
    @actions
  end
end
