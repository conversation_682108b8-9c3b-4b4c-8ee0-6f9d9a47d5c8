class Msp::Templates::CustomFormsController < AuthenticatedController
  include MspBuilds
  set_privilege_name "CompanyUser"

  before_action :set_resource, only: [:show, :update]

  def index
    msp_templates_custom_forms = reseller_company.msp_templates_custom_forms
    msp_templates_custom_forms = msp_templates_custom_forms.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_templates_custom_forms = msp_templates_custom_forms.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    render json: { msp_custom_forms: msp_templates_custom_forms.order("form_name ASC") }
  end

  def create
    msp_custom_form = reseller_company.msp_templates_custom_forms.new(custom_form_params)
    msp_custom_form.set_params(scoped_company_user.id, params)
    msp_custom_form.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_custom_form.last_updated_by_id = scoped_company_user.id
    if msp_custom_form.save
      render json: { message: "Custom Form was successfully created" }, status: :ok
    else
      render json: { message: msp_custom_form.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def show
    if @msp_custom_form.present?
      render json: @msp_custom_form, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  def update
    begin
      validate_default_values(custom_form_params)
      @msp_custom_form&.set_params(scoped_company_user.id, params)
      if @msp_custom_form.update(custom_form_params.merge(last_updated_by_id: scoped_company_user.id))
        update_custom_form_fields
        render json: { message: "Custom Form was successfully updated" }, status: :ok
      else
        render json: { message: @msp_custom_form.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    rescue StandardError => e
      render json: { message: e.message }, status: :unprocessable_entity
    end
  end

  def fetch_all_form_fields
    msp_forms = reseller_company&.msp_templates_custom_forms.where(company_module: "helpdesk")
    if msp_forms.present?
      all_fields = msp_forms.map(&:msp_templates_custom_form_fields)&.flatten
      if all_fields.present?
        render json: { fields: all_fields }, status: :ok
      end
    end
  end
  
  def copy_existing_item
    msp_custom_form = reseller_company.msp_templates_custom_forms.find_by(id: params["id"])
    new_msp_custom_form = msp_custom_form.dup
    new_msp_custom_form&.set_params(scoped_company_user.id, params)
    new_msp_custom_form.set_params(scoped_company_user.id, params)
    new_msp_custom_form.form_name = "Copy - #{new_msp_custom_form.form_name}"
    new_msp_custom_form.last_updated_by_id = scoped_company_user.id
    msp_custom_form.msp_templates_custom_form_fields.each do |msp_custom_form_field|
      required_atts = msp_custom_form_field.serializable_hash.except("id", "msp_templates_custom_form_id", "created_at", "updated_at")
      new_msp_form_field = Msp::Templates::CustomFormField.new(required_atts)
      new_msp_form_field.msp_templates_field_position = msp_custom_form_field.msp_templates_field_position.dup
      new_msp_custom_form.msp_templates_custom_form_fields << new_msp_form_field
    end
    if new_msp_custom_form.save
      render json: { message: "Custom Form was successfully copied"}, status: :ok
    else
      render json: new_msp_custom_form.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    msp_templates_custom_forms = reseller_company.msp_templates_custom_forms

    items.each do |item|
      if item["company_build_name"].blank?
        custom_form = msp_templates_custom_forms.find_by(id: item["id"])
        custom_form&.set_params(scoped_company_user.id, params)
        custom_form&.update(company_build_id: params["company_build_id"], last_updated_by_id: scoped_company_user.id)
      else
        custom_form = msp_templates_custom_forms.find_by(form_name: item['form_name'], company_module: item['company_module'])
        new_msp_custom_form = msp_templates_custom_forms.find_or_initialize_by(form_name: custom_form.form_name, company_module: custom_form["company_module"], company_build_id: params["company_build_id"])
        new_msp_custom_form.last_updated_by_id = scoped_company_user.id
        new_msp_custom_form&.set_params(scoped_company_user.id, params)
        new_msp_custom_form.show_in_open_portal = custom_form["show_in_open_portal"]
        clone_custom_form_fields(new_msp_custom_form, custom_form.msp_templates_custom_form_fields)
        new_msp_custom_form.save!
      end
    end
    render json: { message: "Company custom forms are cloned successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def clone_custom_form_fields form, fields
    msp_templates_custom_form_fields = form.msp_templates_custom_form_fields

    fields.each do |field|
      new_form_field = msp_templates_custom_form_fields.find_or_initialize_by(name: field["name"])
      new_form_field.assign_attributes(name: field["name"],
                                       field_attribute_type: field["field_attribute_type"],
                                       order_position: field["order_position"],
                                       label: field["label"],
                                       required: field["required"],
                                       note: field["note"],
                                       default_value: field["default_value"],
                                       private: field["private"],
                                       permit_view_default: field["permit_view_default"],
                                       permit_edit_default: field["permit_edit_default"],
                                       required_to_close: field["required_to_close"],
                                       list_type: field["list_type"],
                                       sort_list: field["sort_list"],
                                       audience: field["audience"],
                                       options: field["options"]
                                      )
      new_form_field.save!
      msp_templates_field_position = Msp::Templates::FieldPosition.find_or_initialize_by(
        msp_templates_custom_form_field_id: new_form_field.id
      )
      msp_templates_field_position.position = field.msp_templates_field_position["position"]
      msp_templates_field_position.save!
    end
  end

  def update_custom_form_fields
    msp_templates_custom_form_fields = @msp_custom_form.msp_templates_custom_form_fields
    current_ids = msp_templates_custom_form_fields.pluck(:id)
    fields = custom_form_params[:msp_templates_custom_form_fields_attributes]
    submitted_ids = fields.pluck(:id).compact

    to_remove_ids = current_ids - submitted_ids
    @msp_custom_form.msp_templates_custom_form_fields.where(id: to_remove_ids).destroy_all

    fields.select{ |field| !field["id"] }.each do |field|
      new_field = @msp_custom_form.msp_templates_custom_form_fields.new(field.to_h)
      field.set_params(scoped_company_user.id)
      new_field.save
    end
    reseller_company
  end

  def validate_default_values(params)
    template_fields = @msp_custom_form.msp_templates_custom_form_fields.where(name: ["status", "priority"])

    template_fields.each do |field|
      field_params = params[:msp_templates_custom_form_fields_attributes].find { |f| f[:name] == field.name }

      if field_params
        default_options = field.field_attribute_type == "status" ? ["Open", "In Progress", "Closed"] : ["low", "medium", "high"]
        current_options = JSON.parse(field_params[:options] || '[]').map { |opt| opt['name'] }

        missing_default_values = default_options - current_options

        if missing_default_values.present?
          raise StandardError, "Default value(s) #{missing_default_values.join(', ')} for #{field.name} field are missing."
        end
      end
    end
  end

  private

  def set_resource
    @msp_custom_form = reseller_company.msp_templates_custom_forms.where(id: params["id"]).first
  end

  def custom_form_params
    params.require(:custom_form).permit(
      :form_name,
      :company_module,
      :show_in_open_portal,
      :icon,
      :description,
      :color,
      msp_templates_custom_form_fields_attributes: [
        :id,
        :msp_templates_custom_form_id,
        :field_attribute_type,
        :order_position,
        :label,
        :name,
        :default_value,
        :options,
        :required,
        :note,
        :private,
        :permit_view_default,
        :permit_edit_default,
        :sort_list,
        :required_to_close,
        :list_type,
        :audience,        
        msp_templates_field_position_attributes: {}
      ]
    )
  end
end
