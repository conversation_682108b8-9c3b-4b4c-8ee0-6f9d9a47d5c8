class Msp::Templates::LibraryDocumentsController < AuthenticatedController
  include MspBuilds
  set_privilege_name "CompanyUser"
  before_action :set_resource, only: [:update]

  def index
    msp_templates_documents = reseller_company.msp_templates_documents
    msp_templates_documents = msp_templates_documents.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_templates_documents = msp_templates_documents.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_templates_documents = msp_templates_documents.order("name ASC")
    render json: { msp_templates_documents: msp_templates_documents }
  end

  def create
     msp_templates_document = reseller_company.msp_templates_documents.new(resource_params)
     msp_templates_document.set_params(scoped_company_user.id, params)
     msp_templates_document.company_build_id = params[:company_build_id] if params[:company_build_id].present?
     msp_templates_document.last_updated_by_id = scoped_company_user.id
    if msp_templates_document.save
      render json: { message: "Document was successfully created"}
    else
      render json: { message: msp_templates_document.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    @msp_templates_document&.set_params(scoped_company_user.id, params)
    if @msp_templates_document.update(name: params["library_document"]['name'], last_updated_by_id: scoped_company_user.id)
      render json: { message: "Document was successfully updated"}
    else
      render json: { message: @msp_templates_document.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def copy_existing_item
    msp_templates_document = reseller_company.msp_templates_documents.find_by(id: params["id"])
    file_url = msp_templates_document.attached_file.url
    new_msp_templates_document = msp_templates_document.dup
    if valid_file_url?(file_url)
      new_msp_templates_document.attached_file.attach(io: URI.parse(file_url).open, filename: msp_templates_document.attached_file.filename.to_s, content_type: msp_templates_document.attached_file.content_type)
    end
    new_msp_templates_document.name = "Copy - #{new_msp_templates_document.name}"
    new_msp_templates_document.last_updated_by_id = scoped_company_user.id
    new_msp_templates_document.set_params(scoped_company_user.id, params)
    if new_msp_templates_document.save
      render json: { message: "Document was successfully copied"}, status: :ok
    else
      render json: new_msp_templates_document.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_templates_documents = reseller_company.msp_templates_documents
    items.each do |item|
      if item["company_build_name"].blank?
        new_msp_document = msp_templates_documents.find_by(id: item["id"])
        new_msp_document.set_params(scoped_company_user.id, params)
        new_msp_document&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        new_msp_document = msp_templates_documents.find_or_initialize_by(name: item["name"], company_build_id: company_build_id)
        new_msp_document.last_updated_by_id = scoped_company_user.id
        new_msp_document.set_params(scoped_company_user.id, params)
        submitted_doc = msp_templates_documents.find_by(id: item["id"])
        file_url = submitted_doc&.attached_file.url
        if valid_file_url?(file_url)
          new_msp_document.attached_file.attach(io: URI.parse(submitted_doc.attached_file.url).open, filename: submitted_doc.attached_file.filename.to_s, content_type: submitted_doc.attached_file.content_type )
        end
        new_msp_document.save!
      end
    end
    render json: { message: "Company documents are cloned successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def valid_file_url?(url)
    if url.nil?
      false
    else
      res = RestClient.get(url)
      return res.code.to_s.start_with?('2')
    end
  end

  def set_resource
    @msp_templates_document = reseller_company.msp_templates_documents.where(id: params["id"]).first
  end

  def resource_params
    params.require(:library_document).permit(:attached_file, :name, :company_id)
  end
end
