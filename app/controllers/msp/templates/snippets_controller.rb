class Msp::Templates::SnippetsController < AuthenticatedController
  include MspBuilds
  set_privilege_name "CompanyUser"
  before_action :set_resource, only: [:update]

  def index
    msp_snippets = reseller_company.msp_snippets
    msp_snippets = msp_snippets.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_snippets = msp_snippets.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_snippets = msp_snippets.order("title ASC")
    render json: { msp_snippets: msp_snippets }
  end

  def create
    msp_snippet = reseller_company.msp_snippets.new(title: params["title"], description: params["description"])
    msp_snippet.set_params(scoped_company_user.id, params)
    msp_snippet.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_snippet.last_updated_by_id = scoped_company_user.id
    if msp_snippet.save
      render json: { message: "Snippet was successfully created"}, status: :ok
    else
      render json: { message: msp_snippet.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    @msp_snippet&.set_params(scoped_company_user.id, params)
    if @msp_snippet.update(title: params["title"], description: params["description"], last_updated_by_id: scoped_company_user.id)
      render json: { message: "Snippet was successfully updated"}, status: :ok
    else
      render json: { message: @msp_snippet.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def copy_existing_item
    msp_snippet = reseller_company.msp_snippets.find_by(id: params["id"])
    new_msp_snippet = msp_snippet.dup
    new_msp_snippet.set_params(scoped_company_user.id, params)
    new_msp_snippet.title = "Copy - #{new_msp_snippet.title}"
    new_msp_snippet.last_updated_by_id = scoped_company_user.id
    if new_msp_snippet.save
      render json: { message: "Snippet was successfully copied"}, status: :ok
    else
      render json: new_msp_snippet.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_snippets = reseller_company.msp_snippets
    items.each do |item|
      if item["company_build_name"].blank?
        new_msp_snippet = msp_snippets.find_by(id: item["id"])
        new_msp_snippet.set_params(scoped_company_user.id, params)
        new_msp_snippet&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        new_msp_snippet = msp_snippets.find_or_initialize_by(title: item["title"], company_build_id: company_build_id)
        new_msp_snippet.last_updated_by_id = scoped_company_user.id
        new_msp_snippet.set_params(scoped_company_user.id, params)
        new_msp_snippet.description = item["description"]
        new_msp_snippet.save!
      end
    end
    render json: { message: "Company snippets are cloned successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def set_resource
    @msp_snippet = reseller_company.msp_snippets.where(id: params["id"]).first
  end
end
