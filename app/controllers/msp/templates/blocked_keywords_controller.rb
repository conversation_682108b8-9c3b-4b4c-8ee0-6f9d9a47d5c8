class Msp::Templates::BlockedKeywordsController < AuthenticatedController
  set_privilege_name "CompanyUser"
  include MspBuilds

  before_action :set_resource, only: [:update]

  def index
    msp_blocked_keywords = reseller_company.msp_blocked_keywords
    msp_blocked_keywords = msp_blocked_keywords.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_blocked_keywords = msp_blocked_keywords.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_blocked_keywords = msp_blocked_keywords.order("keyword ASC")
    render json: { msp_blocked_keywords: msp_blocked_keywords }
  end

  def create
    msp_blocked_keyword = reseller_company.msp_blocked_keywords.new(keyword: params['blocked_keyword'])
    msp_blocked_keyword.set_params(scoped_company_user.id, params)
    msp_blocked_keyword.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_blocked_keyword.last_updated_by_id = scoped_company_user.id
    if msp_blocked_keyword.save
      render json: { message: 'Blocked keyword was successfully created' }
    else
      render json: { message: msp_blocked_keyword.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    @msp_blocked_keyword&.set_params(scoped_company_user.id, params)
    if @msp_blocked_keyword.update(keyword: params['blocked_keyword'], last_updated_by_id: scoped_company_user.id)
      render json: { message: 'Blocked keyword was successfully updated' }
    else
      render json: { message: @msp_blocked_keyword.errors.full_messages.to_sentence }, status: :bad_request
    end
  end

  def copy_existing_item
    msp_blocked_keyword = reseller_company.msp_blocked_keywords.find_by(id: params["id"])
    new_msp_blocked_keyword = msp_blocked_keyword.dup
    new_msp_blocked_keyword&.set_params(scoped_company_user.id, params)
    new_msp_blocked_keyword.keyword = "Copy - #{new_msp_blocked_keyword.keyword}"
    new_msp_blocked_keyword.last_updated_by_id = scoped_company_user.id
    if new_msp_blocked_keyword.save
      render json: { message: "Blocked keyword was successfully copied"}, status: :ok
    else
      render json: new_msp_blocked_keyword.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_blocked_keywords = reseller_company.msp_blocked_keywords
    items.each do |item|
      if item["company_build_name"].blank?
        msp_blocked_keyword = msp_blocked_keywords.find_by(id: item["id"])
        msp_blocked_keyword&.set_params(scoped_company_user.id, params)
        msp_blocked_keyword&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        msp_blocked_keyword = msp_blocked_keywords.find_or_initialize_by(keyword: item["keyword"], company_build_id: company_build_id)
        msp_blocked_keyword.last_updated_by_id = scoped_company_user.id
        msp_blocked_keyword&.set_params(scoped_company_user.id, params)
        msp_blocked_keyword.save
      end
    end
    render json: { message: "Company blocked keyword are copied successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end
  
  private

  def set_resource
    @msp_blocked_keyword = reseller_company.msp_blocked_keywords.find_by(id: params['id'])
  end
end
