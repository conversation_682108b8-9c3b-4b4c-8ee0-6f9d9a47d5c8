class Msp::Templates::CategoriesController < AuthenticatedController
  include MspBuilds
  set_privilege_name "CompanyUser"
  before_action :set_resource, only: [:update]

  def index
    msp_categories = reseller_company.msp_categories
    msp_categories = msp_categories.where(company_build_id: params[:company_build_id]) if params[:company_build_id].present?
    msp_categories = msp_categories.where.not(id: params[:excludes_ids]) if params[:excludes_ids].present?
    msp_categories = msp_categories.order("name ASC")
    render json: { msp_categories: msp_categories }
  end

  def create
    msp_category = reseller_company.msp_categories.new(name: params["category"])
    msp_category.set_params(scoped_company_user.id, params)
    msp_category.company_build_id = params[:company_build_id] if params[:company_build_id].present?
    msp_category.last_updated_by_id = scoped_company_user.id
    if msp_category.save
      render json: { message: "Category was successfully created"}
    else
      render json: { message: msp_category.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def update
    @msp_category&.set_params(scoped_company_user.id, params)
    if @msp_category.update(name: params["category"], last_updated_by_id: scoped_company_user.id)
      render json: { message: "Category was successfully updated"}
    else
      render json: { message: @msp_category.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def copy_existing_item
    msp_category = reseller_company.msp_categories.find_by(id: params["id"])
    new_msp_category = msp_category.dup
    new_msp_category&.set_params(scoped_company_user.id, params)
    new_msp_category.name = "Copy - #{new_msp_category.name}"
    new_msp_category.last_updated_by_id = scoped_company_user.id
    if new_msp_category.save
      render json: { message: "Category was successfully copied"}, status: :ok
    else
      render json: new_msp_category.errors.full_messages.to_sentence, status: :unprocessable_entity
    end
  end

  def copy_elements
    items = params["selected_items"]
    company_build_id = params["company_build_id"]
    msp_categories = reseller_company.msp_categories
    items.each do |item|
      if item["company_build_name"].blank?
        msp_category = msp_categories.find_by(id: item["id"])
        msp_category&.set_params(scoped_company_user.id, params)
        msp_category&.update(company_build_id: company_build_id, last_updated_by_id: scoped_company_user.id)
      else
        msp_category = msp_categories.find_or_initialize_by(name: item["name"], company_build_id: company_build_id)
        msp_category.last_updated_by_id = scoped_company_user.id
        msp_category&.set_params(scoped_company_user.id, params)
        msp_category.save
      end
    end
    render json: { message: "Company categories are added successfully."}, status: :ok
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def set_resource
    @msp_category = reseller_company.msp_categories.where(id: params["id"]).first
  end
end
