class Msp::Templates::CompanyUsersController < AuthenticatedController
  set_privilege_name "CompanyUser"

  def index
    @company_users = scoped_company.company_users.not_sample_company_user.includes(:user).order('users.last_name, users.first_name')

    user_data = []
    @company_users.each do |company_user|
      user = company_user.user
      data = {
        id: company_user.id,
        title: company_user.title,
        department: company_user.department,
        helpdesk_agent: company_user.helpdesk_agent,
        mobile_phone: company_user.mobile_phone,
        work_phone: company_user.work_phone,
        work_phone_extension: company_user.work_phone_extension,
        work_phone_country_code: company_user.work_phone_country_code,
        work_phone_country_code_number: company_user.work_phone_country_code_number,
        mobile_phone_country_code: company_user.mobile_phone_country_code,
        mobile_phone_country_code_number: company_user.mobile_phone_country_code_number,
        mfa_enabled: company_user.mfa_enabled,
        mfa_verified_at: company_user.mfa_verified_at ,
        mfa_verified: company_user.mfa_verified,
        locked: company_user.locked,
        supervisor_id: company_user.supervisor_id,
        location_id: company_user.location_id,
        user: {
          id: user&.id,
          full_name: user&.full_name || "Missing name",
          email: user&.email,
        }
      }
      user_data << data
    end

    render json: { company_users: user_data }
  end
end
