class Integrations::SageIntacct::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  before_action :set_resource, only: [:destroy, :deactivate]

  def create
    config = Integrations::SageIntacct::Config.find_or_initialize_by(company_id: current_company.id)
    config.assign_attributes(sage_intacct_params)

    if config.save
      config.schedule_hotglue_job("ENABLED")
      return render json: { message: "Sage intacct linked successfully." }, status: :ok
    end
    render json: { message: config.errors.full_messages.to_sentence }, status: :unprocessable_entity
  end

  def destroy
    if @config
      enqueue_company_integration_deletion(@config)
      return render json: { message: "Integration deleted successfully" }, status: :ok
    end
    render json: {}, status: :unprocessable_entity
  end

  def deactivate
    if @config.present?
      @config.company_integration.update!(active: false, status: false)
      @config.schedule_hotglue_job("DISABLED")
      return render json: { message: "Integration deactivated successfully" }, status: :ok
    end
    render json: {}, status: :bad_request
  end

  private

  def sage_intacct_params
    params.require(:sage_intacct).permit(:tenant)
  end

  def set_resource
    @config = Integrations::SageIntacct::Config.find_by_id(params[:id])
  end
  
end
