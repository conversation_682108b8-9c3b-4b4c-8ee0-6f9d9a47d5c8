class Integrations::UsersController < AuthenticatedController
  include ReadReplicaDb

  PER_PAGE_DEFAULT = 25

  def index
    @users_array = []
    @total_users_count = 0

    if params['type'] == 'vendor'
      apps = current_company_apps.where(vendor_id: params['id'])
      apps = apps.where(id: params['appId']) if params['appId'].present?
      apps.each do |app|
        get_users(app)
      end
    elsif params['type'] == 'product'
      apps = current_company_apps.where(product_id: params['id'])
      apps.each do |app|
        get_users(app)
      end
    else
      app = current_company_apps.find_by(id: params['id'])
      get_users(app)
    end

    render json: { users: @users_array, page_count: page_count, total: @total_users_count }, status: :ok
  end

  def get_users app
    already_present_user_ids = @users_array.pluck(:id)
    @total_users_count += app.users.where.not(id: already_present_user_ids).length
    users = app.users
               .includes(user_sources: :integration, apps: :app_sources)
               .where.not(id: already_present_user_ids)
    if params[:page].present? && params[:per_page].present?
      users = users.offset(offset).limit(params[:per_page].to_i)
    end
    users.find_each do |user|
      @users_array << {
        id: user.id,
        email: user.email,
        name: user.name,
        last_login_at: user.last_login_at,
        sources: user.user_sources.map(&:icon),
        apps: get_user_apps(user),
        app_id: app.id,
      }
    end
  end

  def offset
    (params[:page].to_i - 1) * params[:per_page].to_i
  end

  def fetch_users
    users_array = []

    users = Integrations::User.includes(:user_sources).where(company_id: current_company.id)
    users.each do |user|
      user_object = {}
      user_object[:name] = user.name
      user_object[:email] = user.email
      user_object[:last_login_at] = user.last_login_at
      user_object[:user_created_at] = user.user_created_at
      user_object[:sources] = user.user_sources.map(&:icon)
      users_array << user_object
    end

    render json: { users: users_array }, status: :ok
  end

  def create
    ActiveRecord::Base.transaction do
      # If an Integrations::User (params['saas_user']) is provided, then default to that user. 
      # Otherwise, use the info from the selected CompanyUser (params['selected_user'])
      user = params['saas_user'].present? && params['saas_user']['id'] ? params['saas_user'] : params['selected_user']      

      integration_user = scoped_company.integrations_users.find_or_initialize_by(email: user['email'])
      integration_user.name = user['name']
      integration_user.company_user_id = params['selected_user']['root_id']
      integration_user.save!

      intg_app_user = set_read_replica_db do
        Integrations::AppUser.find_or_initialize_by(app_id: params['saas_app']['id'], user_id: integration_user.id)
      end
      intg_app_user.save! if intg_app_user.new_record?
      
      render json: { }, status: :ok
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK" unless Rails.env.test?
      render json: { message: e.message }, status: :unprocessable_entity
    end
  end

  def update
    integration_user = Integrations::AppUser.find(params['saas_user']['id']).user
    integration_user.company_user_id = params['selected_user']['root_id']
    integration_user.save!
    render json: { }, status: :ok
  rescue Exception => e
    Rails.logger.error("Failed to update integration user: #{e.message}")
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def destroy
    app_user = Integrations::AppUser.includes(:app).find(params['id'].to_i)
    if ["manual", "imported"].include?(app_user.app.source)
      app_user.destroy!
    else
      app_user.user.update!(company_user_id: nil)
    end
    render json: { }, status: :ok
  rescue Exception => e
    Rails.logger.error("Failed to destroy app user: #{e.message}")
    render json: { message: e.message }, status: :unprocessable_entity
  end

  private

  def current_company_apps
    current_company.apps.includes(:users)
  end

  def get_user_apps user
    user.apps.map{ |app|
      if app.app_sources.length === 1 && app.app_sources[0].integration_id === microsoft_id
        app.name.humanize if app.app_type === 'licensed'
      else
        app.name.humanize
      end
    }.compact
  end

  def microsoft_id
    @microsoft_id ||= Integration.find_by(name: 'microsoft').id
  end

  def page_count
    (@total_users_count / (params[:per_page].try(:to_f) || PER_PAGE_DEFAULT)).ceil
  end
end
