class Integrations::Aws::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion

  def create
    aws_config = Integrations::Aws::Config.find_or_initialize_by(company_id: current_company.id)
    aws_config.assign_attributes(auth_params)

    if aws_config.save
      render json: { message: "AWS integrated successfully"}, status: :ok
    else
      render json: { message: aws_config.errors.full_messages.to_sentence}, status: :bad_request
    end
  end

  def destroy
    integration_config = Integrations::Aws::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    aws_config = Integrations::Aws::Config.find_by_id(params[:id])
    if aws_config
      aws_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private

  def auth_params
    params.require(:aws_credentials).permit(:access_key, :secret_key, :region)
  end
end
