class Integrations::Microsoft::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  require 'signet/oauth_2/client'
  require 'httparty'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    client = microsoft_service.client(params[:request_from], params[:allow_guest_users])
    redirect_to client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?

      token_detail = microsoft_service.token params[:code]
      expiry_time = Time.now + (token_detail["expires_in"] - 600)

      microsoft_config = Integrations::Microsoft::Config.find_or_initialize_by(
        company_id: params[:state]) #Following OAuth convention. params[:state] is acting as company_id here.

      allow_guest_users = params[:state].split(", ")[2] == "true"

      microsoft_config.assign_attributes(
        token: token_detail["access_token"],
        expires_in: expiry_time,
        refresh_token: token_detail["refresh_token"],
        allow_guest_users: allow_guest_users)

      microsoft_config.save!
    end

    microsoft_redirect_path = "#{build_company_url(current_company)}vendors/sync_accounts?direct_integration=true"
    redirect_to microsoft_redirect_path
  end

  def destroy
    integration_config = Integrations::Microsoft::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    microsoft_config = Integrations::Microsoft::Config.find_by_id(params[:id])
    if microsoft_config
      microsoft_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  def microsoft_integration
    if @current_company.microsoft_config.present?

      company_integration = @current_company.microsoft_config.company_integration
      status = company_integration.status
      sync_status = company_integration.sync_status
      error_message = company_integration.error_message
      ad_enabled = @current_company.microsoft_config.ad_enabled

      render json: { response: true,
                    sync_status: sync_status,
                    error_message: error_message,
                    company_channel_key: current_company.guid,
                    ad_status: ad_enabled
                  }, status: :ok
    else
      render json: { response: false }, status: :ok
    end
  end

  private
  def microsoft_service
    company_id ||= params[:state].split(", ")[0].to_i if params[:state] #Following OAuth convention. params[:state] is acting as company_id here.
    company_id ||= @current_company.id
    @microsoft_service ||= Integrations::Microsoft::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company ||= Company.find_by_cache(id: params[:state]) if params[:state].present?
  end
end
