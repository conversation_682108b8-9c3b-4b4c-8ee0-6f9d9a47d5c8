class Integrations::Slack::Con<PERSON><PERSON>ontroller < AuthenticatedController
  include ::Integrations::Helpdesk::Slack::SlackHelperMethods

  AUTHORIZATION_URI = "https://slack.com/oauth/v2/authorize?client_id=#{ Rails.application.credentials.slack[:client_id]}"

  def show
    redirect_to build_authorization_uri
  end

  private

  def determine_scope
    if slack_feature_access_pending?(current_company.id)
      'chat:write,chat:write.public,commands,incoming-webhook,users:read,users:read.email'
    else
      'chat:write,chat:write.public,commands,incoming-webhook,users:read,users:read.email,channels:history,channels:manage,im:history'
    end
  end

  def build_authorization_uri
    state = { company_id: current_company.id, workspace_id: scoped_workspace.id }.to_json
    "#{AUTHORIZATION_URI}&scope=#{determine_scope}&user_scope=&state=#{state}"
  end
end
