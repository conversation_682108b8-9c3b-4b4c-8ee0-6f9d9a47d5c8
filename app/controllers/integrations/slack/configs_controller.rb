class Integrations::Slack::ConfigsController < AuthenticatedController
  include ApplicationHelper
  include ::Integrations::Helpdesk::Slack::SlackHelperMethods

  skip_before_action :ensure_access, only: :create
  skip_before_action :authenticate_user!, only: :create

  attr_reader :channel_name

  def index
    render json: Integrations::Slack::Config.company_slack_configs(current_company, params), status: :ok
  end

  def create
    redirect_uri = "#{ build_company_url(company) }help_tickets/settings/connectors/slack"

    if params[:code].present?
      token_detail = get_token_details(params[:code])
      @channel_name = token_detail['incoming_webhook']['channel']
      slack_config = Integrations::Slack::Config.find_or_initialize_by(
        team_id: token_detail['team']['id'],
        channel_id: token_detail['incoming_webhook']['channel_id'])
      
      if !slack_config.new_record? && slack_config.company_id != company.id
        flash = { error: "Sorry, this Slack channel is already integrated in #{slack_config.company.name}" }
        return redirect_to "#{redirect_uri}?flash=#{flash.to_json}"
      end

      # This checks the channel name of the format 'ticket_number-user_names-ticket_id'
      ticket_id = channel_name[/\d+$/].to_i
      if ticket_id.present? && HelpTicket.exists?(id: ticket_id)
        flash = { error: "Channels created by the Genuity Bot app cannot be added as integrations. Please choose a different channel." }
        return redirect_to "#{redirect_uri}?flash=#{flash.to_json}"
      end

      attributes = slack_config_attributes(token_detail, company, state_params)
      slack_config.assign_attributes(attributes)
      slack_config.save!

      #Slack TODO: remove flag
      if !slack_config.channel_name.start_with?('#')
        if slack_feature_access_pending?(slack_config.company_id)
          Integrations::Slack::Client.new(slack_config).send_message('Installation Message', installation_message_blocks, channel_id: token_detail['authed_user']['id'])
        else
          Integrations::Slack::Client.new(slack_config).send_message('Installation Message', attachments(installation_message_blocks), channel_id: token_detail['authed_user']['id'])
        end
      end

      child_attributes = attributes.except(:company_id, :workspace_id, :channel_name)

      slack_config.generated_configs.each do |generated_config|
        generated_config.update(child_attributes)
      end

    end

    redirect_to redirect_uri
  
  rescue => e
    flash = { error: "Slack integration failed: #{e.message}" }
    return redirect_to "#{redirect_uri}?flash=#{flash.to_json}"
  end

  def update
    @config = Integrations::Slack::Config.find(params['id'].to_i)
    if params[:channel_name].present?
      handle_channel_name_update
    else
      handle_channel_deactivation
    end
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: 'Channel not found. Please refresh the page and try again.' }, status: :not_found
  end

  def destroy
    if Integrations::Slack::Config.find_by_id(params[:id])&.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  private

  def company
    @company ||= Company.find_cache(id: state_params['company_id'])
  end

  def slack_client
    @slack_client ||= Integrations::Slack::Client.new(@config)
  end

  def channel_deactivation_message
    [
      {
        "type": "section",
        "text": {
          "type": "plain_text",
          "text": "This channel has been deactivated from Genuity web app."
        }
      }
    ]
  end

  def state_params
    JSON.parse(params[:state]) #Following OAuth convention. params[:state] has company_id and workspace_id stringify json here.
  end

  def get_token_details code
    body = {
      code: code,
      client_id: Rails.application.credentials.slack[:client_id],
      client_secret: Rails.application.credentials.slack[:client_secret],
      redirect_uri: slack_callback,
     }
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    response = HTTParty.post 'https://slack.com/api/oauth.v2.access', headers: headers, body: body
    response.parsed_response
  end

  def slack_callback
    "#{ build_secure_url }integrations/slack/oauth2callback"
  end

  def slack_config_attributes(token_detail, company, state_params)
    attributes = {
      access_token: token_detail['access_token'],
      team_name: token_detail['team']['name'],
      channel_name: channel_name.start_with?('#') ? channel_name : "private-channel",
      webhook_configuration_url: token_detail['incoming_webhook']['configuration_url'],
      webhook_url: token_detail['incoming_webhook']['url'],
      authed_user_id: token_detail['authed_user']['id'],
      bot_user_id: token_detail['bot_user_id'],
      company_id: company.id,
      workspace_id: state_params['workspace_id'],
      active: true
    }
  
    unless channel_name.start_with?('#')
      attributes[:meta_data] = { private_channel_name: channel_name }
    end
  
    attributes
  end
  

  def installation_message_blocks
    [
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "*Important Notice:*\nThe integration requires the Genuity bot app to be added in your private channel. You can easily add the bot directly from Slack.\n\nIf you need further assistance, consider the guide below:\n<https://docs.gogenuity.com/docs/slack#step-4-select-your-workspace-from-the-top-right-dropdown-and-then-select-the-specific-channel-which-you-want-to-integrate-with-genuity-and-then-click-the-allow-button|GoGenuity Bot Installation Guide>"
        }
			}
    ]
  end
  
  def handle_channel_name_update
    if params[:channel_name].start_with?('#')
      render json: { message: "Private channel names cannot start with the '#' symbol." }, status: :unprocessable_entity
    elsif @config.update_column(:channel_name, params[:channel_name])
      @config.update_associated_automated_tasks(params[:channel_name])
      render json: { message: 'Channel name has been updated successfully.' }, status: :ok
    else
      render json: { message: 'Something went wrong while updating the channel name. Please try again.' }, status: :unprocessable_entity
    end
  end

  def handle_channel_deactivation
    #Slack TODO: remove flag
    if slack_feature_access_pending?(@config.company_id)
      res = slack_client.send_message('Channel connection status', channel_deactivation_message)
    else
      res = slack_client.send_message('Channel connection status', attachments(channel_deactivation_message, 'red'))
    end
    if (res.class == Slack::Web::Api::Errors::ChannelNotFound || res['ok']) && @config.update(active: false)
      render json: { message: 'Channel has been deactivated successfully.' }, status: :ok
    else
      render json: { message: 'Something went wrong. Please refresh the page and try again.' }, status: :unprocessable_entity
    end
  end
end
