class Integrations::Quickbooks::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  before_action :authorize_write, except: [:oauth_callback]
  skip_before_action :ensure_access, only: [:oauth_callback]
  skip_before_action :authenticate_user!, only: [:oauth_callback]

  def authenticate
    quickbooks_client       = quickbooks_service.client
    quickbooks_client.state = current_company.id
    redirect_to quickbooks_client.authorization_uri.to_s
  end

  def oauth_callback
    state    = params[:state]
    realm_id = params[:realmId]
    @current_company = Company.find_cache(id: state)
    begin
      CronLock.create!(key: "quickbooks:sync:state:#{params[:state]}")
      resp = quickbooks_service.token(params[:code])

      quickbooks_config = Integrations::Quickbooks::Config.find_or_initialize_by(
                          company_id: current_company.id)

      quickbooks_config.assign_attributes(access_token: resp["access_token"],
                                          refresh_token: resp["refresh_token"],
                                          realm_id: realm_id,
                                          token_expires_at: 60.minutes.from_now,
                                          reconnect_token_at: 50.minutes.from_now,
                                          skip_callbacks: true
                                        )

      quickbooks_config.save!
      CronLock.find_by(key: "quickbooks:sync:state:#{params[:state]}")&.destroy!
      Integrations::Quickbooks::FetchVendorsData.new().call(current_company.id)
      redirect_to "#{build_company_url(current_company)}vendors/sync_accounts?financial_accounts=true&show_modal=quickbooks"
    rescue ActiveRecord::RecordNotUnique => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      Rails.logger.warn("Duplicate execution of quickbooks:sync:state:#{params[:state]}")
    rescue => e
      CronLock.find_by(key: "quickbooks:sync:state:#{quickbooks_config.company.id}")&.destroy!
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      redirect_to "#{build_company_url(current_company)}vendors/sync_accounts?financial_accounts=true&error=true"
    end
  end

  def fetch_transactions
    quickbooks_config.assign_attributes(
      start_date: params['start_date'],
      select_all: params['select_all']
    )

    config_integrated_vendors.update_all(sync_status: false, is_new: false)
    config_integrated_vendors.where(id: params['selected_vendor_ids']).update_all(sync_status: true)
    quickbooks_config.save!
  end

  def destroy
    integration_config = current_company.quickbooks_config
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    quickbooks_config = Integrations::Quickbooks::Config.find_by_id(params[:id])
    if quickbooks_config
      quickbooks_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def quickbooks_service
    @quickbooks_service ||= Integrations::Quickbooks::FetchData.new(nil, @current_company)
  end

  def quickbooks_config
    @quickbooks_config ||= Integrations::Quickbooks::Config.find_by(company_id: current_company.id)
  end

  def config_integrated_vendors
    @config_integrated_vendors ||= quickbooks_config.integrated_vendors
  end
end
