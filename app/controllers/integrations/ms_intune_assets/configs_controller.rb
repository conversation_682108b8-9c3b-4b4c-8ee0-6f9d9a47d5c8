class Integrations::MsIntuneAssets::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper
  require "signet/oauth_2/client"

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    client = intune_service.client
    redirect_to client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?
      token_detail = intune_service.token(params[:code])
      unless token_detail["error"].present?
        expiry_time = Time.now + (token_detail["expires_in"] - 600)

        intune_config = Integrations::MsIntuneAssets::Config.find_or_initialize_by(company_id: @current_company.id)
        intune_config.company_user_id = current_company_user&.id

        intune_config.assign_attributes(
          token: token_detail["access_token"],
          expires_in: expiry_time,
          refresh_token: token_detail["refresh_token"]
        )
        intune_config.company_user_id = current_company_user&.id

        intune_config.save!
      end
    end

    redirect_to "#{protocol}#{@current_company.subdomain}.#{Rails.application.credentials.assets_connectors_path}"
  end

  def destroy
    company_integration = Integrations::MsIntuneAssets::Config.find_by_id(params[:id])&.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    intune_config = Integrations::MsIntuneAssets::Config.find_by_id(params[:id])
    if intune_config.present?
      intune_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    else
      render json: { message: "Sorry, there was an error deactivating. Please try again" }, status: :not_found
    end
  end

  private

  def intune_service
    company_id ||= @current_company.id
    @intune_service ||= Integrations::MsIntuneAssets::FetchData.new(company_id)
  end

  def set_company
    @current_company ||= Company.find_by_cache(id: params[:state]) if params[:state].present?
  end
end
