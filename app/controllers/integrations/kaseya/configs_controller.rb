class Integrations::Kaseya::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper

  before_action :set_company, only: :consent_callback
  before_action :set_resource, only: [:destroy, :deactivate, :update_import_type]
  before_action :authorize_write, except: [:consent_callback]
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def create
    kaseya_config = Integrations::Kaseya::Config.find_or_initialize_by(company_id: scoped_company.id)
    kaseya_config.assign_attributes(kaseya_config_params)
    kaseya_config.company_user_id = current_company_user&.id

    begin
      if kaseya_config.save
        client = kaseya_service(kaseya_config.vsa_url).client(kaseya_config.client_id, kaseya_config.integrator_username, kaseya_config.client_secret, current_company_user&.id)
        render json: { message: 'Kaseya configuration succussfully saved.'}
      else
        render json: { message: kaseya_config.errors.full_messages }
      end
    rescue => e
      error_messages = ['authentication failed']
      
      if Rails.env.production? || Rails.env.staging?
        Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
      end

      render json: { message: "Invalid credentials." }, status: :unprocessable_entity
    end
  end

  def consent_callback
    if @current_company.present? && params[:code].present?
      kaseya_config = Integrations::Kaseya::Config.find_by(company_id: params[:state])
      token_detail = kaseya_service(kaseya_config.vsa_url).retrieve_kaseya_access_token(params[:code], kaseya_config.client_id, kaseya_config.client_secret)
      kaseya_config.assign_attributes(
        access_token: token_detail['access_token'],
        token_expires_at: token_detail['expires_in'],
        refresh_token: token_detail['refresh_token']
      )
      kaseya_config.save!
      redirect_to "#{protocol}#{@current_company.subdomain}.#{Rails.application.credentials.assets_connectors_path}"
    else
      render "failed", locals: { params: params['error'].tr("_"," ").capitalize }
    end
  end

  def destroy
    company_integration = @kaseya_config&.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    end
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def deactivate
    if @kaseya_config
      @kaseya_config.update_columns(access_token: nil)
      @kaseya_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    else
      render json: { message: "Sorry, there was an error deactivating. Please try again"}, status: :bad_request
    end
  end

  def update_import_type
    return render json: { message: "Sorry, there was an error changing import place. Please try again" }, status: :bad_request unless @kaseya_config
  
    @kaseya_config.skip_callbacks = true
    @kaseya_config.update!(import_type: params[:import_type])
    render json: { message: "Integration import place updated successfully" }, status: :ok
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private
  def kaseya_service(vsa_url)
    @kaseya_service ||= Integrations::Kaseya::FetchData.new(scoped_company.id, vsa_url)
  end

  def set_company
    @current_company ||= Company.find_by_cache(id: params[:state])
  end

  def kaseya_config_params
    params.require(:kaseya_config).permit(:integrator_username, :client_secret, :vsa_url, :import_type)
  end

  def set_resource
    @kaseya_config =  Integrations::Kaseya::Config.find_by_id(params[:id])
  end
end
