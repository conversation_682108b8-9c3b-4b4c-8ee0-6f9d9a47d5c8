class Integrations::Ubiquiti::ConfigsController < Assets::BaseController
  include IntegrationHelper
  include ReadReplicaDb
  URL_REGEX = /((^\w+:|^)\/\/)?(([a-zA-Z0-9\.\-\:]+)([\/\w\d\W]+)?)/m

  def fetch_sites
    company_integration_data = current_company&.ubiquiti_config&.company_integration
    controllers_data = request.post? ? params[:ubiquiti_config] : all_controllers
    log_event(controllers_data, "save_ubiquiti_configs", true)
    inactive_controllers_data = inactive_controllers if company_integration_data.present? && !company_integration_data.active && !company_integration_data.status
    controller_sites = []
    existing_sites = []
    temp_sites = []
    invalid = nil
    invalid_controller_url = nil
    if params[:import_type].present?
      @import_type = params[:import_type] 
    elsif company_integration_data.present?
      @import_type = current_company&.ubiquiti_config&.import_type
    end
    integration_locations = current_company.integrations_locations.where(source: "ubiquiti").pluck(:address)
    controllers_data.each do |controller|
      temp_sites = []
      ubiquiti_config = get_ubiquiti_config controller
      current_controller = Integrations::Ubiquiti::FetchData.new(ubiquiti_config)
      if ubiquiti_config.valid?
        current_controller&.list_sites&.each do |site|
          temp_sites << { name: "#{site["name"]}-#{site["_id"]}", desc: site["desc"], id: site["_id"], url: ubiquiti_config[:url] }
        end
        new_sites =  temp_sites.uniq.select { |site|  !integration_locations.include?(site[:name]) }
        existing_sites = temp_sites.uniq.select { |site|  integration_locations.include?(site[:name]) }
        controller_sites << { ubiquiti_sites: new_sites, ubiquiti_config: controller, existing_sites: existing_sites }
      else
        invalid = ubiquiti_config
        break
      end
    rescue Exception => e
      invalid_controller_url = ubiquiti_config[:url]
      break
    end
    if invalid_controller_url.present?
      render json: { message: "Invalid credentials for #{invalid_controller_url}"}, status: :request_timeout
    elsif invalid.present?
      render json: { message: invalid.errors.full_messages.join(",  ") }, status: :bad_request
    else
      render json: { controller_sites: controller_sites,
                    existing_sites: existing_sites,
                    company_configs: company_integration_data,
                    inactive_controllers: inactive_controllers_data || [],
                    import_type: @import_type
                  }, status: :ok
    end
  rescue Exception => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end

  def deactivated_controllers
    render json: { inactive_controllers: inactive_controllers }, status: :ok
  end

  def all_controllers
    current_company&.ubiquiti_config&.ubiquiti_controllers
  end

  def integrate_ubiquiti
    params[:ubiquiti_config].each do |config|
      ActiveRecord::Base.transaction do
        ubiquiti_config = Integrations::Ubiquiti::Config.find_or_initialize_by(company_id: current_company.id)
        ubiquiti_config.company_user_id = current_company_user&.id
        ubiquiti_config.import_type = params[:import_type] if params[:import_type].present?
        site_names = config["selected"].map { |site| site["name"] }
        controller_configs = ubiquiti_config.ubiquiti_controllers.find_or_initialize_by(url: config["configs"]["url"])
        controller_configs.assign_attributes({username: config["configs"]["username"], password: config["configs"]["password"], sites: site_names })
        controller_configs[:sites] = (controller_configs[:sites] + site_names).uniq
        controller_configs.company_user_id = current_company_user&.id
        controller_configs.company_id = current_company.id
        controller_configs.save
        ubiquiti_config.save!
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
    render json: { message: "Ubiquiti integrated successfully" }, status: :ok
  end

  def get_ubiquiti_config(controller)
    if request.post?
      ubiquiti_config = Integrations::Ubiquiti::Config.find_or_initialize_by(company_id: current_company.id)
      ubiquiti_controllers = ubiquiti_config.ubiquiti_controllers.find_or_initialize_by(url: controller[:url],
                                                                                        password: controller[:password],
                                                                                        username: controller[:username]
      )
    else
      ubiquiti_controllers = current_company.ubiquiti_config.ubiquiti_controllers.find_by(url: controller[:url])
    end
    ubiquiti_controllers
  end

  def destroy
    company_integration = Integrations::Ubiquiti::Config.find_by_id(params[:id]).company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    ubiquiti_config = Integrations::Ubiquiti::Config.find_by_id(params[:id])
    if ubiquiti_config
      ubiquiti_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  def update_import_type
    ubiquiti_config = set_read_replica_db do
      Integrations::Ubiquiti::Config.find_by_id(params[:id])
    end

    if ubiquiti_config
      ubiquiti_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  def activate_config
    ubiquiti_config = current_company.company_integrations.find_by_id(params[:id].to_i)
    if ubiquiti_config
      ubiquiti_config.update!(active: true, status: true, company_user_id: current_company_user&.id)
      render json: { message: "Integration activated successfully" }, status: :ok
    end
  end

  def delete_controller
    ubiquiti_config_controller = current_company.ubiquiti_config&.ubiquiti_controllers&.find_by_url(params[:url])
    if ubiquiti_config_controller&.destroy
      render json: { message: "Controller deleted successfully" }, status: :ok
    else
      render json: { message: "Something went wrong while deleting controller" }, status: :unprocessable_entity
    end
  end

  def ubiquiti_config_params
    params.require(:ubiquiti_config).permit(:url, :username, :password, :company_id, :sites, :import_type)
  end

  def inactive_controllers
    all_controllers&.map do |controller|
      ubiquiti_configs = get_ubiquiti_config controller
      {
        id: ubiquiti_configs[:id],
        url: ubiquiti_configs[:url]
      }
    end
  end

  def log_event(response, api_type, status)
    status = status ? :success : :error
    log_params = {
      api_type: api_type,
      class_name: 'Integrations::Ubiquiti::ConfigsController',
      company_id: current_company&.id,
      status: status,
      response: response.to_s,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: status != :error ? nil : response,
      error_message: status != :error ? nil : response}
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end
end
