class Integrations::Netsuite::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion

  def create
    netsuite_config = ::Integrations::Netsuite::Config.find_or_initialize_by(company_id: current_company.id)
    netsuite_config.assign_attributes(netsuite_params)

    if netsuite_config.save
      netsuite_config.schedule_hotglue_job("ENABLED")
      return render json: { message: "Netsuite linked successfully." }, status: :ok
    end
    render json: { message: netsuite_config.errors.full_messages.to_sentence }, status: :unprocessable_entity
  end

  def destroy
    integration_config = Integrations::Netsuite::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      return render json: { message: "Integration deleted successfully" }, status: :ok
    end
    render json: {}, status: :unprocessable_entity
  end

  def deactivate
    netsuite_config = Integrations::Netsuite::Config.find_by_id(params[:id])
    if netsuite_config
      netsuite_config.company_integration.update!(active: false, status: false)
      netsuite_config.schedule_hotglue_job("DISABLED")
      return render json: { message: "Integration deactivated successfully" }, status: :ok
    end
    render json: {}, status: :bad_request
  end

  private

  def netsuite_params
    params.require(:netsuite).permit(:tenant)
  end
end
