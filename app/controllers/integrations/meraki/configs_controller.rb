class Integrations::Meraki::ConfigsController < Assets::BaseController
  include IntegrationHelper
  include ReadReplicaDb

  require 'string/similarity'

  before_action :set_company, only: [:create, :fetch_locations, :save_config]

  def fetch_locations
    meraki_config = get_meraki_config
    if meraki_config.valid?
      Integrations::Meraki::FetchTopologiesWorker.perform_async(current_company.id, meraki_config.name, meraki_config.token, request.post?)
    else
      render json: { message: meraki_config.errors.full_messages.join(",  "), is_valid_token: meraki_config.authenticate }, status: :bad_request
    end
  end

  def fetch_meraki_data
    meraki_data = current_company.meraki_records.last
    if meraki_data
      current_company.meraki_records.destroy_all
      render json: { networks: meraki_data.networks, integrated_networks: meraki_data.integrated_networks, meraki_addresses: meraki_data.total_location , existing_locations: meraki_data.existing_locations, meraki_config: meraki_data.meraki_config_id}, status:200
    end
  end

  def get_meraki_config
    if request.post?
      meraki_config = Integrations::Meraki::Config.find_or_initialize_by(company_id: current_company.id)
      meraki_config.assign_attributes(meraki_config_params)
    else
      meraki_config = current_company.meraki_config
    end
    meraki_config
  end

  def destroy
    company_integration = Integrations::Meraki::Config.find_by_id(params[:id])&.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    meraki_config = Integrations::Meraki::Config.find_by_id(params[:id])
    if meraki_config
      meraki_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  def save_config
    meraki_config_params = params[:meraki_config].permit!
    meraki_config = Integrations::Meraki::Config.find_or_initialize_by(company_id: current_company.id)
    meraki_config.company_user_id = current_company_user&.id
    meraki_config.assign_attributes(meraki_config_params)
    if meraki_config.save
      render json: { message: "Cisco Meraki integrated successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def customize
    meraki_config = current_company.meraki_config
    update_meraki_config
    meraki_config.company_integration.update!(sync_status: :pending)
    Integrations::Meraki::SyncDataWorker.perform_async(meraki_config.id, false, true, current_company_user&.id)
    render json: { message: "Cisco Meraki integrated successfully" }, status: :ok
  end

  def update_meraki_config
    meraki_config = current_company.meraki_config
    new_networks = params["selected_networks"]&.split(',')
    del_networks = params["deleted_networks"]&.split(',')

    if new_networks || del_networks
      networks = new_networks.to_a + (meraki_config.sync_networks&.split(',').to_a - del_networks.to_a)
      current_company.discovered_assets.meraki.where(meraki_network_id: del_networks)&.destroy_all
      meraki_config.update_columns(sync_networks: networks.join(','))
    end

    current_company.discovered_assets.meraki.destroy_all if meraki_config.sync_by && meraki_config.sync_by != params["sync_by"]
    if params["sync_by"] == 'networks' || params["sync_by"] == 'allNetworks'
      meraki_config.update_columns(sync_by: params[:sync_by])
      current_company.integrations_locations.where(source: "meraki")&.destroy_all
    elsif params["sync_by"] == 'locations'
      meraki_config.update_columns(sync_by: "locations", sync_networks: "")
    end

    if params["save_cameras"]
      meraki_config.update_columns(save_cameras: true)
    else
      meraki_config.update_columns(save_cameras: false)
      current_company.discovered_assets.where("optional_details->>'device_type' = ?", 'camera')&.destroy_all
    end

    if params["save_sm_devices"]
      meraki_config.update_columns(save_sm_devices: true)
    else
      meraki_config.update_columns(save_sm_devices: false)
      current_company.discovered_assets.where("optional_details->>'device_type' = ?", 'sm_device')&.destroy_all
    end

    update_with_new_meraki_configs
  end

  def update_import_type
    meraki_config = set_read_replica_db do
      Integrations::Meraki::Config.find_by_id(params[:id])
    end

    if meraki_config
      meraki_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private
  def meraki_config_params
    if request.post?
      params.require(:meraki_config).permit(:company_id, :name, :token, :save_cameras, :save_sm_devices, :sync_networks, :import_type)
    end
  end

  def set_company
    if request.post?
      params[:meraki_config][:company_id] = current_company.id
    end
  end

  def update_with_new_meraki_configs
    config_org_name = params[:meraki_config][:name]
    config_token = params[:meraki_config][:token]
    return unless config_org_name.present?
    return unless config_token.present?
    current_company.meraki_config.update(name: config_org_name, token: config_token, skip_callbacks: true, company_user_id: current_company_user&.id)
  end
end
