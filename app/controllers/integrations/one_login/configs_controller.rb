class Integrations::OneLogin::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  before_action :set_company_in_params, only: :create

  def create
    one_login_config = Integrations::OneLogin::Config.find_or_initialize_by(company_id: current_company.id)

    one_login_config.assign_attributes(one_login_config_params)
    if one_login_config.save
      render json: { message: "OneLogin integrated successfully" }, status: :ok
    else
      render json: { message: one_login_config.errors.full_messages.join(",  "), is_valid_token: one_login_config.authenticate }, status: :bad_request
    end
  end

  def destroy
    integration_config = Integrations::OneLogin::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    one_login_config = Integrations::OneLogin::Config.find_by_id(params[:id])
    if one_login_config
      one_login_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def one_login_config_params
    params.require(:one_login_config).permit(:client_id, :client_secret, :region)
  end

  def set_company_in_params
    params[:one_login_config][:company_id] = current_company.id
  end
end
