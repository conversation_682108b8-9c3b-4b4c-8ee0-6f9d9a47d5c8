class Integrations::Bill::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion

  def create
    bill_config = Integrations::Bill::Config.find_or_initialize_by(company_id: current_company.id)
    bill_config.assign_attributes(user_name: auth_params[:user_name],
                                  password: auth_params[:password],
                                  organization_id: auth_params[:org_id],
                                  skip_callbacks: true
                                  )

    if bill_config.save
      fetch_integrated_vendors
      render json: {}, status: :ok
    else
      render json: { message: bill_config.errors.full_messages.to_sentence, is_valid_token: bill_config.authenticate}, status: :bad_request
    end
  end

  def fetch_orgs
    attrs = {user_name: auth_params[:user_name], password: auth_params[:password]}
    response = Integrations::Bill::FetchData.new(attrs).fetch_orgs
    if (response && response["response_status"] == 0)
      render json: { orgs: response["response_data"] }, status:200
    else
      error_message = response ? response["response_data"]["error_message"] : "Undefined Error"
      render json: { message: error_message }, status: :unprocessable_entity
    end
  end

  def fetch_integrated_vendors
    Integrations::Bill::FetchVendorsData.new(current_company.id).call
  end

  def fetch_transactions
    begin
      config_integrated_vendors.where.not(id: params['selected_vendor_ids']).update_all(sync_status: false, is_new: false)
      config_integrated_vendors.where(id: params['selected_vendor_ids']).update_all(sync_status: true)
      bill_config.update!(
        start_date: params['start_date'],
        select_all: params['select_all']
      )
    rescue => e
      flash[:error] = "Failed to fetch vendors transaction: #{e.message}"
      render json: {}, status: :unprocessable_entity
    end
  end

  def destroy
    integration_config = Integrations::Bill::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    bill_config = Integrations::Bill::Config.find_by_id(params[:id])
    if bill_config
      bill_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private

  def auth_params
    params.require(:bill_credentials).permit(:user_name, :password, :org_id)
  end

  def config_integrated_vendors
    @config_integrated_vendors ||= bill_config.integrated_vendors
  end

  def bill_config
    @bill_config ||= Integrations::Bill::Config.find_by(company_id: current_company.id)
  end
end
