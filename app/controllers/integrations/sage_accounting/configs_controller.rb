class Integrations::SageAccounting::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  require 'signet/oauth_2/client'
  require 'httparty'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    client = sage_accounting_service.client
    redirect_to client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?

      response = sage_accounting_service.token params[:code]

      sage_accounting_config = Integrations::SageAccounting::Config.find_or_initialize_by(
        company_id: params[:state])

      token_detail = response[:parsed_response]
      if response[:response_status]

        access_token_expiry_time = Time.now + (token_detail["expires_in"])
        refresh_token_expiry_time = Time.now + (token_detail["refresh_token_expires_in"])

          sage_accounting_config.assign_attributes(
          token: token_detail["access_token"],
          refresh_token: token_detail["refresh_token"],
          expires_in: access_token_expiry_time,
          refresh_token_expires_in: refresh_token_expiry_time)
      else
        sage_accounting_config.assign_attributes(token: "invalid_token", api_errors: token_detail["error"].humanize)
      end

      sage_accounting_config.save!
    end
    redirect_to("#{build_company_url(current_company)}vendors/sync_accounts?financial_accounts=true")
  end

  def destroy
    integration_config = Integrations::SageAccounting::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    sage_config = Integrations::SageAccounting::Config.find_by_id(params[:id])
    if sage_config
      sage_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def sage_accounting_service
    company_id ||= params[:state].split(", ")[0].to_i if params[:state] #Following OAuth convention. params[:state] is acting as company_id here.
    company_id ||= @current_company.id
    @sage_accounting_service ||= Integrations::SageAccounting::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company ||= Company.find_by_cache(id: params[:state]) if params[:state].present?
  end
end
