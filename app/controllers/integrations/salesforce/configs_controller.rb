class Integrations::Salesforce::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  before_action :authorize_write, except: [:oauth_callback]
  before_action :set_company, only: :oauth_callback
  skip_before_action :ensure_access, only: [:oauth_callback]
  skip_before_action :authenticate_user!, only: [:oauth_callback]

  def authenticate
    redirect_to salesforce_service.client.authorization_uri.to_s
  end

  def oauth_callback
    if params[:code].present?

      token_detail = salesforce_service.token params[:code]
      salesforce_config = Integrations::Salesforce::Config.find_or_initialize_by(
        company_id: params[:state]) #Following OAuth convention. params[:state] is acting as company_id here.

      salesforce_config.assign_attributes(token: token_detail["access_token"],
                                          refresh_token: token_detail["refresh_token"],
                                          code: params[:code],
                                          signature: token_detail["signature"],
                                          id_token: token_detail["id_token"],
                                          token_type: token_detail["token_type"],
                                          instance_url: token_detail["instance_url"],
                                          issued_at: token_detail["issued_at"],
                                          salesforce_id: token_detail["id"])

      salesforce_config.save!
    end
    redirect_to "#{build_company_url(current_company)}vendors/sync_accounts?direct_integration=true"
  end

  def destroy
    integration_config = Integrations::Salesforce::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    salesforce_config = Integrations::Salesforce::Config.find_by_id(params[:id])
    if salesforce_config
      salesforce_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def salesforce_service
    company_id ||= params[:state] #Following OAuth convention. params[:state] is acting as company_id here.
    company_id ||= @current_company.id
    salesforce_service ||= Integrations::Salesforce::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company ||= Company.find_cache(id: params[:state])
  end
end
