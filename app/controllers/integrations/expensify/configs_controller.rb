class Integrations::Expensify::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion

  def create
    expensify_config = Integrations::Expensify::Config.find_or_initialize_by(
                                                      company_id: current_company.id)

    expensify_config.assign_attributes(user_id: auth_params[:user_id],
                                       user_secret: auth_params[:user_secret])
    if expensify_config.save
      render json: { message: "Expensify integrated successfully"}, status: :ok
    else
      render json: { message: expensify_config.errors.full_messages.to_sentence, is_valid: expensify_config.authenticate }, status: :bad_request
    end
  end

  def destroy
    integration_config = Integrations::Expensify::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    expensify_config = Integrations::Expensify::Config.find_by_id(params[:id])
    if expensify_config
      expensify_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private

  def auth_params
    params.require(:expensify_credentials).permit(:user_id, :user_secret)
  end
end
