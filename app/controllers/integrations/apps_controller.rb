class Integrations::AppsController < AuthenticatedController
  include LinkDefaultVendor
  include PostgresHelper
  PER_PAGE_SIZE = 25

  def index
    execute_psql_query
    render json: { saas_apps: @apps, total_apps: total_record , page_count: page_count }, status: :ok
  end

  def create
    ActiveRecord::Base.transaction do
      saas_app = scoped_company.integrations_apps.new(app_params)
      check_default_vendors(saas_app, params['default_vendor_name']) if params['default_vendor_name'].present?
      saas_app.save!
      render json: { }, status: :ok
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      render json: { message: e.message }, status: :unprocessable_entity
    end
  end

  def update
    app = Integrations::App.find_by(id: params['app']['id'])
    ActiveRecord::Base.transaction do
      app.assign_attributes(app_params)
      check_default_vendors(app, params['default_vendor_name']) if params['default_vendor_name'].present?
      app.save!
      render json: { }, status: :ok
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      render json: { message: e.message }, status: :unprocessable_entity
    end
  end

  def bulk_update
    apps = scoped_company.integrations_apps.where(id: params[:integrations_apps][:apps_ids])
    ActiveRecord::Base.transaction do
      apps.each do |app|
        if integrations_app_params[:status] == 'ignored' && app.source == 'manual'
          app.destroy!
        else
          app.assign_attributes(integrations_app_params)
          app.save!
        end
      end
      render json: {}, status: :ok
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      render json: {}, status: :unprocessable_entity
    end
  end

  def check_access
    #TODO need to implement logic to restrict access to limited users
    render json: {status: true}, status: :ok
  end

  def services
    app = Integrations::App.find(params[:app_id])
    render json: {services: app&.app_services.pluck(:friendly_name).compact}, status: :ok
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: "Services were not found." }, status: :not_found
  end

  def old_apps_of_user
    user_ids = Integrations::User.where(company_user_id: params[:company_user_id]).pluck(:id)
    history_app_ids = ContractAndAppHistory.where(user_id: user_ids, record_type: "app").pluck(:app_id).uniq
    new_app_ids = Integrations::App.joins("INNER JOIN integrations_app_users ON integrations_apps.id = integrations_app_users.app_id
                                          INNER JOIN integrations_users ON integrations_app_users.user_id = integrations_users.id")
                                   .where("integrations_users.company_user_id = ?", params[:company_user_id]).distinct.pluck(:app_id)
    old_app_ids = history_app_ids - new_app_ids
    apps = Integrations::App.where(id: old_app_ids).offset((params[:page_no].to_i - 1) * params[:page_size].to_i).limit(params[:page_size].to_i)
    apps_data = apps.map do |app|
      {
        id: app.id,
        name: app.name,
        logo_url: app.logo_url,     
        app_type: app.app_type,    
        access_type: app.access_type 
      }
    end
    render json: { apps: apps_data, total_old_apps: old_app_ids.count }
  end

  private
  def integrations_app_params
    {
      vendor_id: vendor_id,
      product_id: product_id,
      status: params[:integrations_apps][:status],
    }
  end

  def vendor_id
    return vendor.id if params[:integrations_apps][:vendor_id]  || params[:integrations_apps][:default_vendor_id]
    return nil
  end

  def product_id
    return product.id if params[:integrations_apps][:product_id]  || params[:integrations_apps][:default_product_id] || params[:integrations_apps][:new_product_name]
    return nil
  end

  def vendor
    @vendor ||= begin
      vendor_id = params[:integrations_apps][:vendor_id]
      default_vendor_id = params[:integrations_apps][:default_vendor_id]
      category_id = params[:integrations_apps][:category_id]
      if vendor_id
        vendor = current_company.vendors.find_by(id: vendor_id)
        vendor.update!(category_id: category_id) if vendor.category_id != category_id
        vendor
      else
        default_vendor = DefaultVendor.find_by(id: default_vendor_id)
        unless category_id
          cat_name = default_vendor.default_category.name
          category_id = current_company.categories.create!(name: cat_name).id
        end
        if default_vendor
          current_company.vendors.create!(
            name: default_vendor.name,
            category_id: category_id,
            creator_id: current_company_user&.contributor&.id,
            created_by: current_company_user)
        else
          nil
        end
      end
    end
  end

  def product
    @product ||= begin
      product_id = params[:integrations_apps][:product_id]
      default_product_id = params[:integrations_apps][:default_product_id]
      new_product_name = params[:integrations_apps][:new_product_name]
      if new_product_name
        current_company.products.create!(vendor: vendor, name: new_product_name)
      elsif product_id
        current_company.products.find_by(id: product_id)
      else
        default_product = DefaultProduct.find_by(id: default_product_id)
        if default_product
          current_company.products.create!(vendor: vendor, name: default_product.name, default_product: default_product)
        else
          nil
        end
      end
    end
  end
  def order_by_string
    if sort_direction && sort_by
      case sort_by
      when "total"
        "ia.total_users #{sort_direction}"
      when "users"
        "ia.used #{sort_direction}"
      when "usage"
        "usage #{sort_direction} NULLS LAST"
      when "vendor"
        "vendors.name #{sort_direction} NULLS LAST"
      when "product"
        "products.name #{sort_direction} NULLS LAST"
      else
        "ia.name #{sort_direction}"
      end
    elsif params['group_by'] == 'vendors'
      "vendors.name ASC NULLS LAST"
    else
      "ia.name ASC"
    end
  end
                
  def sort_by
    params[:active_sort]
  end

  def sort_direction
    params[:active_sort_direction]
  end

  def execute_psql_query
    sql_query =<<END
    SELECT
      ia.id,
      ia.name,
      ia.status,
      ia.app_type,
      ia.logo_url,
      ia.total_users,
      ia.used,
      ia.access_type,
      ia.created_at,
      ia.source,
      ia.mfa_enabled,
      ia.friendly_name,
      ia.vendor_id,
      #{provider_view_select}
      jsonb_agg(DISTINCT jsonb_build_object('id', iau.id, 'contributor_id', cu.contributor_id, 'company_user_id', cu.id, 'name', iu.name, 'email', iu.email, 'last_login', iu.last_login_at)) AS users,
      array_remove(array_agg(DISTINCT i.icon), NULL) AS sources,
      MIN(i.name) AS service_name,
      CASE
        WHEN ia.access_type = 0 THEN '#{access_type_value(0)}'
        WHEN ia.access_type = 1 THEN '#{access_type_value(1)}'
        WHEN ia.access_type = 2 THEN '#{access_type_value(2)}'
        ELSE ''
      END AS access_type,

      CASE
        WHEN ia.status = 0 THEN '#{app_status(0)}'
        WHEN ia.status = 1 THEN '#{app_status(1)}'
        WHEN ia.status = 2 THEN '#{app_status(2)}'
      END AS status,

      CASE
        WHEN ia.app_type = 0 THEN '#{app_type_value(0)}'
        WHEN ia.app_type = 1 THEN '#{app_type_value(1)}'
        WHEN ia.app_type = 2 THEN '#{app_type_value(2)}'
        ELSE ''
      END AS app_type,

      CASE
        WHEN COALESCE(ia.total_users, 0.0) < COALESCE(ia.used, 0.0) THEN 100
        WHEN ia.total_users > 0 THEN COALESCE((ia.used::float / ia.total_users::float) * 100, 0)
        ELSE 0
      END AS usage,
      count(*) OVER() AS full_count
    FROM
      integrations_apps ia
    LEFT JOIN
      integrations_app_users iau ON iau.app_id = ia.id
    LEFT JOIN
      integrations_users iu ON iu.id = iau.user_id
    LEFT JOIN
      company_users cu ON cu.id = iu.company_user_id
    LEFT JOIN
      integrations_app_sources ias ON ias.app_id = ia.id
    LEFT JOIN
      integrations i ON i.id = ias.integration_id
      #{search_join}
      #{provider_view_joins}
      #{source_join}
    WHERE ia.company_id = :company_id
      #{edit_app_where}
      #{search_where_clause}
      #{source_where_clause}
      AND ia.status IN (#{status.join(", ")})
    GROUP BY ia.id #{group_by_provider_view}
    ORDER BY #{order_by_string}
    LIMIT :limit OFFSET :offset;
END
    sql_params = {
      company_id: scoped_company.id, 
      limit: index_per_page, 
      offset: offset,
      status: status
    }
    sql_params[:search_query] = "%\\#{params[:search]}%" if params[:search].present?
    query = ActiveRecord::Base.send(:sanitize_sql_array, [clean_query(sql_query), sql_params])
    results = ActiveRecord::Base.connection.execute(query)
    @company_apps = results.as_json
    set_apps()    
  end

  def set_apps
    @apps = []
    if params['group_by'] == 'apps'
      set_app_view_apps
    else
      set_provider_view_apps
    end         
  end

  def set_app_view_apps
    @vendors = []
    @company_apps.each do |obj|
      vendor_obj = nil
      vendor_obj = @vendors.find { |vendor| vendor[:id] == obj['vendor_id'] } if @vendors.present?
      vendor_obj = set_vendor(obj['vendor_id']) unless vendor_obj.present?
      obj[:vendor_name] = vendor_obj[:name]
      @apps << obj
    end
  end

  def set_provider_view_apps
    linked_vendor_apps = []
    without_vendor_apps = []

    @company_apps.each do |obj|
      if obj['vendor_id'].present?
        linked_vendor_apps << obj
      else
        without_vendor_apps << obj
      end
    end
    
    grouped_apps = linked_vendor_apps.group_by { |app| app['vendor_id'] }
  
    grouped_apps.each do |vendor_id, apps|
      vendor_app = apps.first
      vendor_logo_url = Vendor.find_by(id: vendor_id).logo_url

      @apps << {
        vendor_id: vendor_id,
        logo_url: vendor_logo_url,
        vendor_name: vendor_app['vendor_name'],
        sources: vendor_app['sources'],
        category_name: vendor_app['category_name'],
        created_at: vendor_app['created_at'],
        saas_apps: apps,
        total_staff: total_staff(apps)
      }
    end

    without_vendor_apps.each do |app|
      users = JSON.parse(app['users'])
      @apps << {
        vendor_id: nil,
        vendor_name: app['name'],
        logo_url: app['logo_url'],
        category_name: 'Uncategorized',
        sources: app['sources'],
        created_at: app['created_at'],
        saas_apps: [app],
        total_staff: users.first['name'].present? ? users.count : 0
      }
    end
  end

  def set_vendor(id)
    vendor_name = Vendor.find_by(id: id)&.name
    vendor_obj = { id: id, name: vendor_name }
    @vendors << vendor_obj
    vendor_obj
  end

  def search_where_clause
    if params[:search].present?
      search_query = "AND (ia.name ILIKE :search_query OR ia.friendly_name ILIKE :search_query
                            OR searched_iu.name ILIKE :search_query"
      if params['group_by'] == 'apps'
        return "#{search_query})"
      end
      "#{search_query} OR vendors.name ILIKE :search_query)"
    end
  end

  def is_licensed_app(type)
    Integrations::App.app_types.keys[type] == "licensed"
  end

  def offset
    return 0 if params['app_id'].present?
    params['page'].to_i > 0 ? (params['page'].to_i) * index_per_page : 0
  end

  def index_per_page
    params["per_page"] ? params["per_page"].to_i : PER_PAGE_SIZE
  end

  def page_count
    @company_apps.length > 0 ? (total_record / index_per_page.to_f).ceil : 0
  end

  def total_record
    @company_apps.length > 0 ? @company_apps.last["full_count"] : 0
  end

  def app_params
    params.require(:app).permit(
      :name,
      :app_type,
      :total_users,
      :used,
      :access_type,
      :mfa_enabled,
      :status,
      :source,
      :friendly_name,
      :vendor_id
    )
  end

  def total_staff(linked_apps)
    linked_apps.sum do |linked_app|
      users = JSON.parse(linked_app["users"])
      empty_users_name = users.reject { |obj| obj['name'].nil?}
      if empty_users_name.blank?
        empty_users_name.count
      else
        users.count
      end
    end
  end

  def provider_view_select
    if params['group_by'] == 'vendors'
      return 'vendors.id as vendor_id,
        vendors.name as vendor_name,
        categories.name as category_name,'
    end
  end
  
  def provider_view_joins
    if params['group_by'] == 'vendors' || params['app_id'].present?
      return 'LEFT OUTER JOIN vendors ON vendors.id = ia.vendor_id
      LEFT OUTER JOIN categories ON categories.id = vendors.category_id'
    end
  end

  def group_by_provider_view
    if params['group_by'] == 'vendors'
      return ', vendors.id,
        vendors.name,
        categories.name'
    end
  end

  def status
    return [0, 1] unless params['status'] == 'ignored'
    return [2]
  end

  def access_type_value(val)
    Integrations::App.access_types.key(val)
  end

  def app_status(val)
    Integrations::App.statuses.key(val)
  end

  def app_type_value(val)
    Integrations::App.app_types.key(val)
  end

  def edit_app_where
    params['app_id'].present? ? "AND ia.id = #{params['app_id'].to_i}" : ''
  end

  def source_join
    if params['source'].present? && !params[:source].in?(['manual', 'imported'])
      'INNER JOIN integrations_app_sources ON integrations_app_sources.app_id = ia.id'
    end
  end

  def source_where_clause
    if params['source'].present?
      source = params[:source]
      if source == 'manual'
        "AND ia.source = 'manual'"
      elsif source == 'imported'
          "AND ia.source = 'imported'"
      else
        integration_id = Integration.find_by(name: source)&.id
        "AND integrations_app_sources.integration_id = #{integration_id}" if integration_id
      end
    end
  end

  def search_join
    if params[:search].present?
      'LEFT JOIN
        integrations_app_users searched_iau ON searched_iau.app_id = ia.id
      LEFT JOIN
        integrations_users searched_iu ON searched_iu.id = searched_iau.user_id'
    end
  end
end
