class Integrations::AzureAssets::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper

  require 'signet/oauth_2/client'
  require 'httparty'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    azure_assets_service.client
    redirect_to azure_assets_service.client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?
      token_detail = azure_assets_service.token params[:code]
      expiry_time = Time.now + (token_detail["expires_in"].to_i - 600)

      #Following OAuth convention. params[:state] is acting as company_id here.
      azure_assets_config = Integrations::AzureAssets::Config.find_or_initialize_by(company_id: params[:state])

      azure_assets_config.assign_attributes(
        token: token_detail["access_token"],
        expires_in: expiry_time,
        refresh_token: token_detail["refresh_token"])
      azure_assets_config.company_user_id = current_company_user&.id

      azure_assets_config.save!
    end

    redirect_to "#{protocol}#{@current_company.subdomain}.#{Rails.application.credentials.assets_connectors_path}?direct_integration=true"
  end

  def destroy
    company_integration = Integrations::AzureAssets::Config.find_by_id(params[:id])&.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    azure_assets_config = Integrations::AzureAssets::Config.find_by_id(params[:id])
    if azure_assets_config
      azure_assets_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def azure_assets_service
    company_id ||= params[:state] #Following OAuth convention. params[:state] is acting as company_id here.
    company_id ||= @current_company.id
    @azure_assets_service ||= Integrations::AzureAssets::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company ||= Company.find_by_cache(id: params[:state]) if params[:state].present?
  end
end
