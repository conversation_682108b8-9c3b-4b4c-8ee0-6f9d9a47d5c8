class Integrations::Okta::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion

  def create
    okta_config = Integrations::Okta::Config.find_or_initialize_by(company_id: current_company.id)
    okta_config.assign_attributes(name: auth_params[:name],
                                  token: auth_params[:token])

    if okta_config.save
      render json: { message: "Okta integrated successfully"}, status: :ok
    else
      render json: { message: okta_config.errors.full_messages.to_sentence, is_valid_token: okta_config.verify_token}, status: :bad_request
    end
  end

  def destroy
    integration_config = Integrations::Okta::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    okta_config = Integrations::Okta::Config.find_by_id(params[:id])
    if okta_config
      okta_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private

  def auth_params
    params.require(:okta_credentials).permit(:token, :name)
  end
end
