class Integrations::Azure::ConfigsController < Vendors::BaseController
  include ApplicationHelper
  include CompanyIntegrationDeletion
  require 'signet/oauth_2/client'
  require 'httparty'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    azure_service.client
    redirect_to azure_service.client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?
      token_detail = azure_service.token params[:code]
      expiry_time = Time.now + (token_detail["expires_in"].to_i - 600)

      azure_config = Integrations::Azure::Config.find_or_initialize_by(
        company_id: params[:state]) #Following OAuth convention. params[:state] is acting as company_id here.

      azure_config.assign_attributes(
        token: token_detail["access_token"],
        expires_in: expiry_time,
        refresh_token: token_detail["refresh_token"])

      azure_config.save!
    end
    redirect_to "#{protocol}#{@current_company.subdomain}.#{Rails.application.credentials.vendors_connectors_path}?direct_integration=true"
  end

  def destroy
    integration_config = Integrations::Azure::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    azure_config = Integrations::Azure::Config.find_by_id(params[:id])
    if azure_config
      azure_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def azure_service
    company_id ||= params[:state] #Following OAuth convention. params[:state] is acting as company_id here.
    company_id ||= @current_company.id
    @azure_service ||= Integrations::Azure::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company ||= Company.find_by_cache(id: params[:state]) if params[:state].present?
  end
end
