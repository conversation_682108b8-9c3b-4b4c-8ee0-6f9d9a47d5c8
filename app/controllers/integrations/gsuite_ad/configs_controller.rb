# frozen_string_literal: true

class Integrations::GsuiteAd::ConfigsController < AuthenticatedController
  set_privilege_name 'CompanyUser'

  require "google/apis/admin_directory_v1"
  require 'googleauth'
  require 'googleauth/web_user_authorizer'

  include ApplicationHelper

  before_action :authorize_write, except: [:consume]
  skip_before_action :ensure_access, only: [:consume]
  skip_before_action :authenticate_user!, only: [:consume]
  before_action :set_company, only: :consume

  SCOPE = [Google::Apis::AdminDirectoryV1::AUTH_ADMIN_DIRECTORY_USER_READONLY,
           Google::Apis::AdminDirectoryV1::AUTH_ADMIN_DIRECTORY_ORGUNIT_READONLY,
           Google::Apis::AdminDirectoryV1::AUTH_ADMIN_DIRECTORY_RESOURCE_CALENDAR_READONLY,
           "https://www.googleapis.com/auth/admin.directory.group.readonly"]
  GSUITE_AUTHORIZATION_URI = 'https://accounts.google.com/o/oauth2/auth?prompt=consent'
  GSUITE_TOKEN_CREDENTIAL_URI = 'https://oauth2.googleapis.com/token'
  GSUITE_CALLBACK = "#{Rails.application.credentials.domain_with_port}/integrations/gsuite_directory/oauth2callback"
  GSUITE_OOB_URI = "#{Rails.application.credentials.domain_with_port}/integrations/gsuite_directory/authorize"

  def authorize
    company_id = current_company.id
    authorizer = gsuite_service_authorizer company_id
    credentials = authorizer.get_credentials company_id
    if credentials.nil?
      connect(company_id, params[:request_from])
    else
      redirect_to "#{build_company_url(current_company)}company/users/sync_and_download"
    end
  end

  def consume
    gsuite_redirect_path = "#{build_company_url(current_company)}company/users/sync_and_download"

    if params[:code].present?
      file_path = Rails.root.join("tmp/#{@company_id}_ad_token.yaml")
      File.delete(file_path) if File.exist?(file_path)

      authorizer = gsuite_service_authorizer @company_id
      credentials = authorizer.get_and_store_credentials_from_code(
                    user_id: @company_id,
                    code: params[:code],
                    base_url: gsuite_oob_uri)

      file = File.file?(file_path)
      if file
        file = File.open(file_path)
        file_data = file.read
        expiry_time = file_data.split('expiration_time_millis')[1].gsub("\":", "").gsub("}'\n", "")
      else
        expiry_time = 1568902599000
      end

      gsuite = Integrations::GsuiteAd::Config.find_or_initialize_by(company_id: @company_id)
      gsuite.assign_attributes(token: credentials.access_token,
                               scope: credentials.scope,
                               code: params[:code],
                               refresh_token: credentials.refresh_token,
                               client_id: credentials.client_id,
                               expiration_time: expiry_time)

      gsuite.save!

      gsuite_redirect_path = "#{gsuite_redirect_path}?show_directory_modal=true"
    end

    redirect_to gsuite_redirect_path
  end

  def destroy
    gsuite_config = Integrations::GsuiteAd::Config.find_by_id(params[:id])

    if gsuite_config&.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    gsuite_config = Integrations::GsuiteAd::Config.find_by_id(params[:id])
    if gsuite_config
      gsuite_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  def gsuite_ad_integration
    config = @current_company.gsuite_ad_config
    if config.present?

      company_integration = config.company_integration
      status = company_integration.status
      sync_status = company_integration.sync_status
      last_synced_at = company_integration.last_synced_at
      error_message = company_integration.error_message

      render json: {response: true,
                    sync_status: sync_status,
                    error_message: error_message,
                    company_channel_key: current_company.guid,
                    gsuite_ad_id: config.id,
                    last_synced_at: last_synced_at,
                    selected_groups: config.group_ids,
                    selected_orgs: config.organization_paths,
                    sync_all_users: config.sync_all_users,
                    excluded_attributes: config.excluded_attributes,
                    }, status: :ok
    else
      render json: { response: false }, status: :ok
    end
  end

  def sync_gsuite_ad
    if @current_company.gsuite_ad_config.present?
      groups = params[:selected_groups] || []
      orgs = params[:selected_orgs] || []
      sync_all_users = params[:sync_all_users] === true
      excluded_attributes = params[:excluded_attributes] || []
      is_resync = params[:is_resync] === true

      if is_resync
        Integrations::GsuiteAd::FetchGroupsAndOrg.perform_async(@current_company.gsuite_ad_config.id)
      end
      
      save_groups_and_orgs orgs, groups, sync_all_users

      intg = Integration.find_by(name: 'gsuite_ad')
      comp_intg = CompanyIntegration.find_or_initialize_by(
        integrable: @current_company.gsuite_ad_config,
        integration_id: intg.id,
        company_id: @current_company.id
      )
      comp_intg.assign_attributes(status: true, sync_status: :pending)
      comp_intg.save!

      Integrations::GsuiteAd::SyncDataWorker.perform_async(
        @current_company.gsuite_ad_config.id, 
        true, 
        groups, 
        orgs, 
        sync_all_users,
        excluded_attributes,
        is_resync
      )
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  def sync_gsuite_ad_data
    gsuite_config = Integrations::GsuiteAd::Config.find(params[:config_id])
    render json: { gsuite: gsuite_config.data }, status: :ok
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: "Gsuite config data not found" }, status: :not_found
  end

  def company_channel_key
    render json: { company_channel_key: current_company.guid }, status: :ok
  end

  private
  def gsuite_service_authorizer company_id
    Integrations::GsuiteAd::FetchData.new(company_id, "write").get_authorizer(company_id, "write")
  end

  def gsuite_service company_id
    Integrations::Gsuite::FetchData.new(company_id, "write")
  end

  def save_groups_and_orgs orgs, groups, sync_all_users
    gsuite_config = Integrations::GsuiteAd::Config.find_by_id(@current_company.gsuite_ad_config.id)

    gsuite_config.groups.destroy_all
    gsuite_config.organizations.destroy_all

    if sync_all_users
      gsuite_config.update_column('sync_all_users', true)
    else
      gsuite_config.update_column('sync_all_users', false)

      orgs.each do |org_unit_path|
        new_org = Integrations::GsuiteAd::Organization.new(
          org_unit_path: org_unit_path,
          company_id: @current_company.id,
          config_id: gsuite_config.id
        )
        new_org.save!
      end
  
      groups.each do |group_id|
        new_grp = Integrations::GsuiteAd::Group.new(
          external_id: group_id,
          company_id: @current_company.id,
          config_id: gsuite_config.id
        )
        new_grp.save!
      end
    end
  end

  def connect company_id, request_from
    client = Signet::OAuth2::Client.new(
      authorization_uri: GSUITE_AUTHORIZATION_URI,
      token_credential_uri:  GSUITE_TOKEN_CREDENTIAL_URI,
      client_id: Rails.application.credentials.gsuite[:client_id],
      client_secret: Rails.application.credentials.gsuite[:client_secret],
      scope: SCOPE,
      redirect_uri: gsuite_callback,
      state: "#{company_id}, #{request_from}") #Following OAuth convention. params[:state] is acting as company_id here.
    redirect_to client.authorization_uri.to_s
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    return redirect_to root_path if params[:state].blank?
    @company_id = params[:state]
    @current_company = Company.find_by_cache(id: @company_id)
  end

  def gsuite_callback
    "#{Rails.application.credentials.domain_with_port}/integrations/gsuite_directory/oauth2callback"
  end

  def gsuite_oob_uri
    "#{build_secure_url}integrations/gsuite_ad/authorize"
  end
end
