class Integrations::JamfPro::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper

  before_action :ensure_config, only: [:destroy, :deactivate]

  def create
    token_detail = jamf_pro_service.token(auth_params)

    if token_detail['token'].present?
      jamf_pro_config = Integrations::JamfPro::Config.find_or_initialize_by(company_id: @current_company.id)

      jamf_pro_config.assign_attributes(
        instance_name: auth_params['instance_name'],
        username: auth_params['username'],
        password: auth_params['password'],
        token: token_detail['token'],
        expires_in: token_detail['expires']
      )
      jamf_pro_config.company_user_id = current_company_user&.id
      if jamf_pro_config.save
        render json: { message: 'Jamf Pro configuration successfully saved.'  }, status: :ok
      else
        render json: { message: jamf_pro_config.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      render json: { message: 'Credentials are invalid.' }, status: :not_found
    end

  end

  def destroy
    company_integration = @jamf_pro_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deleting. Please try again' }, status: :unprocessable_entity
    end
  end

  def deactivate
    if @jamf_pro_config.company_integration.update(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: 'Integration deactivated successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deactivating. Please try again' }, status: :unprocessable_entity
    end
  end

  private

  def auth_params
    params.require(:jamf_pro_data).permit(:instance_name, :username, :password)
  end

  def jamf_pro_service
    @jamf_pro_service ||= Integrations::JamfPro::FetchData.new(@current_company.id)
  end

  def ensure_config
    @jamf_pro_config ||= Integrations::JamfPro::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: 'Config was not found.' }, status: :not_found }
    end
  end
end
