class Integrations::Gsuite::ConfigsController < Vendors::BaseController
  include CompanyIntegrationDeletion
  require 'google/apis/admin_reports_v1'
  require "google/apis/admin_directory_v1"
  require 'googleauth'
  require 'googleauth/web_user_authorizer'

  before_action :authorize_write, except: [:consume]
  skip_before_action :ensure_access, only: [:consume]
  skip_before_action :authenticate_user!, only: [:consume]

  SCOPE = [Google::Apis::AdminReportsV1::AUTH_ADMIN_REPORTS_USAGE_READONLY,
           Google::Apis::AdminDirectoryV1::AUTH_ADMIN_DIRECTORY_USER_READONLY,
           Google::Apis::AdminDirectoryV1::AUTH_ADMIN_DIRECTORY_USER_SECURITY,
           "https://www.googleapis.com/auth/apps.licensing"]
  GSUITE_AUTHORIZATION_URI = 'https://accounts.google.com/o/oauth2/auth?prompt=consent'
  GSUITE_TOKEN_CREDENTIAL_URI = 'https://oauth2.googleapis.com/token'

  def authorize
    company_id = current_company.id
    authorizer = gsuite_service_authorizer company_id
    credentials = authorizer.get_credentials company_id
    if credentials.nil?
      connect(company_id, params[:request_from])
    else
      redirect_to "#{build_company_url(current_company)}vendors/sync_accounts"
    end
  end

  def consume
    company_id = params[:state] unless params[:state].nil? #Following OAuth convention. params[:state] is acting as company_id here.

    company = Company.find_by_cache(id: company_id)
    if params[:state].split(", ")[1] == "users"
      gsuite_redirect_path = "#{build_company_url(company)}company/users/sync_and_download"
      ad_enabled = true
    else
      gsuite_redirect_path = "#{build_company_url(company)}vendors/sync_accounts?direct_integration=true"
      ad_enabled = false
    end

    if params[:error] == "access_denied"
      redirect_to gsuite_redirect_path 
      return
    end

    file_path = Rails.root.join("tmp/#{company_id}_token.yaml")
    File.delete(file_path) if File.exist?(file_path)

    authorizer = gsuite_service_authorizer company_id
    credentials = authorizer.get_and_store_credentials_from_code(
                  user_id: company_id,
                  code: params[:code],
                  base_url: gsuite_oob_uri)

    file = File.file?(file_path)
    if file
      file = File.open(file_path)
      file_data = file.read
      expiry_time = file_data.split('expiration_time_millis')[1].gsub("\":", "").gsub("}'\n", "")
    else
      expiry_time = *************
    end

    gsuite = Integrations::Gsuite::Config.find_or_initialize_by(company_id: company_id)
    gsuite.assign_attributes(token: credentials.access_token,
                             scope: credentials.scope,
                             code: params[:code],
                             refresh_token: credentials.refresh_token,
                             client_id: credentials.client_id,
                             expiration_time: expiry_time,
                             ad_enabled: ad_enabled)

    gsuite.save!

    redirect_to gsuite_redirect_path
  end

  def destroy
    gsuite_config = Integrations::Gsuite::Config.find_by_id(params[:id])
    service = gsuite_service gsuite_config.company.id
    service.refresh_token
    gsuite_config.reload
    service =  gsuite_service gsuite_config.company.id
    # service.revoke_permission?(gsuite_config.token)
    if gsuite_config
      enqueue_company_integration_deletion(gsuite_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    gsuite_config = Integrations::Gsuite::Config.find_by_id(params[:id])
    if gsuite_config
      gsuite_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private
  def gsuite_service_authorizer company_id
    Integrations::Gsuite::FetchData.new(company_id, "write").get_authorizer(company_id, "write")
  end

  def gsuite_service company_id
    Integrations::Gsuite::FetchData.new(company_id, "write")
  end

  def connect company_id, request_from
    client = Signet::OAuth2::Client.new(
      authorization_uri: GSUITE_AUTHORIZATION_URI,
      token_credential_uri:  GSUITE_TOKEN_CREDENTIAL_URI,
      client_id: Rails.application.credentials.gsuite[:client_id],
      client_secret: Rails.application.credentials.gsuite[:client_secret],
      scope: SCOPE,
      redirect_uri: gsuite_callback,
      state: "#{company_id}, #{request_from}") #Following OAuth convention. params[:state] is acting as company_id here.
    redirect_to client.authorization_uri.to_s
  end

  def gsuite_callback
    "#{Rails.application.credentials.domain_with_port}/integrations/gsuite/oauth2callback"
  end

  def gsuite_oob_uri
    "#{build_secure_url}/integrations/gsuite/authorize"
  end
end
