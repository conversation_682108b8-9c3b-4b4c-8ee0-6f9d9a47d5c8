class Integrations::OneDrive::ConfigsController < AuthenticatedController
  def get_credentials
    render json: Rails.application.credentials.one_drive[:client_id], status: :ok
  end

  def should_display_drive_import
    # For all companies: just enable feature
    # For specific company: add company_id in company_ids column
    service_option = ServiceOption.find_by(service_name: 'knowledge_base_one_drive_integration')
    should_display = !service_option.status || service_option.company_ids.include?(scoped_company.id)
    render json: { display_status: should_display }, status: :ok 
  end
end
