class Integrations::Xero::ConfigsController < Vendors::BaseController
  include ApplicationHelper
  include CompanyIntegrationDeletion

  require 'xeroizer'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  # space separated, see all scopes at https://developer.xero.com/documentation/oauth2/scopes.
  # note that `offline_access` is required to get a refresh token, otherwise the access only lasts for 30 mins and cannot be refreshed.
  SCOPE = "accounting.transactions.read offline_access"

  def consent
    authorization_url = xero_client.authorize_url(redirect_uri: callback_url,
                                                  state: current_company.id,
                                                  scope: SCOPE)
    redirect_to authorization_url
  end

  def callback_url
    "#{Rails.application.credentials.domain_with_port}/integrations/xero/oauth2callback"
  end

  def consent_callback
    if params[:code].present?
      token_detail = xero_client.authorize_from_code(params[:code], redirect_uri: callback_url).to_hash

      expiry_time = Time.now + (token_detail[:expires_at].to_i - 600)

      xero_config = Integrations::Xero::Config.find_or_initialize_by(company_id: current_company.id)
      xero_config.assign_attributes(token: token_detail[:access_token],
                                    expires_in: expiry_time,
                                    refresh_token: token_detail[:refresh_token],
                                    tenant_ids: tenant_ids)
      xero_config.save!
    end

    redirect_to "#{build_company_url(current_company)}vendors/sync_accounts?direct_integration=true"
  end

  def destroy
    integration_config = Integrations::Xero::Config.find_by_id(params[:id])
    if integration_config
      enqueue_company_integration_deletion(integration_config)
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    xero_config = Integrations::Xero::Config.find_by_id(params[:id])
    if xero_config
      xero_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  private

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    return redirect_to root_path if params[:state].blank?
    @current_company = Company.find_by_cache(id: params[:state])
  end

  def tenant_ids
    tenants = xero_client.current_connections
    tenants.map { |t| t.tenant_id }
  end

  def xero_client
    @xero_client ||= begin
      Xeroizer::OAuth2Application.new(
        Rails.application.credentials.xeroizer[:client_id],
        Rails.application.credentials.xeroizer[:client_secret]
      )
    end
  end
end
