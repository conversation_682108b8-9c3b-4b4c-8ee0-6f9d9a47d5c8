# frozen_string_literal: true

class Integrations::AzureAd::ConfigsController < AuthenticatedController
  set_privilege_name 'CompanyUser'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent_callback]

  def consent
    client = azure_ad_service.client(params[:request_from], params[:allow_guest_users])
    redirect_to client.authorization_uri.to_s
  end

  def consent_callback
    azure_ad_redirect_path = "#{build_company_url(current_company)}company/users/sync_and_download"
    if current_company.present? && params[:code].present?
      token_detail = azure_ad_service.token(params[:code])
      expiry_time = Time.now + (token_detail["expires_in"] - 600)
      azure_ad_config = Integrations::AzureAd::Config.find_or_initialize_by(
        company_id: params[:state]) #Following OAuth convention. params[:state] is acting as company_id here.
      allow_guest_users = params[:state].split(", ")[2] == "true"

      azure_ad_config.assign_attributes(
        token: token_detail["access_token"],
        expires_in: expiry_time,
        refresh_token: token_detail["refresh_token"],
        allow_guest_users: allow_guest_users)

      azure_ad_config.save!
      azure_ad_redirect_path = "#{azure_ad_redirect_path}?show_azure_ad_groups_modal=true"

    end

    redirect_to azure_ad_redirect_path
  end

  def destroy
    if Integrations::AzureAd::Config.find_by_id(params[:id])&.company_integration&.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def azure_ad_integration
    if @current_company.azure_ad_config.present?

      company_integration = @current_company.azure_ad_config.company_integration
      status = company_integration.status
      sync_status = company_integration.sync_status
      last_synced_at = company_integration.last_synced_at
      error_message = company_integration.error_message
      excluded_attributes = @current_company.azure_ad_config.excluded_attributes
      selected_groups = @current_company.azure_ad_config.group_ids
      sync_all_users = @current_company.azure_ad_config.sync_all_users

      render json: { response: true,
                    sync_status: sync_status,
                    error_message: error_message,
                    azure_ad_config_id: @current_company.azure_ad_config.id,
                    company_channel_key: current_company.guid,
                    last_synced_at: last_synced_at,
                    selected_groups: selected_groups,
                    sync_all_users: sync_all_users,
                    excluded_attributes: excluded_attributes,
                  }, status: :ok
    else
      render json: { response: false }, status: :ok
    end
  end

  def sync_azure_ad_data
    azure_ad_config = Integrations::AzureAd::Config.find(params[:config_id])
    config_json = get_azure_ad_data(azure_ad_config.data, azure_ad_config.excluded_attributes)
    render json: { azure_ad: config_json}, status: :ok
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: "Azure config data not found" }, status: :not_found
  end

  def sync_azure_ad
    if @current_company.azure_ad_config.present?
      groups = params[:selected_groups] || []
      sync_all_users = params[:sync_all_users] == "true"
      save_groups(groups, sync_all_users)
      excluded_attributes = params[:excluded_attributes] || []
      is_resync = params[:is_resync] == "true"

      if is_resync
        Integrations::AzureAd::FetchGroupsWorker.perform_async(@current_company.azure_ad_config.id)
      end

      intg = Integration.find_by(name: 'azure_ad')
      comp_intg = CompanyIntegration.find_or_initialize_by(
        integrable: @current_company.azure_ad_config,
        integration_id: intg.id,
        company_id: @current_company.id
      )
      comp_intg.assign_attributes(status: true, sync_status: :pending)
      comp_intg.save!

      Integrations::AzureAd::SyncDataWorker.perform_async(
        @current_company.azure_ad_config.id,
        true,
        groups,
        sync_all_users,
        excluded_attributes,
        is_resync
      )
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  private
  def azure_ad_service
    # Following OAuth convention. params[:state] is acting as company_id here.
    company_id = params[:state] ? params[:state].split(", ")[0].to_i : @current_company.id
    @azure_ad_service ||= Integrations::AzureAd::FetchData.new(company_id)
  end

  def set_company
    #Following OAuth convention. params[:state] is acting as company_id here.
    @current_company = Company.find_by_cache(id: params[:state]) if params[:state].present?
  end

  def save_groups(groups, sync_all_users)
    azure_ad_config = Integrations::AzureAd::Config.find_by_id(@current_company.azure_ad_config.id)
    azure_ad_config.groups.destroy_all

    if sync_all_users
      azure_ad_config.update_column('sync_all_users', true)
    else
      azure_ad_config.update_column('sync_all_users', false)

      groups.each do |group_id|
        new_grp = Integrations::AzureAd::Group.find_or_initialize_by(
          external_id: group_id,
          company_id: @current_company.id,
          config_id: azure_ad_config.id
        )
        new_grp.save!
      end
    end
  end

  def get_azure_ad_data(azure_ad_data, excluded_attributes)
    if azure_ad_data.present?
      page = params[:page].to_i
      per_page = 100
      start_index = page * per_page
      end_index = start_index + per_page
      azure_ad_data["total_records"] = azure_ad_data["groups"].count
      azure_ad_data["pages"] = (azure_ad_data["total_records"] / per_page) + 1
      if params[:searchTerm].present?
        search_term = params[:searchTerm].downcase
        azure_ad_data["groups"] = azure_ad_data["groups"].select do |group|
          group["name"].downcase.include?(search_term)
        end
        azure_ad_data["total_records"] = azure_ad_data["groups"].count
        azure_ad_data["pages"] = (azure_ad_data["total_records"] / per_page) + 1
      end

      azure_ad_data["groups"] = azure_ad_data["groups"][start_index...end_index] if azure_ad_data["total_records"] > per_page
      azure_ad_data["excluded_attributes"] = excluded_attributes
      azure_ad_data
    end
  end
end
