class Sla::<PERSON><PERSON>esController < AuthenticatedController
  before_action :set_resource, only:[:destroy, :update]

  def index
    respond_to do |format|
      format.json { render json: sla_policies }
      format.html { }
    end
  end

  def show
    sla_details = resource.details&.order("sla_details.created_at ASC")
    sla_emails = map_emails_with_contributors(resource.emails)
    sla_conditions = resource.conditions&.order(id: :asc).map { |condition| condition_json(condition) }
    custom_form = { id: resource.custom_form.id, name: resource.custom_form.form_name, label: 'Form' }
    policy = {
      policy: resource,
      custom_form: custom_form,
      details: sla_details,
      emails: sla_emails,
      workspace_id: resource.custom_form.workspace_id,
      conditions: sla_conditions,
    }
    respond_to do |format|
      format.json { render json: policy }
      format.html { }
    end
  end

  def create
    create_policy
  end

  def update
    update_policy
  end

  def destroy
    if resource.destroy
      render json: resource, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def ordering
    id_lookup = { }
    params[:policies].each do |policy_params|
      id_lookup[policy_params[:id].to_i] = policy_params[:order]
    end
    Sla::Policy.where(id: id_lookup.keys).find_each do |policy|
      policy.update_columns(order: id_lookup[policy.id]) if id_lookup[policy.id]
    end

    render json: sla_policies
  end

  def has_base_policy?
    base_condition = Sla::Policy
      .left_joins(:conditions)
      .find_by(conditions: { id: nil }, company: scoped_company, custom_form_id: params[:custom_form_id]).present?
    respond_to do |format|
      format.json { render json: base_condition }
    end
  end

  private
  def update_policy
    ActiveRecord::Base.transaction do
      param = params[:params]
      if resource.present?
        validate_conditions(param[:policy], param[:conditions])
        resource.update!(
          name: param[:policy]["name"],
          description: param[:policy]["description"],
          custom_form_id: param[:policy]["custom_form"]["id"]
        )
        Sla::ConditionUpdate.new(resource, param[:conditions], scoped_company_user.id).update(param[:custom_form_changed], param[:removed_condition_ids])
        Sla::DetailUpdate.new(resource, param[:details], scoped_company_user.id).update(param[:custom_form_changed], param[:removed_target_ids])
        Sla::EmailUpdate.new(resource, param[:emails], scoped_company_user.id).call(param["updated_ids"]) if param[:emails].present?
        remove_emails(param["remove_email_ids"])

        render json: resource, status: :ok
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end
  end

  def create_policy
    ActiveRecord::Base.transaction do
      param = params[:params]
      new_policy = param[:policy]
      validate_conditions(new_policy, param[:conditions])
      @policy = scoped_company.policies.new(
        name: new_policy["name"],
        description: new_policy["description"],
        custom_form_id: new_policy["custom_form"]["id"]
      )
      @policy.set_params(params, scoped_company_user.id)
      @policy.save!
      Sla::ConditionUpdate.new(@policy, param[:conditions], scoped_company_user.id).create
      Sla::DetailUpdate.new(@policy, param[:details], scoped_company_user.id).create
      Sla::EmailUpdate.new(@policy, param[:emails], scoped_company_user.id).call if param[:emails].present?

      render json: @policy, status: :ok if @policy.present?
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end
  end

  def sla_policies
    sla_policies = Sla::Policy.where(company: scoped_company, custom_form_id: custom_form_ids)
                              .order(:custom_form_id, :order)
                              .includes(:custom_form)
    custom_form_names = sla_policies.map { |policy| [policy.custom_form_id, policy.custom_form.form_name] }
    policies_with_blank_conditions, policies_with_conditions = sla_policies.partition { |policy| policy.conditions.blank? }
    policies_with_blank_conditions = group_by_custom_form(policies_with_blank_conditions)
    policies_with_conditions = group_by_custom_form(policies_with_conditions)
    { sla_policies: policies_with_conditions, base_policies: policies_with_blank_conditions, custom_form_names: custom_form_names.to_h }
  end
  
  def resource
    @resource ||= scoped_company.policies.find_by(id: params[:id])
  end

  def validate_conditions(policy, conditions)
    custom_form_id = policy["custom_form"]["id"]
    policies = Sla::Policy.where(custom_form_id: custom_form_id).where.not(id: policy["id"])
    blank_conditions_count = policies.count { |policy| policy.conditions.blank? }
    raise "policy having no conditions with the same custom form already exists" if blank_conditions_count > 0 && conditions.blank?
  end

  def custom_form_ids
    @ids = CustomForm.where(workspace: scoped_workspace).pluck(:id)
  end

  def remove_emails(ids)
    ids&.each do |id|
      sla_email = Sla::Email.find_by(id: id)
      sla_email.current_user_id = scoped_company_user.id
      sla_email&.destroy
    end
  end

  def map_emails_with_contributors(sla_emails)
    contributor_ids = sla_emails.flat_map(&:target).uniq
    contributors_by_id = Contributor.where(id: contributor_ids).index_by(&:id)
    emails = sla_emails.map do |email|
      contributors = []
      if email.target.include?("assigned")
        contributors << {
          id: "assigned",
          name: "Agent Assigned",
          email: "assigned",
          type: "assigned",
          avatar_thumb_url: nil,
          root_id: nil
        }
      end
      contributors_target_ids = email.target - ["assigned"]
      contributors_target_ids.each do |target_id|
        cont = contributors_by_id[target_id.to_i]
        if cont.present?
          contributors << {
            id: cont.id,
            name: cont.name,
            email: cont.email,
            type: cont.contributor_type,
            avatar_thumb_url: cont.avatar_thumb_url,
            root_id: cont.root_id
          }
        end
      end
      {
        id: email.id,
        email_type: email.email_type,
        subject: email.subject,
        target: contributors,
        email_body: email.email_body,
        approaches_in: email.approaches_in,
        name: email.name,
      }
    end
    emails
  end  

  def set_resource
    resource.set_params(params, scoped_company_user.id)
  end

  def group_by_custom_form(policies)
    policies.group_by(&:custom_form_id)
  end

  def condition_json(policy_condition)
    {
      id: policy_condition.id,
      condition_type: policy_condition.condition_type,
      field_type: policy_condition.field_type,
      value: JSON.parse(policy_condition.value)
    }
  end
end
