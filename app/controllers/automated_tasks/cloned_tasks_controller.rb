module AutomatedTasks
  class ClonedTasksController < Authenticated<PERSON>ontroller
    def create
      contributor = scoped_company_user.contributor
      task = build_task

      task.order = next_available(:order)
      task.serial_number = next_available(:serial_number)

      if task.save
        render json: task_json(task), status: :ok
      else
        render json: task.errors.full_messages.to_sentence, status: :unprocessable_entity
      end
    end

    private
    def id
      params[:id]
    end

    def load_resource
      scoped_workspace.automated_tasks.includes(task_events: { event_details: :event_subject_type }, task_actions: :action_type).find_by(id: id)
    end

    def resource
      @resource ||= load_resource
    end

    def task_json(task = resource)
      AutomatedTasks::JsonOutput.new(scoped_company).json(task)
    end

    def build_task
      creator = AutomatedTasks::Clone.new
      task = creator.call(resource)
      task.set_params(nil, scoped_company_user.id, params)
      task
    end

    def next_available(attribute)
      existing_values = scoped_workspace.automated_tasks.pluck(attribute)
      existing_values.any? ? existing_values.max + 1 : 1
    end
  end
end
