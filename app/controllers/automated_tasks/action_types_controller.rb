module AutomatedTasks
  class ActionTypesController < ApplicationController
    def index
      render json: actions
    end

    def details
      if params[:module]
        AutomatedTasks::ActionType.where(module: params[:module]).map do |a|
          {
            id: a.id,
            name: a.name.gsub(/[\[\]]/, ''),
            actionClass: a.action_class,
            model: a.model,
          }
        end
      elsif params[:model]
        AutomatedTasks::ActionType.where(model: params[:model].uniq).map do |a|
          {
            id: a.id,
            name: a.name.gsub(/[\[\]]/, ''),
            actionClass: a.action_class,
            model: a.model,
          }
        end
      else
        []
      end
    end

    def actions
      if params['model'] && params['model'].uniq.count > 1
        common_action_threshold = params['model'].uniq.count - 1
        common_actions = details.group_by { |action| action[:actionClass] }.values.map do |value|
          value.first if value.count > common_action_threshold
        end.compact
        return common_actions
      end
      return details
    end
  end
end