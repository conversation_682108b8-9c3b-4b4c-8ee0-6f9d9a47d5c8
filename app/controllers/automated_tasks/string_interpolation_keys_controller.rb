module AutomatedTasks
  class StringInterpolationKeysController < AuthenticatedController
    before_action :set_resource, only: [:key_interpolation]

    def index
      render json: interpolate.keys
    end

    def key_interpolation
      response = StringInterpolate.new(@object).call(params[:key])
      render json: response
    end

    private

    def set_resource 
      if params[:entity_id].present? && params[:entity_class].present?
        @object = params[:entity_class].constantize.find(params[:entity_id])
      else
        render json: {}, status: :not_found
      end
    end

    def interpolate
      @interpolate ||= StringInterpolate.new(stub)
    end

    def help_ticket
      HelpTicket.new(company: scoped_company, workspace: scoped_workspace)
    end

    def stub(key_type = params[:type])
      @stub ||= begin
        case key_type
        when 'HelpTicket' then help_ticket
        when 'ProjectTask' then ProjectTask.new(help_ticket: help_ticket, company: scoped_company)
        when 'TimeSpent' then TimeSpent.new(help_ticket: help_ticket, company_user: scoped_company.admin_company_users.first)
        when 'CustomFormValue' then CustomFormValue.new(module: help_ticket, company: scoped_company)
        when 'HelpTicketComment' then HelpTicketComment.new(help_ticket: help_ticket)
        when 'CustomFormAttachment' then help_ticket
        else
          nil
        end
      end
    end
  end
end
