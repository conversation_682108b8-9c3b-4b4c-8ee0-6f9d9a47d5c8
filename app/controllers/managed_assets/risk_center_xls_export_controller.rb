module ManagedAssets
  class RiskCenterXlsExportController < BaseXlsController
    set_privilege_name("ManagedAsset")

    def index
      workbook = export_class.new(scoped_company, options, params).export
      send_data workbook.stream.string, filename: "#{scoped_company.subdomain}_asset_risk_center_#{options['export_type']}_data.xlsx",
                                        disposition: 'attachment'
    end

    def export_class
      ImportExport::Assets::RiskCenterXlsExport
    end

    def options
      JSON.parse(params[:options])
    end
  end
end
