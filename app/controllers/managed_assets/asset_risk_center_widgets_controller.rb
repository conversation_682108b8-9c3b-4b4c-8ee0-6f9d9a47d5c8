module ManagedAssets
  class AssetRiskCenterWidgetsController < AuthenticatedController
    include ManagedAssetRiskCenterHelper
    around_action :set_read_replica_db, only: [:risk_center_widgets_summary_data]

    def index
      widgets = AssetRiskCenterWidget.where(company_id: scoped_company.id)
      all_widgets = widgets.map do |widget|
        { name: widget.name, id: widget.id, enabled: widget.enabled }
      end
      
      render json: { all_widgets: all_widgets }
    end

    def widgets_summary_data
      begin
        assets_by_data_service = ManagedAssets::RiskCenter::AssetsByData.new(widgets_resources)

        if params[:widget_data_list] == 'true'
          render json: { widget_data_list: assets_by_data_service.get_widget_list_data }
        elsif params[:filter_name].present?
          render json: { widgets_summary_data_list: assets_by_data_service.widget_summary_data_list }
        elsif params[:asset_type_counts] == 'true'
          render json: { assets_count: get_managed_assets_count }
        else
          render json: { widgets_summary_data: assets_by_data_service.widgets_summary_data }
        end
      rescue StandardError => e
        render json: { message: e.message }, status: :unprocessable_entity
      end
    end

    def summary
      render json: { asset_risk_summaries: ManagedAssets::RiskCenter::AssetsByData.new(widgets_resources).asset_risk_summaries }
    end

    def widget_data
      render json: { widget_data: ManagedAssets::RiskCenter::AssetsByData.new(widgets_resources).call }
    end

    def create
      if (AssetRiskCenterWidget.exists?(['(name ILIKE ? OR label ILIKE ?) AND company_id = ?', widget_params[:name], widget_params[:label], scoped_company.id]))
        render json: { message: 'Widget with the same name already exists.' }, status: :conflict
        return
      end

      widget = AssetRiskCenterWidget.new(widget_params.merge(company_id: scoped_company.id))

      if widget.save
        render json: { message: 'Widget created successfully!', widget: widget }, status: :ok
      else
        render json: { message: 'Unable to save widget', errors: widget.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def create_widget_custom_option
      if RiskCenterWidgetOption.exists?(
        ['company_id = ? AND name ILIKE ?', scoped_company.id, params[:option]]
      )
        render json: { message: 'Option already exists.' }, status: :conflict
        return
      end

      widget_option = RiskCenterWidgetOption.new(
        widget_type: params[:type],
        name: params[:option],
        company_id: scoped_company.id
      )
      
      if widget_option.save
        render json: { message: 'Option added successfully!', option: params[:option] }, status: :ok
      else
        render json: { message: 'Failed to save the widget option.' }, status: :unprocessable_entity
      end
    end

    def update
      widget = AssetRiskCenterWidget.find(params[:id])

      if widget.update(widget_params)
        render json: { widget: widget }, status: :ok
      else
        render json: { message: 'Unable to update widget', errors: widget.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def destroy
      deleted_widget = AssetRiskCenterWidget.find_by(id: params[:id])
      return render json: { message: 'Widget not found' }, status: :not_found unless deleted_widget

      if deleted_widget.destroy
        render json: {}, status: :ok
      else
        render json: { message: deleted_widget.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def custom_widget_options
      is_custom = params[:is_custom] === "true"
      options = ManagedAssets::RiskCenter::AssetsByData.new(widgets_resources).get_all_options(params[:tab], is_custom, params[:widget_type])
      render json: { options: options , status: :ok }
    end

    def should_display_asset_risk_center
      # Hide risk center for all companies - when service option status is true
      # Hide risk center for specific companies - based on company_ids column
      # Display risk center for all companies - when service option status is false
      service_option = ServiceOption.find_by(service_name: "asset_risk_center_sections")
      hide_risk_center = service_option.status || service_option.company_ids.include?(scoped_company.id)
      render json: { display_risk_center: !hide_risk_center, status: :ok }
    end

    def has_agent_installed
      agent_installed = AgentLocation.exists?(company_id: scoped_company.id)

      render json: { agent_installed: agent_installed, status: :ok }
    end

    private

    def get_managed_assets_count
      result = assets_by_type(params, scoped_company).group('company_asset_types.name').count

      result.map { |name, count| { name: name, count: count } }
    end

    def widgets_resources
      {
        params: params,
        scoped_company: scoped_company,
      }
    end

    def widget_params
      params
        .require(:asset_risk_center_widget)
        .permit(
          :name,
          :label,
          :description, 
          :icon, 
          :is_custom,
          :enabled,
          :option, 
          selected_options: []
        )
    end
  end
end
