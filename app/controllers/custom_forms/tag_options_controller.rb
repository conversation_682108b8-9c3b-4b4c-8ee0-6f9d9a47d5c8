class CustomForms::TagOptionsController < AuthenticatedController
  def index
    tags = []
    helpdesk_custom_forms_ids = scoped_company.custom_forms.where(company_module: 'helpdesk').pluck(:id)
    tags = CustomFormValue.includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: "tag" }, custom_form_id: helpdesk_custom_forms_ids)
      .distinct
      .order('value_str ASC')
      .pluck(:value_str)
    render json: tags, status: :ok
  end
end
