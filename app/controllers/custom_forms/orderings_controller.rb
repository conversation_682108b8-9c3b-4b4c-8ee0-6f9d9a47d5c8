module CustomForms
  class OrderingsController < AuthenticatedController
    def update
      id_lookup = {}
      params[:forms].each do |form_params|
        id_lookup[form_params[:id].to_i] = form_params[:order]
      end
      CustomForm.where(id: id_lookup.keys).find_each do |form|
        form.update_column('order', id_lookup[form.id]) if id_lookup[form.id]
      end
      forms = CustomForms::FormsQuery.new(form_query_params).call
      render json: forms
    end

    def form_query_params
      {
        company_id: scoped_company.id,
        workspace_id: scoped_workspace.id,
        status: params[:status],
        company_module: params[:company_module],
      }
    end
  end
end
