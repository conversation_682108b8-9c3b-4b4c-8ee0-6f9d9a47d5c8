class CustomForms::CategoryOptionsController < AuthenticatedController
  def index
    categories = []
    helpdesk_custom_forms_ids = scoped_company.custom_forms.where(company_module: 'helpdesk').pluck(:id)
    categories = CustomFormValue.includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: 'category' }, custom_form_id: helpdesk_custom_forms_ids)
      .distinct
      .order('value_str ASC')
      .pluck(:value_str)
    options = categories.reject { |c| c.empty? }
    render json: options, status: :ok
  end
end
