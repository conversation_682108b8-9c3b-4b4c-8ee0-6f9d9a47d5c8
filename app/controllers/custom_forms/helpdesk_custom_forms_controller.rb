module CustomForms
  class HelpdeskCustomFormsController < CustomFormsController
    def index
      helpdesk_forms = HelpdeskCustomForm.joins(:custom_form)
                                         .includes(:helpdesk_custom_email)
                                         .where('custom_forms.company_id = ?', current_company.id)
                                         .where('custom_forms.workspace_id = ?', scoped_workspace.id)
      render json: helpdesk_forms.as_json
    end

    # Currently only used to update email and custom email. Can be expanded if necessary.
    def update
      ActiveRecord::Base.transaction do
        @helpdesk_custom_form = HelpdeskCustomForm.includes(:custom_form, :helpdesk_custom_email).find(params[:id])
        @form = helpdesk_custom_form.custom_form
        helpdesk_custom_form.assign_attributes(form_params)
        update_helpdesk_custom_email
        helpdesk_custom_form.save!
        render json: helpdesk_custom_form.as_json, status: :ok
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue => e
        Rails.logger.error("Transaction failed: #{e.message}")
        render json: { message: e }, status: :unprocessable_entity
        raise ActiveRecord::Rollback
      end
    end

    private

    def email
      custom_email_params[:email]&.downcase
    end

    def custom_email_params
      params.require(:helpdesk_custom_form).require(:helpdesk_custom_email_attributes).permit(
        :email,
        :name
      )
    end

    def form_params
      params.require(:helpdesk_custom_form).permit(:email)
    end
  end
end
