module Options
  class CompanyUserOptionsController < OptionsController
    skip_before_action :ensure_access
    skip_before_action :verify_mfa
    skip_before_action :ensure_free_trial_or_subscribed
    skip_before_action :ensure_confirmed_email
    skip_before_action :ensure_company_verification

    protected
    def options
      if is_cache_enabled? && validate_cache_key_requirements?
        cache_key = generate_cache_key(nil, company)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key)
          get_options
        end
      else
        get_options
      end
    end

    def validate_cache_key_requirements?
      if company.blank?
        raise 'Scoped company is not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end

    def get_options
      company_users = company.company_users.not_sample_company_user
                                   .includes(:user)
                                   .select('company_users.id', 'company_users.avatar_url', 'company_users.contributor_id', 'company_users.self_onboarding', 'company_users.user_id', 'users.first_name', 'users.last_name', 'users.email')
                                   .order('users.first_name, users.last_name')

      if company_users
        if params[:archived_users_only].present?
          company_users = company_users.where.not(archived_at: nil)
        elsif params[:archived].blank?
          company_users = company_users.where(archived_at: nil)
        end
      end
      if params[:query].present?
        search_patterns = params[:query].split(" ").map { |q| "%#{q}%" }
        company_users = company_users.where('users.first_name ilike any(array[?]) OR users.last_name ilike any(array[?])', search_patterns, search_patterns)
      end
      total_count = company_users.length
      unless params[:select_all] == 'true'
        company_users = company_users.limit(limit).offset(offset)
      end
      company_users = company_users.where.not(id: params[:excludes]) if params[:excludes].present?
      company_users.map do |company_user|
        if company_user.user
          {
            id: company_user.id,
            full_name: "#{company_user.first_name} #{company_user.last_name}",
            email: company_user.email,
            avatar_thumb_url: company_user.avatar_url,
            contributor_id: company_user.contributor_id,
            self_onboarding_status: company_user.self_onboarding,
            total_agent_count: total_count,
            contract_ids: params[:is_contract] ? company_user.contract_ids : []
          }
        end
      end
    end

    def limit
      params[:limit] || 200
    end

    def offset
      params[:offset] || 0
    end

    def company
      @company ||= begin
        if params["company_subdomain"].present?
          Company.find_by_cache(subdomain: params["company_subdomain"])
        else
          scoped_company
        end
      end
    end
  end
end

