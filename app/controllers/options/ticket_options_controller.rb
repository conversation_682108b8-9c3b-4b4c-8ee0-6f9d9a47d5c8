module Options
  class TicketOptionsController < OptionsController
    private
    def options
      scoped_company.help_tickets.active.includes(custom_form_values: :custom_form_field).joins(custom_form_values: :custom_form_field).where("custom_form_fields.name = 'subject'").select(:id).map do |help_ticket|
        {
          id: help_ticket.id,
          name: help_ticket.custom_form_values.first.value_str,
        }
      end
    end
  end
end
