module Options
  class LinkableOptionsController < OptionsController
    before_action :authenticate_user!

    private
    def options
      if is_cache_enabled? && validate_cache_key_requirements?
        workspace = is_help_desk_module? ? scoped_workspace : nil
        cache_key = generate_cache_key(workspace, scoped_company, false, is_help_desk_module?)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key, 'linkable_options')
          get_linkable_options
        end
      else
        get_linkable_options
      end
    end

    def get_linkable_options
      if is_help_desk_module?
        ::Options::LinkableOptions.call(params, scoped_company, scoped_company_user, scoped_workspace)
      else
        ::Options::LinkableOptions.call(params, scoped_company, scoped_company_user)
      end
    end

    def validate_cache_key_requirements?
      if is_help_desk_module? && scoped_workspace.blank?
        raise 'Scoped workspace is not present for cache key.'
      elsif scoped_company.blank?
        raise 'Scoped company is  not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end
  end
end
