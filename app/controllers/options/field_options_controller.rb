##
# FieldOptionsController returns the combined options for a given
# field name and field type.  This is used in Automated Tasks.
module Options
  class FieldOptionsController < OptionsController
    before_action :verify_params

    private
    def verify_params
      render json: "", status: :bad_request unless params[:name] && params[:attr_type]
    end

    def options
      my_options = nil
      form_fields.find_each do |f|
        if f.options
          options = JSON.parse(f.options)
          if options.is_a?(Array)
            my_options ||= []
            if my_options.empty?
              my_options = options
            else
              my_options &= options
            end
          elsif options.is_a?(Hash)
            my_options ||= {}
            my_options.merge!(options) { |old_val, new_val| old_val && new_val }
          end
        end
      end
      my_options
    end

    def form_fields
      @form_fields ||= begin
        CustomFormField.where(custom_form: custom_forms, name: params[:name], field_attribute_type: params[:attr_type])
      end
    end

    def custom_forms
      @custom_forms ||= begin
        forms = scoped_company.custom_forms.where(company_module: params[:module])
        forms = forms.where(id: custom_form_ids) if custom_form_ids
        forms
      end
    end

    def custom_form_ids
      params[:custom_form_id]&.split(",")&.map(&:to_i)
    end
  end
end
