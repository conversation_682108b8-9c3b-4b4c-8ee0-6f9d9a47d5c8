module Options
  class ExternalUserOptionsController < AuthenticatedController
    def index
      render json: [] unless scoped_company_user # this is just to see if they are logged in
      render json: user_options + email_options
    end

    private
    def company_contributors
      return Contributor.none if include_ids.blank? && include_emails.present?
      my_options = scoped_company.contributors.includes(:group, company_user: :user)
      my_options = my_options.where(id: include_ids) if include_ids.present?
      if query.present?
        qs = query.split(" ")
        q = qs.map { |q| "%#{q}%" }
        first = true
        sql = nil
        sql_params = []
        q.each do |q_sql|
          if first
            sql = 'users.first_name ilike ? OR users.last_name ilike ? OR groups.name ilike ?'
            first = false
          else
            sql += ' OR users.first_name ilike ? OR users.last_name ilike ? OR groups.name ilike ?'
          end
          3.times { sql_params << q_sql }
        end
        my_options = my_options.where("#{sql}", *sql_params)
      end
      my_options.order("groups.name, users.last_name, users.first_name")
    end
      
    def user_options
      @user_options ||= begin
        my_options = company_contributors.map do |contributor|
          if contributor.name && contributor.company_user.present?
            {
              id: contributor.id,
              name: contributor.name,
              type: 'User',
              email: contributor.email,
              avatar_thumb_url: contributor.avatar_thumb_url,
              root_id: contributor.root_id
            }
          else
            nil
          end
        end
        my_options.compact
      end
    end

    def email_form_values
      return CustomFormValue.none if include_ids.present? && include_emails.blank?
      form_values = scoped_company.custom_form_values.joins(:custom_form_field)
      form_values = form_values.where("custom_form_fields.field_attribute_type = ?", CustomFormField.field_attribute_types['external_user'])
      my_options = form_values.where(value_str: include_emails) if include_emails.present?

      if query.present?
        qs = query.split(" ")
        q = qs.map { |q| "%#{q}%" }
        first = true
        sql = nil
        sql_params = []
        q.each do |q_sql|
          if first
            sql = 'value_str ilike ?'
            first = false
          else
            sql += ' OR value_str ilike ?'
          end
          sql_params << q_sql
        end
        form_values = form_values.where("#{sql}", *sql_params)
      end
      form_values
    end

    def email_options
      @email_options ||= begin
        my_options = email_form_values.select(:value_str).pluck(:value_str).uniq.map do |email|
          if email.present?
            {
              id: email,
              type: 'Email',
              name: email,
            }
          else
            nil
          end
        end
        my_options.compact
      end
    end

    def query
      params[:query]
    end

    def include_emails
      @include_emails ||= begin
        ids = []
        if params[:includes].is_a?(Array)
          ids = params[:includes].grep{ |i| i =~ /\@/ }
        else
          if params[:includes] =~ /\@/
            ids << params[:includes]
          end
        end
        ids
      end
    end

    def include_ids
      @include_ids ||= begin
        ids = []
        if params[:includes].is_a?(Array)
          ids = params[:includes].map(&:to_i).select{ |i| i.is_a?(Numeric) }
        else
          if params[:includes].is_a?(Numeric)
            ids << params[:includes]
          end
        end
        ids
      end
    end
  end
end
