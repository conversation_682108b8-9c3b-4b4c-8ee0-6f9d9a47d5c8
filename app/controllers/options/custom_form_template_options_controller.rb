module Options
  class CustomFormTemplateOptionsController < AuthenticatedController
    def index
      render json: custom_form_templates
    end

    private
    def custom_form_templates
      templates = CustomFormTemplate.all
      templates = templates.where(company_module: company_module) if company_module
      templates = templates.where(is_active: true) if active
      templates = templates.select(:id, :form_name, :is_active)
      templates.map { |f| {id: f.id, name: f.form_name, is_active: f.is_active } }
    end

    def active
      params[:active]
    end

    def company_module
      params[:company_module]
    end

    def portal
      params[:portal]
    end
  end
end
