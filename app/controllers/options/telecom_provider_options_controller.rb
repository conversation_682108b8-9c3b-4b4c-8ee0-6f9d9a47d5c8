module Options
  class TelecomProviderOptionsController < OptionsController
    private
    def options
      provider_options = {}
      telecom = DefaultCategory.telecom
      DefaultVendor.select('id, name, default_category_id, url, logo_url, friendly_name')
                  .includes(:default_category)
                  .where(default_category_id: telecom.id)
                  .all
                  .each { |vendor| provider_options[vendor.name] = vendor.as_json.merge(type: 'DefaultVendor', category: vendor&.default_category, url: vendor&.url) }
      scoped_company.telecom_providers.select('id, name, url')
                    .active
                    .where.not(name: nil)
                    .each { |provider| provider_options[provider.name] = provider.as_json.merge(type: 'TelecomProvider' ) }
      sorted_options = []
      provider_options.keys.each do |key|
        sorted_options << provider_options[key]
      end
      sorted_options = sorted_options.sort_by { |provider| provider['name'].downcase }
      sorted_options
    end
  end
end
