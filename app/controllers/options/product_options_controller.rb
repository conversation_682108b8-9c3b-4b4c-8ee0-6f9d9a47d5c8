module Options
  class ProductOptionsController < AuthenticatedController
    def index
      render json: { products: sorted_options || [] }
    end

    private
    def sorted_options
      product_options&.sort_by { |product| product['name'].downcase }
    end

    def product_options
      @product_options ||= begin
        options = {}
        if vendor.present?
          products = vendor.products.includes(:default_product)
          products.each do |product|
            options[product.name] = product_data(product)
          end
        end

        if default_vendor.present?
          default_products = default_vendor.default_products.includes(:default_category).find_each do |default_product|
            options[default_product.name] ||= default_product_data(default_product)
          end
        end
        options.values.sort { |a, b| a['name'].upcase <=> b['name'] }
      end
    end

    def product_data(product)
      data = {}
      data['type'] = 'Product'
      data['id'] = product.id
      data['name'] = product.name
      data['logo_url'] = product.logo_url
      data['logo_url'] ||= product.default_product&.logo_url
      data['category'] = product.category&.as_json
      # data['tags'] = product.category.as_json
      data
    end

    def default_product_data(default_product)
      data = {}
      data['type'] = 'DefaultProduct'
      data['id'] = default_product.id
      data['name'] = default_product.name
      data['logo_url'] = default_product.logo_url
      data['category'] = default_product.default_category&.as_json
      data
    end

    def default_vendor
      return @default_vendor if @default_vendor

      if (params[:default_vendor_id])
        @default_vendor = DefaultVendor.find_by(id: params[:default_vendor_id])
      end

      if @default_vendor.blank?
        @default_vendor = DefaultVendor.find_by(name: vendor.name)
      end
      @default_vendor
    end

    def vendor
      @vendor ||= begin
        my_vendor = scoped_company.vendors.find(params[:vendor_id]) if params[:vendor_id].present?
        my_vendor ||= DefaultVendor.find_by(name: params[:vendor_name]) if params[:vendor_name].present?
        my_vendor
      end
    end
  end
end
