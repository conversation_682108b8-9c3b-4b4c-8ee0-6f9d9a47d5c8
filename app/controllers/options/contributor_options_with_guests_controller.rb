module Options
  class ContributorOptionsWithGuestsController < SmartListFieldOptionsController
    before_action :ensure_scoped_company

    def options
      parameters = [scoped_company, params, scoped_company_user]
      if is_help_desk_module?
        parameters = [*parameters, scoped_workspace, true, is_help_desk_module?]
      else
        parameters = [*parameters, scoped_company.default_workspace, true, is_help_desk_module?]
      end

      if is_cache_enabled? && validate_cache_key_requirements(is_help_desk_module?)
        cache_key = generate_cache_key(parameters[3], scoped_company, true, is_help_desk_module?)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key)
          ContributorOptions.call(*parameters)
        end
      else
        ContributorOptions.call(*parameters)
      end
    end

    def ensure_scoped_company
      scoped_company
    rescue
      render json: []
    end

    private

    def field_type
      'people_list'
    end
  end
end

