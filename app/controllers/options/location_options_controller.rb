module Options
  class LocationOptionsController < SmartListFieldOptionsController
    private
    def options
      if is_cache_enabled? && validate_cache_key_requirements?
        cache_key = generate_cache_key(nil, scoped_company)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key, 'location_options')
          set_read_replica_db do
            ::Options::LocationOptions.call(params, scoped_company, query, includes, excludes, limit, offset)
          end
        end
      else
        set_read_replica_db do
          ::Options::LocationOptions.call(params, scoped_company, query, includes, excludes, limit, offset)
        end
      end
    end

    def validate_cache_key_requirements?
      if scoped_company.blank?
        raise 'Scoped company is  not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end

    def field_type
      'location_list'
    end
  end
end
