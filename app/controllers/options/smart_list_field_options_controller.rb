module Options
  class SmartListFieldOptionsController < OptionsController
    protect_from_forgery with: :null_session

    skip_before_action :ensure_access, unless: :field_closed?
    before_action :authenticate_user!, unless: :is_help_center_portal?
    before_action :ensure_help_center_access

    def field_opened?
      !field_closed?
    end

    def field_closed?
      return false unless company
      @is_closed ||= custom_form_field_closed?
    end

    def custom_form_field_closed?
      return false unless company
      form_field.nil?
    end

    def company
      @company ||= scoped_company
      return nil unless current_user&.super_admin || (@company&.allow_access?)
      @company
    end

    def form_field
      @form_field ||= CustomFormField.find_by(id: params[:field_id], field_attribute_type: field_type, private: false) if params[:field_id].present?
    end

    def is_help_center_portal?
      return true if params['is_help_center_portal'] == 'true'
      field_opened?
    end

    def ensure_help_center_access
      if params['is_help_center_portal'] == 'true'
        return if can_access_help_center

        respond_to do |format|
          format.json { render json: { message: "Looks like your company's free trial ended and there is no active subscription" }, status: :payment_required }
          format.html { render "shared/free_trial_ended_and_not_subscribed"}
        end
      end
    end
  end
end
