module Options
  class CompanyAndDefaultVendorOptionsController < OptionsController
    private

    def options
      sql = """
        SELECT v.id, v.name, v.logo_url, v.url, v.default_category_id AS category_id, 'DefaultVendor' AS type
          FROM default_vendors as v
          where v.name NOT IN (
            SELECT name FROM vendors
            WHERE vendors.company_id = ?
          )
        UNION
        SELECT id, name, logo_url, url, category_id, 'Vendor' AS type
          FROM vendors
          WHERE vendors.company_id = ?  AND vendors.archived = ?
          ORDER BY name
      """
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, scoped_company.id, scoped_company.id, false])
      results = ActiveRecord::Base.connection.execute(query)
      results
    end
  end
end
