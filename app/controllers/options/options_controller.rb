module Options
  class OptionsController < AuthenticatedController
    include HandleCompanyCacheKeys
    include ValidateCacheKeyRequirements
    skip_before_action :authenticate_user!

    def index
      render json: options
    end

    def limit
      params[:limit] || 200
    end

    def offset
      params[:offset]
    end

    def includes
      params[:includes]
    end

    def excludes
      params[:excludes]
    end

    def query
      params[:query]
    end

    def values
      params[:values]
    end
  end
end
