module Options
  class EntityOptionsController < OptionsController
    def options
      offset = params[:offset].present? ? params[:offset].to_i : 0

      if params[:search].present?
        contracts = scoped_company.contracts
        locations = scoped_company.locations
        assets = scoped_company.managed_assets.active
        tickets = scoped_company.help_tickets.active.joins(custom_form_values: :custom_form_field).where("custom_form_fields.name = ?", 'subject')
        vendors = scoped_company.vendors
        staff = scoped_company.company_users.includes(:user).joins(:user)
        groups = scoped_company.contributors.joins(:group)

        locations = locations.where("name ilike ?", search)
        contracts = contracts.where("regexp_replace(name, '[^A-Za-z0-9]', '', 'g') ilike ?", search)
        assets = assets.where("regexp_replace(name, '[^A-Za-z0-9]', '', 'g') ilike ?", search)
        tickets = tickets.where("regexp_replace(custom_form_values.value_str, '[^A-Za-z0-9]', '', 'g') ilike ?", search)
        vendors = vendors.where("regexp_replace(name, '[^A-Za-z0-9]', '', 'g') ilike ?", search)
        staff_sql = """
        regexp_replace(users.first_name, '[^A-Za-z0-9]', '', 'g') ilike ?
        or
        regexp_replace(users.last_name, '[^A-Za-z0-9]', '', 'g') ilike ?
        """
        staff = staff.where(staff_sql, search, search)
        groups = groups.where("regexp_replace(groups.name, '[^A-Za-z0-9]', '', 'g') ilike ?", search)
      else
        contracts = scoped_company.contracts.limit(20).offset(offset)
        locations = scoped_company.locations.limit(20).offset(offset)
        assets = scoped_company.managed_assets.active.limit(20).offset(offset)
        tickets = scoped_company.help_tickets.active.joins(custom_form_values: :custom_form_field).where("custom_form_fields.name = ?", 'subject').limit(20).offset(offset)
        vendors = scoped_company.vendors.limit(20).offset(offset)
        staff = scoped_company.company_users.includes(:user).joins(:user).limit(20).offset(offset)
        groups = scoped_company.contributors.joins(:group).limit(20).offset(offset)
      end

      contracts = contracts.pluck(:id, :name, :linkable_id).map{ |c| {id: c.first, type: 'Contract', name: c.second, linkable_id: c.third } }
      assets = assets.pluck(:id, :name, :linkable_id).map{ |a| {id: a.first, type: 'ManagedAsset', name: a.second, linkable_id: a.third } }
      tickets = tickets.pluck(:id, "custom_form_values.value_str", :linkable_id).map{ |t| {id: t.first, type: 'HelpTicket', name: t.second, linkable_id: t.third } }
      vendors = vendors.pluck(:id, :name, :linkable_id).map{ |t| {id: t.first, type: 'Vendor', name: t.second, linkable_id: t.third } }
      locations = locations.pluck(:id, :name, :linkable_id).map{ |t| {id: t.first, type: 'Location', name: t.second, linkable_id: t.third } }
      staff = staff.map{ |t| {id: t.id, type: 'CompanyUser', name: t.full_name || t.email, linkable_id: t.linkable_id } }
      groups = groups.map{ |t| {id: t.root_id, type: 'Group', name: t.name, linkable_id: t.group.linkable_id } }

      my_options = contracts + assets + tickets + vendors + staff + groups + locations
      my_options.sort_by { |a| a[:name] }
    end

    def search
      @search ||= "%#{params[:search]}%" if params[:search]
    end
  end
end
