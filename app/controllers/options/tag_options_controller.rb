module Options
  class TagOptionsController < Options::FieldDataController
    private

    def options
      helpdesk_custom_forms_ids = scoped_company.custom_forms.where(company_module: 'helpdesk', workspace_id: scoped_workspace.id).pluck(:id)
      tags = CustomFormValue.includes(:custom_form_field)
        .where(custom_form_fields: { field_attribute_type: "tag" }, custom_form_id: helpdesk_custom_forms_ids)
        .order('value_str ASC')
        .pluck(:id, :value_str)
        .uniq { |e| e[1]}
    end
  end
end
