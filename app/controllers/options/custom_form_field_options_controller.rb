##
# CustomFormFieldOptions returns the custom form fields
# used in a number of dropdowns, specifically in Automated Tasks
module Options
  class CustomFormFieldOptionsController < OptionsController
    private
    def options
      if !custom_form_id
        field_options_for_company
      else
        field_options_by_custom_forms
      end
    end

    def field_options_by_custom_forms
      data = CustomFormField
              .where(custom_form_id: custom_form_id)
              .where.not(field_attribute_type: "static_text")
              .order(:name)
              .uniq { |field| [field['name'], field['field_attribute_type']] }
      if params["is_sla"].present?
        data.pluck(:name, :field_attribute_type, :options).map { |f| { name: f[0], field_attribute_type: f[1], options: f[2].present? ? JSON.parse(f[2]) : nil } }
      elsif params["is_survey"]
        data.pluck(:name, :field_attribute_type, :label, :options).map { |f| { name: f[0], field_attribute_type: f[1], label: f[2], options: f[3] } }
      else
        data.pluck(:name, :field_attribute_type).map { |f| { name: f[0], field_attribute_type: f[1] } }
      end
      
    end

    def field_options_for_company
      data = CustomFormField
              .joins(:custom_form)
              .where("custom_forms.company_module = ? AND custom_forms.company_id = ?", CustomFormTemplate.company_modules[company_module], scoped_company.id)
              .where.not(field_attribute_type: "static_text")
              .order(:name)
              .uniq { |field| [field['name'], field['field_attribute_type']] }
              .pluck(:name, :field_attribute_type, :label)
      data.map { |f| { name: f[0], field_attribute_type: f[1], label: f[2] } }
    end

    def custom_form_id
      params[:custom_form_id]&.split(",")
    end

    def company_module
      @company_module ||= params[:company_module].present? ? params[:company_module] : "helpdesk"
    end
  end
end
