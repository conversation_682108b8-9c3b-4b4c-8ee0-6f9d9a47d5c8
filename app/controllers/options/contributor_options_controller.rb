module Options
  class ContributorOptionsController < SmartListFieldOptionsController
    before_action :ensure_scoped_company
    skip_before_action :ensure_access, if: :help_center_portal?

    def options
      parameters = [scoped_company, params, scoped_company_user]
      if params[:assigned_to_field].present? || is_help_desk_module?
        parameters = [scoped_company, params, scoped_company_user, scoped_workspace, false, is_help_desk_module?]
      end

      if is_cache_enabled? && validate_cache_key_requirements?
        cache_key = generate_cache_key(parameters[3], scoped_company, false, is_help_desk_module?)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key)
          ContributorOptions.call(*parameters)
        end
      else
        ContributorOptions.call(*parameters)
      end
    end

    def ensure_scoped_company
      scoped_company
    rescue
      render json: []
    end

    private

    def validate_cache_key_requirements?
      if (params[:assigned_to_field].present? || is_help_desk_module?) && scoped_workspace.blank?
        raise 'Scoped workspace is not present for cache key.'
      elsif scoped_company.blank?
        raise 'Scoped company is not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end

    def field_type
      'people_list'
    end

    def help_center_portal?
      return params['is_help_center_portal'] == 'true'
    end
  end
end
