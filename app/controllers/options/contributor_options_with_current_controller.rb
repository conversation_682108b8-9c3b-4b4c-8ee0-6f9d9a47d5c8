module Options
  class ContributorOptionsWithCurrentController < ContributorOptionsWithGuestsController
    def options
      current_user_options + super.reject {|user| user[:email] == current_user&.email}
    end
    
    def current_user_options
      [
        {
          id: 'current',
          name: "Current User",
          email: "Set<PERSON>",
          avatarThumbUrl: nil
        } 
      ]
    end
  end
end

