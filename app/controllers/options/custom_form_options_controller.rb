module Options
  class CustomFormOptionsController < OptionsController
    skip_before_action :ensure_access
    before_action :ensure_help_center_access

    # When other modules get workspaces, the if/else can be removed
    # and combined into a single call rather than differentiating
    # between helpdesk vs other modules.
    def options
      @loose_scoping = true
      if is_cache_enabled? && validate_cache_key_requirements?
        cache_key = generate_cache_key(scoped_workspace, scoped_company)
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key, 'custom_form_options')
          ::Options::CustomFormOptions.call(params, excludes, scoped_company, scoped_workspace)
        end
      else
        ::Options::CustomFormOptions.call(params, excludes, scoped_company, scoped_workspace)
      end
    end

    def validate_cache_key_requirements?
      if ['location', 'company_user'].exclude?(params[:company_module]) && scoped_workspace.blank?
        raise 'Scoped workspace is not present for cache key.'
      elsif scoped_company.blank?
        raise 'Scoped company is  not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end

    def ensure_help_center_access
      if params['portal'] == 'true'
        return if can_access_help_center

        respond_to do |format|
          format.json { render json: { message: "Looks like your company's free trial ended and there is no active subscription" }, status: :payment_required }
          format.html { render "shared/free_trial_ended_and_not_subscribed"}
        end
      end
    end
  end
end
