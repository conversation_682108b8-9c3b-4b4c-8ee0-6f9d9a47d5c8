module Options
  class UninvitedCompanyUserOptionsController < OptionsController
    private
    def options
      sql_query = """
        SELECT DISTINCT(company_users.id), users.first_name, users.last_name, title_values.value_str AS title
        FROM company_users
        INNER JOIN users
          ON company_users.user_id = users.id
        LEFT JOIN (custom_form_values AS title_values
          INNER JOIN custom_form_fields AS title_field
            ON title_field.id = title_values.custom_form_field_id
              AND title_field.name = 'title')
          ON title_values.module_id = company_users.id
        WHERE company_users.company_id = :company_id
        AND company_users.granted_access_at IS NULL
        AND company_users.invitation_due_at IS NULL
        AND company_users.archived_at IS NULL
        ORDER BY users.last_name, users.first_name
      """

      sql_params = { company_id: scoped_company }

      query = ActiveRecord::Base.send(:sanitize_sql_array, [sql_query, sql_params])
      results = ActiveRecord::Base.connection.execute(query)
      results.as_json
    end
  end
end
