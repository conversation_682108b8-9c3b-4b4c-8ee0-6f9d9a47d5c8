module Options
  class GroupOptionsController < AuthenticatedController
    include PostgresHelper
    include ApplicationHelper

    around_action :set_read_replica_db, only: [:index]

    def index
      render json: { groups: groups_json, count: group_count }
    end

    def show
      @group = scoped_company.groups.find_by(name: params[:name]) if params[:name]
      @group ||= scoped_company.groups.find_by(id: params[:id]) if params[:id]

      if @group.present?
        render json: @group
      else
        render json: {}, status: :not_found
      end
    end

    protected
    def groups
      @groups ||= begin
        g = scoped_company.groups.includes({ contributor: :privileges }, group_members: { contributor: [:group, company_user: :user] })
        g = g.where("name ilike ?", "%#{query}%") if query.present?
        g = g.where.not(id: params[:excludes]) if params[:excludes].present?
        g = g.joins(contributor: :privileges).where('privileges.name = ?', params[:privilege]) if params[:privilege]
        g
      end
    end

    def group_count
      groups.length
    end

    def group_json(group)
      json = {
        id: group.id,
        member_count: group.group_members.length,
        include_all: group.include_all,
        name: group.name,
        contributor_id: group.contributor_id
      }
      json[:permissions] = group.contributor.privileges.pluck(:permission_type) if params[:privilege] || params[:with_permissions]
      json
    end

    def groups_json
      selected_groups = groups.offset(offset).limit(limit)
      if is_help_desk_module?
        selected_groups = selected_groups.where('groups.workspace_id IS NULL OR groups.workspace_id = ?', scoped_workspace.id)
      end

      if !has_subscription?("it_help_desk")
        selected_groups = selected_groups.where(workspace_id: nil)
      end
      selected_groups.map do |group|
        if !group.name
          nil
        else
          group_json(group)
        end
      end
    end

    def limit
      params[:limit] || 100
    end

    def offset
      params[:offset] || 0
    end

    def query
      params[:query]
    end

    def permission_type
      params[:permission_type]
    end
  end
end
