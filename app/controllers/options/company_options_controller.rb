module Options
  class CompanyOptionsController < OptionsController
    skip_before_action :ensure_access
    skip_before_action :verify_mfa
    skip_before_action :ensure_free_trial_or_subscribed
    skip_before_action :ensure_confirmed_email
    skip_before_action :ensure_company_verification

    private

    def options
      if is_cache_enabled? && validate_cache_key_requirements?
        if params[:related_companies].present?
          cache_key = generate_cache_key(nil, scoped_company, false, false, "#{scoped_company_user.id}_company_options")
        else
          cache_key = generate_cache_key(nil, nil, false, false, "#{current_user.id}_company_options")
        end
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          if params[:related_companies].present?
            track_cached_keys(cache_key, 'company_options')
            all_companies = child_companies << scoped_company.id
            company_options = Company.where(id: all_companies)
                                    .select(:id, :name, :default_logo_url, :subdomain)
                                    .order(:name)
          else
            track_cached_keys(cache_key, nil, 'general_company_options')
            ::Options::CompanyOptions.call(params, current_user)
          end
        end
      else
        if params[:related_companies].present?
          all_companies = child_companies << scoped_company.id
          company_options = Company.where(id: all_companies)
                                   .select(:id, :name, :default_logo_url, :subdomain)
                                   .order(:name)
        else
          ::Options::CompanyOptions.call(params, current_user)
        end
      end
    end

    def resolve_parent_company
      current_company.reseller_company || current_company
    end

    def child_companies
      if scoped_company_user.present?
        CompanyResolver.new(scoped_company_user).admin_child_companies(resolve_parent_company, params[:list_filters])
      else
        []
      end
    end

    def validate_cache_key_requirements?
      return false unless session["warden.user.user.key"].present?

      if params[:related_companies].present?
        if scoped_company.blank?
          raise 'Scoped company is not present for cache key.'
        elsif scoped_company_user.blank?
          raise 'Scoped company user is not present for cache key.'
        end
      end
      if current_user.blank?
        raise 'Current user is not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end
  end
end
