class DiscoveredAssetHistoriesController < ApplicationController
  before_action :set_discovered_asset_history, only: [:destroy]

  def index
    discovered_asset_histories = current_company.discovered_asset_histories
                                               .includes(company_user: :user)
    asset_connector_logs = current_company.asset_connector_logs
                                         .includes(company_user: :user)
    
    combined_records = combine_and_sort_records(discovered_asset_histories, asset_connector_logs)
    paginated_records = paginate_combined_records(combined_records)
    formatted_records = format_combined_records(paginated_records[:records])
    
    render json: {
      discovered_asset_histories: formatted_records,
      page_count: paginated_records[:total_pages],
      total_count: paginated_records[:total_count]
    }, status: :ok
  end

  def destroy
    if @discovered_asset_history.destroy
      render json: {}, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  private

  def combine_and_sort_records(histories, logs)
    history_records = histories.map { |h| { record: h, type: 'discovered_asset_history' } }
    log_records = logs.map { |l| { record: l, type: 'asset_connector_log' } }
    
    combined = (history_records + log_records).sort_by do |item|
      item[:record].created_at
    end.reverse
    
    combined
  end

  def paginate_combined_records(combined_records)
    page = [params[:page].to_i, 1].max
    per_page = [params[:per_page].to_i, 25].max
    
    total_count = combined_records.length
    total_pages = (total_count.to_f / per_page).ceil
    
    start_index = (page - 1) * per_page
    end_index = start_index + per_page - 1
    
    paginated_records = combined_records[start_index..end_index] || []
    
    {
      records: paginated_records,
      total_pages: total_pages,
      total_count: total_count
    }
  end

  def format_combined_records(records)
    records.map do |item|
      record = item[:record]
      type = item[:type]
      
      format_record_with_user(record, type)
    end
  end

  def format_record_with_user(record, record_type)
    user = record.company_user&.user
    full_name = user ? [user.first_name, user.last_name].compact.join(" ") : nil
    
    record.as_json.merge(
      company_user_name: full_name,
      record_type: record_type
    )
  end
  def set_discovered_asset_history
    @discovered_asset_history = DiscoveredAssetHistory.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: {}, status: :not_found
  end
end
