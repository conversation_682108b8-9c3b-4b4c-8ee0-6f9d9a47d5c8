class ItReports::HelpdeskController < ItReports::BaseController
  def statuses
    render json: @service.statuses, status: :ok
  end

  def sources
    render json: @service.sources, status: :ok
  end

  def agent_productivity
    render json: @service.agent_productivity, status: :ok
  end

  def ticket_pipeline
    render json: @service.ticket_pipeline, status: :ok
  end

  private

  def service
    ItReports::Helpdesk
  end
end
