class ItReports::VendorsController < ItReports::BaseController
  def spend_breakdown
    render json: @service.spend_breakdown, status: :ok
  end

  def top_vendors
    render json: @service.top_vendors, status: :ok
  end

  def top_apps
    render json: @service.top_apps, status: :ok
  end

  def spend_by_category
    render json: @service.spend_by_category, status: :ok
  end

  private

  def service
    ItReports::Vendors
  end
end
