module HelpTickets
  class BlockedEntitiesController < AuthenticatedController
    include MultiCompany::GeneralScoping

    def index
      if params[:keywords].present?
        blocked_keywords = BlockedEntity.where(workspace_id: scoped_workspace, entity_type: :keyword)
        render json: { keywords: blocked_keywords }
      else
        entities = BlockedEntity.where(workspace_id: scoped_workspace)
        entities = entities.group_by(&:entity_type)
        render json: {
          emails: entities['email'],
          domains: entities['domain']
        }
      end
    end

    def create
      BlockedEntity.transaction do
        params[:entities].each do |entity|
          blocked = BlockedEntity.new(resource_params)
          blocked.entity = entity
          blocked.workspace_id = scoped_workspace.id
          blocked.company_id = scoped_company.id
          blocked.save!
        end
      end
      render json: {}, status: :ok
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
    end

    def destroy
      entity = BlockedEntity.find_by_id(params[:id])
      if entity.present? && entity.destroy!
        render json: { entity: entity }, status: :ok
      else
        render json: { message: "We encountered an issue deleting this email, please refresh the page and try again"}, status: :not_found
      end
    end

    private

    def resource_params
      params.require(:blocked_entity).permit(:entity, :entity_type, :workspace_id)
    end
  end
end
