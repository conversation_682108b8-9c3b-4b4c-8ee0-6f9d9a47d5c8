module HelpTickets
  class TimeSpentsController < AuthenticatedController
    include MultiCompany::HelpTicketScoping

    before_action :set_time_spent, only: [:index, :show, :update, :destroy]
    before_action :ensure_resource, only: [:update, :destroy]
    before_action :set_time_spent_service, only: [:create, :update, :destroy]

    def index
      render json: @time_spent.resources
    end

    def create
      resource = @time_spent_service.create_time_spent(resource_attributes)
      if resource.save
        render json: resource, status: :ok
      else
        render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    end

    def show
      render json: @time_spent.resource
    end

    def update
      resource = @time_spent.resource
      if @time_spent_service.update_time_spent(resource, resource_attributes)
        render json: resource, status: :ok
      else
        render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    end

    def destroy
      resource = @time_spent.resource
      if @time_spent_service.destroy_time_spent(resource, scoped_company_user, help_ticket.id)
        render json: {}, status: :ok
      else
        render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    end

    private
    def set_time_spent_service
      @time_spent_service = TimeSpentsService.new(scoped_company, help_ticket, params)
    end

    def resource_attributes
      params.require(:time_spent).permit(
                                :hours_spent,
                                :minutes_spent,
                                :company_user_id,
                                :started_at,
                                :start_time,
                                :end_time
                              )
    end

    def ensure_resource
      unless @time_spent.resource
        respond_to do |format|
          format.json { render json: {}, status: :not_found }
          format.html
        end
      end
    end

    def set_time_spent
      @time_spent = HelpTickets::TimeSpentService.new(scoped_company, help_ticket, params, false)
    end
  end
end
