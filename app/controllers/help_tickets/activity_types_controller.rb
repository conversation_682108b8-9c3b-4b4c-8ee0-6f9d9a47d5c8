module HelpTickets
  class ActivityTypesController < AuthenticatedController
    include MultiCompany::GeneralScoping

    def index
      render json: { activity_types: activity_type_for_filter }
    end

    def activity_type_for_filter
      [
        { value: "Subject", name: "Subject" },
        { value: "Created By", name: "Created by" },
        { value: "Assigned To", name: "Assigned To" },
        { value: "priority", name: "Priority" },
        { value: "status", name: "Status" },
        { value: "note", name: "Comment" },
        { value: "time_spent", name: "Time Spent" },
        { value: "create_task", name: "Create Task" },
        { value: "create_checklist_task", name: "Create Checklist Task" },
        { value: "delete_checklist_task", name: "Delete Checklist Task" },
        { value: "task_description", name: "Task Description" },
        { value: "task_assignees", name: "Task Assignees" },
        { value: "due_date", name: "Task Due Date" },
        { value: "delete_task", name: "Delete Task" },
        { value: "completed_date", name: "Task Complete" },
        { value: "merge_ticket", name: "Merge Ticket" },
        { value: "unmerge_ticket", name: "Unmerge Ticket" },
        { value: "archived", name: "Archive" },
        { value: "text_area", name: "Description" },
        { value: "rich_text", name: "Rich Text Description" },
        { value: "asset_list", name: "Asset" },
        { value: "contract_list", name: "Contract" },
        { value: "vendor_list", name: "Vendor" },
        { value: "telecom_list", name: "Telecom Provider" },
        { value: "location_list", name: "Location" },
        { value: "attachment", name: "Attachment" },
        { value: "number", name: "Number" },
        { value: "list", name: "List" },
        { value: "date", name: "Date" },
        { value: "phone", name: "Phone" },
        { value: "text", name: "Text" },
        { value: "move_ticket", name: "Moved Ticket" },
        { value: "related_item", name: "Related Item" },
        { value: "clone_ticket", name: "Clone Ticket" }
    ]
    end
  end
end
