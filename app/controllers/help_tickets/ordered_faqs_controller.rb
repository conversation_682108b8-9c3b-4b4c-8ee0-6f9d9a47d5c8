module HelpTickets
  class OrderedFaqsController < AuthenticatedController
    def create
      render json: { questions: ordered_faqs }, status: :ok
    end

    private
    def ordered_faqs
      mappings = {}
      scoped_company.helpdesk_faqs.each do |faq|
        mappings[faq.id] = faq
      end
      order = 0
      ordered_faqs = []
      faqs = faq_ids.map { |id| mappings[id.to_i] }.compact
      faqs.each do |faq|
        faq.order = order
        order += 1
        faq.save!
      end
      faqs
    end

    def faq_ids
      @faqs ||= params[:faqs]
    end
  end
end
