module HelpTickets
  class TicketsFromEmailsController < AuthenticatedController
    def create
      ticket_emails = scoped_company.ticket_emails.where(id: params[:ticket_email_id])
      HelpDesk::InboundEmail::TicketEmailService.new(scoped_company, ticket_emails, custom_form_id).create_tickets_from_emails("tickets_from_emails")
      render json: {}, status: :ok
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
    end

    def custom_form_id
      params[:custom_form_id]
    end
  end
end
