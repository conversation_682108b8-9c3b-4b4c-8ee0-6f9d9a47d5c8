module HelpTickets
  class TimeSpentsInsightsController < AuthenticatedController
    before_action :set_time_spent, only: [:insights_data]

    def insights_data
      render json: @time_spent.insights_filtered_data, status: :ok
    end

    private
    
    def set_time_spent
      @time_spent = HelpTickets::TimeSpentService.new(scoped_company, nil, params, false, scoped_workspace)
    end
  end
end
