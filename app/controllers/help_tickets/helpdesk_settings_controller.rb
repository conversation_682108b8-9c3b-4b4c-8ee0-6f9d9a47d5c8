module HelpTickets
  class HelpdeskSettingsController < AuthenticatedController
    include Utilities::Domains
    include MultiCompany::GeneralScoping
    include HelpdeskSettingsHelper

    before_action :ensure_valid_workspace, only: [:index]
    before_action :authorize_write, only: [:update, :index]

    def index
      render json: { settings: helpdesk_settings, company_host: company_host }, status: :ok
    end

    def update
      helpdesk_setting.set_params(scoped_company_user.id, params)
      if helpdesk_setting.present?
        setting_type = helpdesk_setting.default_helpdesk_setting.setting_type
        if setting_type == "select_new_ui_for_help_center"
          scoped_company.workspaces.each do |workspace|
            design_setting = workspace.helpdesk_settings
            .find_by(default_helpdesk_setting_id: helpdesk_setting.default_helpdesk_setting_id)
            design_setting.assign_attributes(setting_params)
            if !design_setting.save
              render json: { message: helpdesk_setting.errors.full_messages.to_sentence }, status: :unprocessable_entity
            end
          end
          render json: { settings: helpdesk_settings }, status: :ok
        else
          setting_before_attributes = helpdesk_setting.attributes
          helpdesk_setting.assign_attributes(setting_params)
          if helpdesk_setting.save
            if is_custom_email_setting?(helpdesk_setting)
              update_and_verify_email(helpdesk_setting, setting_before_attributes)
            end
            render json: { settings: helpdesk_settings }, status: :ok
          else
            render json: { message: helpdesk_setting.errors.full_messages.to_sentence }, status: :unprocessable_entity
          end
        end
      else
        render json: { message: "Unable to locate this help desk setting" }, status: :not_found
      end
    end

    def fetch_assignee_groups
      if params["default_value"] == 'true'
        groups = scoped_company.groups.where(workspace_id: scoped_workspace.id, default: true)
      else
        groups = scoped_company.groups.where(contributor_id: params["groups_ids"])
      end
      render json: { gorups: groups }, status: :ok
    end

    protected

    def helpdesk_settings
      section_names = ['access_settings', 'automation_settings', 'display_settings', 'help_center_settings']
      return filter_helpdesk_settings(scoped_workspace.id, section_names)
    end

    def update_and_verify_email(setting, setting_before_attributes)
      custom_email = selected_company.helpdesk_custom_email.present? ? selected_company.helpdesk_custom_email : HelpdeskCustomEmail.new

      custom_email.assign_attributes(
        name: setting.custom_name,
        company_id: selected_company.id
      )

      if is_custom_email_updated?(setting, setting_before_attributes)
        custom_email.assign_attributes(
          verified: false,
          email: setting.selected_option
        )
      end

      custom_email.email = nil if custom_email.email.blank?
      custom_email.save!
      SendCustomEmailVerificationWorker.perform_async(custom_email.id) if custom_email.email.present?
    end

    def helpdesk_setting
      @helpdesk_setting ||= HelpdeskSetting.where(workspace_id: scoped_workspace&.id).find_by(id: params[:setting][:id])
    end

    def is_custom_email_updated?(setting, setting_before_attributes)
      setting_before_attributes["selected_option"] != setting.selected_option
    end

    def is_custom_email_setting?(setting)
      setting.default_helpdesk_setting&.setting_type == 'custom_helpdesk_email'
    end

    def setting_params
      params.require(:setting).permit(:enabled, :selected_option, :custom_name, options: [:custom_prefix, :custom_ticket_number, {assignee_groups: []}])
    end

    def ensure_valid_workspace
      raise "You do not have access to any settings" unless scoped_workspace
    end
  end
end
