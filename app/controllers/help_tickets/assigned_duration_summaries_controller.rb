module HelpTickets
  class AssignedDurationSummariesController < AuthenticatedController
    def index
      render json: summary_info
    end

    protected
    def summary_info
      sql =<<END
      select help_tickets.id, help_tickets.subject, help_tickets.created_at, min(help_ticket_activities.created_at) as assigned_at
        from help_tickets inner join help_ticket_activities
          on help_tickets.id = help_ticket_activities.help_ticket_id
        where help_ticket_activities.activity_type = ? and help_tickets.company_id = ?
          and help_tickets.archived = false
        group by help_tickets.id
        order by help_tickets.created_at desc
        limit 10
END
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, HelpTicketActivity.activity_types['assignment'], scoped_company.id])
      results = ActiveRecord::Base.connection.execute(query)
      results.map {|r| {id: r['id'], subject: r['subject'], created_at: r['created_at'], duration: (DateTime.parse(r['assigned_at'].to_s) - DateTime.parse(r['created_at'].to_s)).to_f } }
    end
  end
end
