module HelpTickets
  class TicketEmailsController < ApplicationController
    include MultiCompany::GeneralScoping

    before_action :ticket_email, only: [:create, :destroy]

    def index
      render json: {
        emails: ticket_emails,
        page: page,
        count: email_filter.count,
      }
    end

    def create
      HelpDesk::TicketEmailCreate.new(email_params)
    rescue ActiveRecord::RecordNotUnique => e
      render json: { message: "Duplicate email" }, status: :bad_request
      return
    end

    def destroy
      if ticket_email.destroy
        render json: ticket_email
      else
        render json: {errors: ticket_email.errors.messages}, status: :bad_request
      end
    end

    private
    def privilege_name
      'HelpTicket'
    end

    def page
      params[:page]&.to_i || 1
    end

    def is_duplicate_ticket?
      HelpTicket.where(message_id: ticket_email.message_id, company_id: ticket_email.company_id).exists?
    end

    def is_duplicate_ticket_comment?
      HelpTicketComment.includes(help_ticket: :company).where(message_id: ticket_email.message_id, companies: {id: ticket_email.company_id}).exists?
    end

    def is_duplicate?
      is_duplicate_ticket? || is_duplicate_ticket_comment? || is_duplicate_ticket_email?
    end

    def is_duplicate_ticket_email?
      HelpTicketEmail.where(message_id: ticket_email.message_id, company_id: ticket_email.company_id).exists?
    end

    def email_filter
      @email_filter ||= HelpDesk::InboundEmail::TicketEmailFilter.new([scoped_workspace.id], page, page_size, search)
    end

    def ticket_emails
      @ticket_emails ||= email_filter.filter
    end

    def search
      params['search_terms']
    end

    def ticket_email
      @ticket_email ||= TicketEmail.find_by(id: (params[:id] || params[:ticket_email_id]))
    end

    def emails
      @emails ||= begin
        company_user_params[:emails]
      end
    end

    def blocked_entities
      ticket_email.company.blocked_entities.pluck(:entity)
    end

    def is_blocked?
      blocked_entities.any? { |entity| ticket_email.from_email.include?(entity) }
    end

    def page_size
      params[:page_size]&.to_i || 25
    end
  end
end
