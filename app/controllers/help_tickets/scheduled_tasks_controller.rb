module HelpTickets
  class ScheduledTasks<PERSON>ontroller < AuthenticatedController
    before_action :set_resource, only: [:update, :destroy], if: Proc.new { |w| request.format.json? }

    def index
      @scheduled_tasks = scoped_workspace.scheduled_tasks.includes(:scheduled_task_recurrence, :scheduled_task_notifications).where(help_ticket_comment_id: nil)
      @scheduled_tasks = @scheduled_tasks&.order(:created_at).map do |task|
        task.as_json.merge(
          recurrence: task.scheduled_task_recurrence&.as_json,
          assignee: get_recipients(task.assignee_id).first,
          notifications: map_notifications_with_contributors(task.scheduled_task_notifications),
          recurrence_pattern_text: get_recurrence_pattern(task)
        )
      end
      render json: { scheduled_tasks: @scheduled_tasks }, status: :ok
    end

    def create
      @scheduled_task = scoped_workspace.scheduled_tasks.new(scheduled_task_params)
      @scheduled_task.assign_attributes(
        company_id: scoped_company.id,
        task_ended_at: params["scheduled_task"]["task_ended_at"],
        task_started_at: params["scheduled_task"]["task_started_at"]
      )

      if params[:comment_subject].present?
        @scheduled_task.comment_subject = params[:comment_subject]
      end
      unless params[:send_to_creator_comment].nil?
        @scheduled_task.send_to_creator_comment = params[:send_to_creator_comment]
      end
      unless params[:notify_on_comment].nil?
        @scheduled_task.send_email_to_creator_comment = params[:notify_on_comment]
      end

      clear_recurring_dates if params['scheduled_task']['recurring']
      @scheduled_task.skip_validations = true if params[:skip_validations]
      if @scheduled_task.save!
        create_new_scheduled_recurrence if params['scheduled_task']['recurring']
        create_or_update_notifications
        render json: { statuses: scoped_workspace.scheduled_tasks, scheduled_task_id: @scheduled_task.id }, status: :ok
      end
    rescue Exception => e
      render json: { message: @scheduled_task.errors.full_messages.join(', ') }, status: :bad_request
    end

    def update
      @scheduled_task.assign_attributes(
        company_id: scoped_company.id,
        task_ended_at: params["scheduled_task"]["task_ended_at"],
        task_started_at: params["scheduled_task"]["task_started_at"]
      )

      if params[:comment_subject].present?
        @scheduled_task.comment_subject = params[:comment_subject]
      end
      unless params[:send_to_creator_comment].nil?
        @scheduled_task.send_to_creator_comment = params[:send_to_creator_comment]
      end
      unless params[:notify_on_comment].nil?
        @scheduled_task.send_email_to_creator_comment = params[:notify_on_comment]
      end

      clear_recurring_dates if params['scheduled_task']['recurring']
      @scheduled_task.skip_validations = true if params[:skip_validations]
      if @scheduled_task.update(scheduled_task_params)
        if params['scheduled_task']['recurring']
          if @scheduled_task.scheduled_task_recurrence
            update_existing_scheduled_recurrence
          else
            create_new_scheduled_recurrence
          end
        end
        create_or_update_notifications
        render json: { statuses: scoped_workspace.scheduled_tasks }, status: :ok
      end
    rescue Exception => e
      render json: { message: @scheduled_task.errors.full_messages.to_sentence }, status: :bad_request
    end

    def destroy
      if @scheduled_task&.destroy
        render json: {}, status: :ok
      else
        render json: { message: @scheduled_task.errors.full_messages.join(', ') }, status: :bad_request
      end
    end

    private

    def scheduled_task_params
      params.require(:scheduled_task).permit(
        :id,
        :name,
        :description,
        :assignee_id,
        :recurring
      )
    end

    def clear_recurring_dates
      @scheduled_task.task_ended_at['date'] = ""
      @scheduled_task.task_started_at['date'] = ""
    end

    def create_new_scheduled_recurrence
      recurrence_params = params["scheduled_task"]["recurrence"]
      @scheduled_task_recurrence = ScheduledTaskRecurrence.new(scheduled_task: @scheduled_task)
      @scheduled_task_recurrence.assign_attributes(
        recurrence_date: recurrence_params['recurrence_date'],
        recurrence_pattern: recurrence_params['recurrence_pattern']
      )
      @scheduled_task_recurrence.save!
    rescue Exception => e
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end

    def create_or_update_notifications
      notification_data = params["scheduled_task"]["notifications"]
      notification_data.each do |notification|
        notification_type = notification[:notification_type]
        if notification[:value]['recipients'].length
          notification[:value]['recipients'] = notification[:value]['recipients'].pluck('id')
        end
        notification_settings = {
          enabled: notification[:enabled],
          value: notification[:value]
        }
        scheduled_task_notification = @scheduled_task.scheduled_task_notifications.find_or_initialize_by(notification_type: notification_type)
        scheduled_task_notification.assign_attributes(notification_settings)
        scheduled_task_notification.save!
      end
    rescue Exception => e
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end

    def update_existing_scheduled_recurrence
      recurrence_params = params["scheduled_task"]["recurrence"]
      @scheduled_task_recurrence = @scheduled_task.scheduled_task_recurrence
      @scheduled_task_recurrence.update!(
        recurrence_date: recurrence_params['recurrence_date'],
        recurrence_pattern: recurrence_params['recurrence_pattern']
      )
    rescue Exception => e
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end

    def set_resource
      @scheduled_task = scoped_workspace.scheduled_tasks.find_by_id(params[:id])
      unless @scheduled_task
        respond_to do |format|
          format.json { render json: { message: "Scheduled task was not found." } }
          format.html { render 'shared/not_found' }
        end
      end
    end

    def get_recipients(assignee_ids)
      contributors = Contributor.where(id: assignee_ids).left_joins(:group, company_user: :user)
      recipients_hash = contributors.map do |cont|
        { id: cont.id,
          root_id: cont.root_id,
          type: cont.contributor_type,
          full_name: cont&.name,
          name: cont&.name,
          first_name: cont&.first_name,
          last_name: cont&.last_name,
          email: cont&.email,
          avatar: cont.avatar,
          phone: cont.phone }
      end
    end

    def map_notifications_with_contributors(scheduled_notifications)
      notifications = scheduled_notifications.map do |notification|
        if notification[:value]['recipients'].length
          notification[:value]['recipients'] = get_recipients(notification[:value]['recipients'])
        end
        notification
      end
      notifications.sort_by { |n| n.id }&.as_json
    end

    def get_recurrence_pattern(task)
      return "One Time" unless task.recurring && task.scheduled_task_recurrence.recurrence_pattern
    
      pattern = task.scheduled_task_recurrence.recurrence_pattern
    
      if pattern['daily']
        return pattern['daily']['recurrence_type'] == "revision" ? "Every #{get_simple_cardinal(pattern['daily']['revision_count'])} #{"day".pluralize(pattern['daily']['revision_count'])}" : "Every weekday"
      elsif pattern['weekly']
        return "Every #{get_simple_cardinal(pattern['weekly']['revision_count'])} #{generate_schedule_description(pattern['weekly'])}"
      elsif pattern['monthly']
        if pattern['monthly']['recurrence_type'] == "monthDay"
          return "Every #{get_simple_cardinal(pattern['monthly']['day_month_revision'])} #{"month".pluralize(pattern['monthly']['day_month_revision'])} on the #{get_ordinal(pattern['monthly']['month_day'])}"
        else
          return "Every #{get_simple_cardinal(pattern['monthly']['week_month_revision'])} #{"month".pluralize(pattern['monthly']['week_month_revision'])} on the #{pattern['monthly']['selected_week']} #{pattern['monthly']['week_day_name']}"
        end
      elsif pattern['yearly']
        if pattern['yearly']['recurrence_type'] == "monthDayOption"
          return "Every #{get_simple_cardinal(pattern['yearly']['revision_count'])} #{"year".pluralize(pattern['yearly']['revision_count'])} on #{pattern['yearly']['day_month_name']} #{pattern['yearly']['month_day']}"
        else
          return "Every #{get_simple_cardinal(pattern['yearly']['revision_count'])} #{"year".pluralize(pattern['yearly']['revision_count'])} on the #{pattern['yearly']['week_day']} #{pattern['yearly']['day_name']} of #{pattern['yearly']['week_month_name']}"
        end
      end
    end

    def get_simple_cardinal(number)
      case number.to_i
      when 1
        return ""
      else
        return number.to_s
      end
    end

    def get_ordinal(number)
      case number.to_i
      when 1
        return ""
      when 2
        return "2nd"
      when 3
        return "3rd"
      else
        return number.to_s + "th"
      end
    end

    def generate_schedule_description(data)
      weekday_mapping = {
        "Mon" => "Monday",
        "Tue" => "Tuesday",
        "Wed" => "Wednesday",
        "Thu" => "Thursday",
        "Fri" => "Friday",
        "Sat" => "Saturday",
        "Sun" => "Sunday"
      }
      full_names = data['selected_days'].map { |abbrev| weekday_mapping[abbrev] }
      result = "#{"week".pluralize(data['revision_count'])} on #{full_names.join(', ')}"
      
      return result
    end
  end
end
