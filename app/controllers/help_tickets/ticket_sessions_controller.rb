module HelpTickets 
  class TicketSessionsController < AuthenticatedController
    include HandleCompanyRequests

    def index
      return unless is_session_requests_enabled?

      render json: { session: resource, sessions: resources_json }
    end

    def destroy
      if is_session_requests_enabled? && resource.present?
        if resource.destroy
          render json: { }, status: :ok
        else
          render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
        end
      end
    end

    private
    def resource
      @resource ||= TicketSession.find_by(window_guid: params[:window_guid] || params[:id])
    end

    def resources_json
      resources.map { |r| r.as_json_expanded }
    end

    def resources
      @resources ||= TicketSession.where(help_ticket_id: params[:id]).where.not(company_user_id: nil)
    end
  end
end
