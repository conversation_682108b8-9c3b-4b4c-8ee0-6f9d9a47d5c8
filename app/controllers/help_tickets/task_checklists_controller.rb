module HelpTickets
  class TaskChecklistsController < AuthenticatedController
    include MultiCompany::GeneralScoping

    before_action :set_checklists_service

    def index
      response = @checklist_service.get_task_checklists
      render_response(response)
    end

    def create
      response = @checklist_service.create_task_checklist
      render_response(response)
    end

    def show
      response = @checklist_service.show_task_checklist
      render_response(response)
    end

    def update
      response = @checklist_service.update_task_checklist
      render_response(response)
    end

    def destroy
      response = @checklist_service.destroy_task_checklist
      render_response(response)
    end

    def create_project_task_in_ticket
      response = @checklist_service.create_project_task
      render_response(response)
    end

    def order_checklists
      id_lookup = {}
      params[:checklists].each do |checklists_params|
        id_lookup[checklists_params[:id].to_i] = checklists_params[:order]
      end
      TaskChecklist.where(id: id_lookup.keys).each do |checklist|
        checklist.update_column(:order, id_lookup[checklist.id]) if id_lookup[checklist.id].present?
      end
      response = @checklist_service.get_task_checklists
      render_response(response)
    end

    private

    def set_checklists_service
      @checklist_service = ::HelpTickets::TaskChecklistsService.new(params, scoped_company, scoped_company_user, scoped_workspace, true)
    end
  end
end
