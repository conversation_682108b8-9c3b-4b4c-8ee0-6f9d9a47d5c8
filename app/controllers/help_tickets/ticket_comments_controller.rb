module HelpTickets
  class TicketCommentsController < AuthenticatedController
    include MultiCompany::HelpTicketScoping
    include HelpTicketCommentsHelper

    before_action :help_ticket, except: :should_display_comments_feature
    before_action :authorize_read_ticket, only: :index
    before_action :authorize_write_ticket, only: [:create, :update]
    before_action :authorize_write_comment, only: :destroy
    before_action :set_ticket_comment, except: :should_display_comments_feature

    def index
      comment_records = ticket_comment_data(@ticket_comment.resources)
      comment_records = comment_records.reject { |comment| comment["status"] == "scheduled" }

      help_ticket.mark_all_comments_as_read if assigned_user?(current_company_user.contributor_id)

      respond_to do |format|
        format.html {}
        format.json {
          render json: {
            comments: comment_records,
            page_count: @ticket_comment.page_count,
            expand_comment_section: expanded_comment_section
          }
        }
      end
    end

    def create
      service = TicketCommentsService.new(params, help_ticket, @ticket_comment, nil, comment_params, time_spent_params)
      response = service.create_comment

      render json: response[:response_body], status: response[:response_status]
    end

    def show
      render json: @ticket_comment.comment_json
    end

    def destroy
      service = TicketCommentsService.new(params, help_ticket, @ticket_comment, @comment, nil, nil, scoped_company_user)
      response = service.destroy_comment

      render json: response[:response_body], status: response[:response_status]
    end

    def update
      service = TicketCommentsService.new(params, help_ticket, @ticket_comment, nil, comment_params, time_spent_params)
      response = service.update_comment

      render json: response[:response_body], status: response[:response_status]
    end

    def get_scheduled_comments
      contributor_id = params[:contributor_id].presence
      comment_records = ticket_comment_data(@ticket_comment.resources)
      scheduled_comment_records = comment_records.select { |comment| comment['status'] == 'scheduled' }

      if contributor_id.present?
        created_by_contributor_ids = scheduled_comment_records
                                      .select { |comment| comment['contributor_id'].to_s == contributor_id.to_s }
                                      .map { |comment| comment['id'] }

        recipient_based_comment_ids = ScheduledTask.includes(:help_ticket_comment, :scheduled_task_notifications)
          .where.not(scheduled_at: nil, help_ticket_comment_id: nil)
          .where(recurrence_count: 0)
          .select { |task|
            task.help_ticket_comment&.help_ticket_id.to_s == ticket_id.to_s &&
            task.scheduled_task_notifications.any? do |notification|
              notification.enabled &&
                notification.value["recipients"].map(&:to_s).include?(contributor_id.to_s)
            end
          }
          .map(&:help_ticket_comment_id)
        
        allowed_comment_ids = (created_by_contributor_ids + recipient_based_comment_ids).uniq

        scheduled_comment_records = scheduled_comment_records.select do |comment|
          allowed_comment_ids.include?(comment['id'])
        end
      end

      scheduled_comment_records = append_scheduled_at_to_comments(scheduled_comment_records)

      render json: {
        comments: scheduled_comment_records,
        expand_comment_section: expanded_comment_section
      }
    end

    def get_scheduled_comment
      return unless params[:comment_id]

      scheduled_task = ScheduledTask.includes(:scheduled_task_notifications)
                                    .find_by(help_ticket_comment_id: params[:comment_id])
      if scheduled_task
        scheduled_task_with_notifications = scheduled_task.as_json.merge(
          notifications: map_notifications_with_contributors(scheduled_task.scheduled_task_notifications)
        )
        render json: scheduled_task_with_notifications, status: :ok
      else
        render json: { error: 'Scheduled task not found for this comment' }, status: :not_found
      end
    end

    def append_scheduled_at_to_comments(scheduled_comment_records)
      scheduled_comment_records.each do |comment_hash|
        comment = HelpTicketComment.find_by(id: comment_hash['id'])
        if comment&.scheduled_task&.task_started_at.present?
          comment_hash[:task_started_at] = comment.scheduled_task.task_started_at
        else
          comment_hash[:task_started_at] = nil
        end
      end
    
      scheduled_comment_records
    end

    def add_scheduled_comment
      comment = HelpTicketComment.find_by(id: params[:scheduled_comment_id])

      if comment.nil?
        render json: { message: 'Comment not found' }, status: :not_found
      elsif comment.scheduled?
        original_updated_at = comment.updated_at
        comment.update(status: :published)
        comment.update_column(:updated_at, original_updated_at)
        if comment.scheduled_task.present?
          comment.scheduled_task.update_columns(scheduled_at: nil, recurrence_count: 1)
        end
        render json: { message: 'Comment published successfully' }, status: :ok
      else
        render json: { message: 'Comment is not published' }, status: :unprocessable_entity
      end
    end

    def change_comment_tone
      TicketCommentAiWorker.perform_async(ticket_id, params[:action_type], params[:comment_body], scoped_company_user.id, params[:comment_id], params[:sub_type])

      render json: {}, status: :ok
    end

    def shorten_reply
      TicketCommentAiWorker.perform_async(ticket_id, params[:action_type], params[:comment_body], scoped_company_user.id, params[:comment_id])

      render json: {}, status: :ok
    end

    def should_display_comments_feature
      service_option = ServiceOption.find_by(service_name: "help_tickets/ai_comments", status: false)
      render json: { display_ai_comment: service_option.present? }, status: :ok
    end

    private
    def ticket_id
      params[:ticket_id] || comment_params[:help_ticket_id]
    end

    def map_notifications_with_contributors(scheduled_notifications)
      notifications = scheduled_notifications.map do |notification|
        if notification[:value]['recipients'].present?
          notification[:value]['recipients'] = get_recipients(notification[:value]['recipients'])
        end
        notification
      end
      notifications.sort_by { |n| n.id }&.as_json
    end

    def get_recipients(assignee_ids)
      contributors = Contributor.where(id: assignee_ids).left_joins(:group, company_user: :user)
      recipients_hash = contributors.map do |cont|
        { id: cont.id,
          root_id: cont.root_id,
          type: cont.contributor_type,
          full_name: cont.name,
          name: cont.name,
          first_name: cont.first_name,
          last_name: cont.last_name,
          email: cont.email,
          avatar: cont.avatar,
          avatar_thumb_url: cont.company_user&.avatar_url
        }
      end
    end

    def comment_params
      params.require(:help_ticket_comment).permit(
        :comment_text,
        :comment_body,
        :help_ticket_id,
        :private_flag,
        :contributor_id,
        :resolution_flag,
        :mute_notification,
        :parent_comment_id,
        :is_ht_modern_view,
        :status,
        private_contributor_ids: []
      ).tap do |whitelisted|
        whitelisted[:private_contributor_ids] = whitelisted[:private_contributor_ids].uniq if whitelisted[:private_contributor_ids].present?
        whitelisted[:status] = params[:status] == 'scheduled' ? 'scheduled' : 'regular' if params[:status].present?
      end
    end

    def time_spent_params
      params.require(:help_ticket_comment).permit(
        time_spent: [
          :time_spent,
          :started_at,
          :company_user_id,
          :id,
          :start_time,
          :end_time
        ]
      )
    end

    def set_ticket_comment
      if params['action'] == 'create' || params['action'] == 'update'
        @ticket_comment = TicketCommentService.new(scoped_company, scoped_company_user, help_ticket, params, comment_params, time_spent_params)
      else
        @ticket_comment = TicketCommentService.new(scoped_company, scoped_company_user, help_ticket, params)
      end
    end

    def expanded_comment_section
     scoped_workspace.helpdesk_settings.joins(:default_helpdesk_setting)
                                        .find_by(default_helpdesk_settings: { setting_type: "expand_add_new_comment_section" })
                                        .enabled
    end

    def ticket_comment_data comment_records
      if (params["is_ht_modern_view"] === 'true')
        comment_data = comment_records.map do |comment|
          comment_hash = comment.is_a?(Hash) ? comment.with_indifferent_access : comment.as_json.with_indifferent_access
          comment_hash.merge("item_type" => "comment")
        end

        comment_times = comment_data.map do |c|
          raw_time = c[:created_at]
          raw_time.is_a?(String) ? Time.parse(raw_time) : raw_time
        end.compact

        comment_start_time = params["page"] == '1' && comment_records.count < 50 ? help_ticket.created_at : comment_times.min
        comment_end_time   = params["page"] == '1' ? Time.now : comment_times.max

        custom_params = params.merge(
          skip_pagination: true,
          comment_start_time: comment_start_time,
          comment_end_time: comment_end_time
        )

        activities = HelpTickets::Activities.call(
          custom_params,
          scoped_workspace_ids,
          scoped_company,
          scoped_workspace,
          help_ticket,
          true
        )

        conversation_data = activities[:help_ticket_activities].map do |activity|
          activity.merge("item_type" => "activity")
        end

        comment_data = comment_records.map do |comment|
          comment_hash = comment.is_a?(Hash) ? comment.with_indifferent_access : comment.as_json.with_indifferent_access
          comment_hash.merge("item_type" => "comment")
        end

        merged_timeline = (conversation_data + comment_data).sort_by do |item|
          created_at_value = item["created_at"] || item[:created_at]
          created_at_value.is_a?(String) ? Time.parse(created_at_value) : created_at_value
        end
      else
        comment_records
      end
    end
  end
end
