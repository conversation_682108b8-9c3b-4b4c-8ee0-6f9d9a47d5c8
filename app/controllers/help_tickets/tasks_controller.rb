module HelpTickets
  class TasksController < AuthenticatedController
    include MultiCompany::GeneralScoping

    before_action :set_tasks_service

    def index
      response = @tasks_service.get_tasks
      render_response(response)
    end

    def create
      response = @tasks_service.create_task
      render_response(response)
    end

    def show
      response = @tasks_service.show_task
      render_response(response)
    end

    def update
      response = @tasks_service.update_task
      render_response(response)
    end

    def destroy
      response = @tasks_service.destroy_task
      render_response(response)
    end

    def order_tasks
      id_lookup = {}
      params[:tasks].each do |task_params|
        id_lookup[task_params[:id].to_i] = task_params[:order]
      end

      tasks_to_update = Task.where(id: id_lookup.keys)
      tasks_to_update.each do |task|
        task.update_column(:order, id_lookup[task.id]) if id_lookup[task.id].present?
      end

      render json: { tasks: tasks_to_update.order(:order) }, status: :ok
    end

    private

    def set_tasks_service
      @tasks_service = ::HelpTickets::TasksService.new(params, scoped_company, scoped_workspace)
    end
  end
end
