module HelpTickets
  class InboundEmailsController < ApplicationController
    include MultiCompany::GeneralScoping
    include HelpDesk::InboundEmail::EmailLogging

    def create
      InboundEmailWorker.perform_async(message_id, email_from)
      render json: { message: "Queued" }, status: :ok
    end

    def message_id
      params[:message_id]
    end
    
    def email_from
      params[:from]
    end
  end
end
