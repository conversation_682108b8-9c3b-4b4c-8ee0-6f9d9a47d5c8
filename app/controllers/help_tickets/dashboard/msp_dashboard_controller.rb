class HelpTickets::Dashboard::Msp<PERSON>ashboardController < AuthenticatedController
  include HelpTickets::Dashboard::DashboardHelper
  around_action :set_read_replica_db

  def companies_overview
    render json: HelpTickets::Dashboard::Msp::CompaniesOverview.call(params, scoped_companies)
  end

  def statuses
    render json: HelpTickets::Dashboard::Msp::Statuses.call(params, scoped_companies)
  end

  def sources
    render json: HelpTickets::Dashboard::Msp::Sources.call(params, scoped_companies)
  end

  def assignments
    render json: HelpTickets::Dashboard::Msp::Assignments.call(params, scoped_companies)
  end

  def priorities
    render json: HelpTickets::Dashboard::Msp::Priorities.call(params, scoped_companies, time_frame)
  end

  def satisfaction
    render json: HelpTickets::Dashboard::Msp::Satisfaction.call(scoped_companies)
  end

  private

  def scoped_companies
    companies_ids = params[:companies_ids].presence || user_companies
    Array.wrap(companies_ids)
  end

  def user_companies
    @user_companies ||= current_user.company_users
      .access_granted
      .not_sample_company_user
      .pluck(:company_id)
  end
end
