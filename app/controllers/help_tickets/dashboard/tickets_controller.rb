module HelpTickets
  module Dashboard
    class TicketsController < HelpTickets::BaseTicketsController
      include HelpTickets::Dashboard::DashboardHelper

      def index
        render json: tickets
      end

      def query_params
        @query_params ||= super.merge(involved_contributor_id: scoped_company_user.contributor_id, 
                                      ticket_limit: 15,
                                      ticket_offset: 0,
                                      assigned_contributor_ids: [ scoped_company_user.contributor_id ])
      end

      def statuses
        ['Active']
      end

      def ticket_sorting
        "priority desc"
      end
    end
  end
end
