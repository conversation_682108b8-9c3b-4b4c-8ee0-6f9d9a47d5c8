module HelpTickets
  module Dashboard
    module DashboardHelper

      def query_result(sql_query, sql_params)
        query = ActiveRecord::Base.send(:sanitize_sql_array, [sql_query, sql_params])
        results = ActiveRecord::Base.connection.execute(query)
        results.as_json
      end

      def ticket_created_dates(from_date)
        tickets = is_msp_request? ? companies_tickets : help_tickets
        tickets.where('help_tickets.created_at > ?', Date.today - from_date).collect { |ticket| ticket['created_at'].to_date }.uniq.sort
      end

      def help_tickets
        @help_tickets ||= HelpTicket.active.not_merged.where(workspace_id: scoped_workspace.id)
      end

      def companies_tickets
        @companies_tickets ||= HelpTicket.active.not_merged.where(company_id: scoped_companies)
      end

      def start_date
        case params[:time_frame]
        when '1 week'
          1.week.ago
        when '1 month'
          1.month.ago
        when '1 quarter'
          3.month.ago
        when '6 months'
          6.month.ago
        else
          6.days.ago
        end
      end

      def time_frame
        case params[:time_frame]
        when '1 week'
          ticket_created_dates(1.week)
        when '1 month'
          ticket_created_dates(1.month)
        when '1 quarter'
          ticket_created_dates(3.month)
        when '6 months'
          ticket_created_dates(6.month)
        else
          ticket_created_dates(6.days)
        end
      end

      def closed_join_sql
        if @params[:show_closed] == "false"
          " AND NOT status_values.value_str = 'Closed'"
        end
      end

      def is_msp_request?
        self.class.to_s.eql?("HelpTickets::Dashboard::MspDashboardController")
      end
    end
  end
end
