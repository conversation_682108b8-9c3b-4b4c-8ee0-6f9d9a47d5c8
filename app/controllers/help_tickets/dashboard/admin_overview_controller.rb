module HelpTickets
  module Dashboard
    class AdminOverviewController < AuthenticatedController
      before_action :ensure_agent_ids, only: [:index]

      def index
        render json: HelpTickets::Dashboard::AdminOverview.call(params, scoped_workspace, scoped_company, agent_ids)
      end

      private
      def agent_ids
        @agent_ids ||= customized_agents_ids.presence || all_agents_ids
      end

      def customized_agents_ids
        @customized_agents_ids ||= scoped_workspace.admin_overview_customization&.agent_ids
      end

      def all_agents_ids
        @all_agents_ids ||= scoped_workspace.workspace_agents&.contributor&.contributor_ids_only_users
      end

      def ensure_agent_ids
        render json: {} unless agent_ids.present?
      end
    end
  end
end
