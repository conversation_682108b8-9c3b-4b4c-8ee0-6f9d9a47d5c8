module HelpTickets
  module Dashboard
    class AdminOverviewCustomizationController < AuthenticatedController
      def update
        overview_customization = AdminOverviewCustomization.find_or_initialize_by(workspace_id: scoped_workspace.id, company_id: scoped_company.id)
        overview_customization.agent_ids = params[:agent_ids]
        overview_customization.save!
      end

      def show
        customized_agents_ids = scoped_workspace.admin_overview_customization&.agent_ids || []
        all_agents_ids = scoped_workspace.workspace_agents&.contributor&.contributor_ids_only_users
        customized_agents_ids = all_agents_ids if customized_agents_ids.empty?
        if customized_agents_ids.present?
          customized_agents = ::Options::ContributorOptions.call(
                                scoped_company,
                                { 'includes' => customized_agents_ids },
                                scoped_company_user, 
                                scoped_workspace,
                                false,
                                is_help_desk_module?)
        end
        render json: {
          customized_agents_ids: customized_agents_ids,
          customized_agents: customized_agents || [],
          all_agents_ids: all_agents_ids
        }
      end
    end
  end
end
