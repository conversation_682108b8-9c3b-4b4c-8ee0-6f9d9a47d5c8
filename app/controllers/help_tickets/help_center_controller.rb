module HelpTickets
  class HelpCenterController < AuthenticatedController
    include Utilities::Domains
    include HelpDeskSettings

    skip_before_action :skip_trackable # Must be kept above :authenticate_user!
    skip_before_action :ensure_access
    skip_before_action :authenticate_user!
    skip_before_action :ensure_free_trial_or_subscribed

    helper_method :workspace
    helper_method :scoped_company
    helper_method :isolate_company_setting

    def ensure_free_trial_or_subscribed
      return if (scoped_company&.allow_access? || !request.format.html?)

      respond_to do |format|
        format.json { render json: { message: "Looks like your company's free trial ended and there is no active subscription" }, status: :payment_required }
        format.html { render "shared/free_trial_ended_and_not_subscribed"}
      end
    end

    def scoped_company
      current_company
    end

    def workspace
      @workspace ||= begin
        @loose_scoping = true
        scoped_workspace || help_center_workspace
      end
    end

    def help_center_workspace
      # This is for checking the classic and modern design as all workspaces will have same setting
      @cached_workspace ||= begin
      workspace = scoped_company.helpdesk_settings
                                .find_by(enabled: true, default_helpdesk_setting_id: workspace_included_default_setting.id)
                                &.workspace

        workspace || scoped_company.workspaces.first
      end
    end

    def workspace_included_default_setting
      @default_setting ||= DefaultHelpdeskSetting.find_by(setting_type: 'include_workspace_in_help_center')
    end

    def response_data
      workspace_helpdesk_settings = workspace.helpdesk_settings.includes(:default_helpdesk_setting)

      bg_color_setting = workspace_helpdesk_settings
        .find_by(default_helpdesk_settings: { setting_type: "bg_color_for_help_center" })

      hyperlinks_color_setting = workspace_helpdesk_settings
        .find_by(default_helpdesk_settings: { setting_type: "hyperlinks_color_for_help_center" })

      public_faqs_enabled = setting_state("allow_faq_page_to_logged_out_users")

      public_articles_enabled = setting_state("allow_public_articles_to_logged_out_users")

      response_data = {
        bg_color: bg_color_setting&.selected_option ||=  is_modern_design_enabled? ? "#0c345f" : "#FFF",
        request_email: "helpdesk@#{company_host}",
        hyperlinks_color: hyperlinks_color_setting&.selected_option || "#0D6EFD",
        modern_design_enabled:  is_modern_design_enabled?,
        public_faqs_enabled: public_faqs_enabled,
        public_articles_enabled: public_articles_enabled,
      }
    end

    def show
      response_data
    end

    def index
      if GlobalEmailBlocking.last&.company_ids&.include?(scoped_company&.id)
        return respond_to do |format|
          format.html { render 'shared/not_found', status: :not_found }
        end
      end
      @body_class = "simple-layout no-drawer"
      @hide_header = true

      if scoped_company.nil?
        return respond_to do |format|
          format.json { render json: { message: "The company was not found." }, status: :not_found }
          format.html { render 'shared/not_found', status: :not_found }
        end
      end

      redirect_if_not_modern_design_enabled
      redirect_routes

      respond_to do |format|
        format.html {}
        format.json { render json: response_data }
      end
    end

    private

    def redirect_if_not_modern_design_enabled
      return if is_modern_design_enabled?

      if params[:workspace_id] && request.path.include?('/help_center/faqs/workspaces/')
        return redirect_to "/faqs/workspaces/#{params[:workspace_id]}"
      end

      if params[:workspace_id] && request.path.include?('/help_center/knowledge_base/')
        if params[:slug]
          return redirect_to "/knowledge_base/#{params[:slug]}/#{params[:workspace_id]}"
        end
        return redirect_to "/knowledge_base/workspaces/#{params[:workspace_id]}"
      end

      redirect_destinations = {
        '/help_center/submit_request' => help_center_path,
        '/help_center/knowledge_base' => knowledge_base_path,
        '/help_center/faqs' => faqs_path
      }

      redirect_path = redirect_destinations[request.path]
      redirect_to redirect_path if redirect_path
    end

    def redirect_routes
      if is_modern_design_enabled? && request.path == '/help_center/submit_request'
        redirect_to_default
      end

      if is_modern_design_enabled? && request.path == '/help_center/faqs'
        redirect_to "/help_center/faqs/workspaces/#{workspace.id}"
      end

      if is_modern_design_enabled? && request.path == '/help_center/knowledge_base'
        redirect_to "/help_center/knowledge_base/workspaces/#{workspace.id}"
      end

      if params[:id].present? && request.path.include?('forms') && !request.path.include?('workspaces')
        form = CustomForm.find_by(id: params[:id])
        if form && form.helpdesk_custom_form.show_in_open_portal
          return redirect_to "/help_center/workspaces/#{form.workspace.id}/forms/#{form.id}"
        end
      end
    end

    def redirect_to_default
      default_form = CustomForm.joins(:helpdesk_custom_form, workspace: :company)
                                .where(companies: { id: scoped_company.id })
                                .where(helpdesk_custom_forms: { show_in_open_portal: true })
                                .where(default: true)
                                .first

      if default_form
        return redirect_to "/help_center/workspaces/#{default_form.workspace.id}/forms/#{default_form.id}"
      else
        return redirect_to "help_center"
      end
    end
  end
end
