module HelpTickets
  class PublicArticlesController < AuthenticatedController
    include <PERSON>Helper
    include HelpDeskSettings
    include ArticlesHelper

    skip_before_action :skip_trackable
    skip_before_action :ensure_access
    skip_before_action :authenticate_user!

    helper_method :workspace
    helper_method :scoped_company

    def ensure_free_trial_or_subscribed
      return if can_access_help_center

      respond_to do |format|
        format.json { render json: { message: "Looks like your company's free trial ended and there is no active subscription" }, status: :payment_required }
        format.html { render "shared/free_trial_ended_and_not_subscribed"}
      end
    end

    def index
      @loose_scoping = true
      return redirect_to no_access_path(sub: subdomain) if public_resources_setting_disabled('allow_public_articles_to_logged_out_users')
      if is_modern_design_enabled?
        return redirect_to '/help_center/knowledge_base' if request.path == '/knowledge_base'

        if params[:workspace_id] && request.path.include?('knowledge_base/workspaces') && !request.path.include?('help_center')
          return redirect_to "/help_center/knowledge_base/workspaces/#{params[:workspace_id]}"
        end
      end
      respond_to do |format|
        format.html { render layout: 'bare' }
        format.json { render json: articles }
      end
    end

    def show
      return redirect_to no_access_path(sub: subdomain) if public_resources_setting_disabled('allow_public_articles_to_logged_out_users')

      if is_modern_design_enabled? && params[:slug].present? && params[:workspace_id].present? && request.format.html? && request.path.include?('knowledge_base') && !request.path.include?('help_center')
        return redirect_to "/help_center/knowledge_base/#{params[:slug]}/#{workspace.id}"
      end
      respond_to do |format|
        format.html { render layout: 'bare' }
        format.json { processed_article }
      end
    end
  end
end
