module HelpTickets
  class ProjectTaskPositionsController < AuthenticatedController
    include MultiCompany::HelpTicketScoping

    before_action :help_ticket
    before_action :authorize_write_ticket

    def update_positions
      if params[:tasks].present?
        service = ProjectTaskPositionsService.new(help_ticket)
        service.update_positions(params[:tasks])

        render json: { message: "Project tasks positions updated successfully" }, status: :ok
      else
        render json: { message: "We encountered an error while updating positions of project tasks." }, status: :bad_request
      end
    rescue
      render json: { message: "We encountered an error while updating positions of project tasks." }, status: :bad_request
    end

    def ticket_id
      params[:help_ticket_id]
    end
  end
end
