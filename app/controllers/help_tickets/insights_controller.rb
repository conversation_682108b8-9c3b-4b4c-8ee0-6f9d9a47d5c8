module HelpTickets
  class InsightsController < AuthenticatedController
    around_action :set_read_replica_db, only: [:index]

    def index
      ticket_insight = HelpDesk::Insight.new(payload, scoped_workspace, nil, filter_names)
      insights_data = get_ticket_insight_data(ticket_insight)
      render json: insights_data, status: :ok
    end

    private
    def payload
      @payload ||= JSON.parse(params[:params])
    end

    def get_ticket_insight_data(ticket_insight)
      if payload['type'].present?
        type = payload['type'].underscore
        ticket_insight.send("fetch_#{type}_data")
      elsif payload['total_ticket_summary']
        ticket_insight.fetch_ticket_summary
      end
    end

    def filter_names
      if payload['values'].present?
        return payload['values'].map { |filter| filter["name"] }
      end
      return []
    end
  end
end
