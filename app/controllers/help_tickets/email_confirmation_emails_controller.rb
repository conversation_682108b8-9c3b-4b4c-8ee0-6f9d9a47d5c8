module HelpTickets
  class EmailConfirmationEmailsController < AuthenticatedController
    def update
      if helpdesk_custom_email.present?
        SendCustomEmailVerificationWorker.perform_async(helpdesk_custom_email.id)
        render json: { }, status: :ok
      else
        render json: { message: "Unable to locate this custom email" }, status: :not_found
      end
    end

    def helpdesk_custom_email
      @custom_email ||= CustomForm.find_by(id: params[:id])&.helpdesk_custom_form.helpdesk_custom_email
    end
  end
end
