module HelpTickets
  class InProgressTicketSummariesController < AuthenticatedController
    def index
      render json: { summaries: summary_info, total: total_count }
    end

    protected
    def total_count
      scoped_company.help_tickets.in_progress.active.not_merged.count
    end

    def summary_info
      sql =<<END
      select count(help_tickets.id), users.id, users.first_name, users.last_name
        from users
        inner join company_users on company_users.user_id = users.id
        inner join contributors on contributors.id = company_users.contributor_id
        inner join help_tickets on help_tickets.company_id = company_users.company_id
        #{joins}
        where company_users.company_id = ? and cff.name = 'assigned_to' and cff.field_attribute_type = ?
          and help_tickets.active_ticket_id IS NULL
          and scff.field_attribute_type = ? 
          and sfv.value_str = 'In Progress'
        group by users.id
      union
      select count(help_tickets.id), null, 'Unassigned', ''
        from help_tickets
        where help_tickets.company_id = ?
          and help_tickets.active_ticket_id IS NULL
          and not exists (select 1 from custom_form_values
              left outer join custom_form_fields
                on custom_form_fields.id = custom_form_values.custom_form_field_id
              where help_tickets.id = custom_form_values.module_id
              and custom_form_values.module_type = 'HelpTicket'
                and custom_form_fields.name = 'assigned_to'
                  and custom_form_fields.field_attribute_type = ?)
          and help_tickets.archived = false
          and (select custom_form_values.value_str
                from custom_form_values
                inner join custom_form_fields
                  on custom_form_fields.id = custom_form_values.custom_form_field_id
                where custom_form_fields.field_attribute_type = ?
                  and custom_form_values.module_id = help_tickets.id and custom_form_values.module_type = 'HelpTicket' LIMIT 1) = 'In Progress'
        group by help_tickets.company_id
        having count(help_tickets.id) > 0
      union
      select count(help_tickets.id), groups.id, groups.name as first_name, '' as last_name
        from groups
        inner join contributors on contributors.id = groups.contributor_id
        inner join help_tickets on help_tickets.company_id = groups.company_id
        #{joins}
        where groups.company_id = ? and cff.name = 'assigned_to' and cff.field_attribute_type = ?
          and scff.field_attribute_type = ? and sfv.value_str = 'In Progress'
        group by groups.id
END
      status = CustomFormField.field_attribute_types[:status]
      people_list_field = CustomFormField.field_attribute_types[:people_list]
      query = ActiveRecord::Base.send(:sanitize_sql, [sql,
                                                      scoped_company.id,
                                                      people_list_field,
                                                      status,
                                                      scoped_company.id,
                                                      people_list_field,
                                                      status,
                                                      scoped_company.id,
                                                      people_list_field,
                                                      status])
      results = ActiveRecord::Base.connection.execute(query)
    end

    def joins
      """
        inner join custom_form_values as cfv on cfv.module_id = help_tickets.id and cfv.value_int = contributors.id and cfv.module_type = 'HelpTicket'
        inner join custom_form_values as sfv on sfv.module_id = help_tickets.id and sfv.module_type = 'HelpTicket'
        inner join custom_form_fields as cff on cff.id = cfv.custom_form_field_id
        inner join custom_form_fields as scff on scff.id = sfv.custom_form_field_id
      """
    end
  end
end
