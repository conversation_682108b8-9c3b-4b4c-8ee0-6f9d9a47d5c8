module HelpTickets
  class ActivitiesController < ModulesController
    include MultiCompany::HelpTicketScoping

    before_action :authorize_read_activities, only: [:index]

    def index
      render json: HelpTickets::Activities.call(params, scoped_workspace_ids, scoped_company, scoped_workspace, @help_ticket)
    end

    def authorize_read_activities
      if params[:ticket_id]
        authorize_read_ticket
      else
        authorize_read
      end
    end
  end
end
