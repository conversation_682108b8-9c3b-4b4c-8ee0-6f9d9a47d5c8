module HelpTickets
  class UserOptionsController < ApplicationController
    include MultiCompany::GeneralScoping

    def index
      render json: options
    end

    def options
      my_options = {}
      values.find_each do |value|
        my_options[value.value_int] = true
      end
      contributor_ids = my_options.keys
      options = Contributor.includes(:group, company_user: :user).where(id: contributor_ids).map do |c|
        {
          id: c.user&.id,
          name: c.name,
        }
      end
      options.uniq
    end

    def values
      CustomFormValue.where(custom_form_field: fields)
    end

    def fields
      CustomFormField.joins(:custom_form).where(name: name, "custom_forms.workspace_id" => scoped_workspace_ids)
    end
  end
end
