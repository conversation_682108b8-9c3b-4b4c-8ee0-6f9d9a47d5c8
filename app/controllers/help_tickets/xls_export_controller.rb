# This controller is used for exporting data from the header of HD dashboard and analytics module.
# It triggers the HelpTicketExportWorker to handle the export process.
module HelpTickets
  class XlsExportController < BaseXlsController
    include MultiCompany::GeneralScoping

    protect_from_forgery prepend: true, with: :exception
    skip_before_action :verify_authenticity_token, only: [:create]

    def export
      type = params[:options] ? [JSON.parse(params[:options])['type']] : []
      HelpTicketExportWorker.perform_async(scoped_company.id, scoped_workspace.id, scoped_company_user.id, params[:options], type)
      render json: {}
    end
  end
end
