module HelpTickets
  class SettingsController < AuthenticatedController
    include Utilities::Domains
    include MultiCompany::GeneralScoping

    before_action :set_workspace_from_param, only: [:index]
    before_action :set_workspace_from_setting, only: [:update]

    before_action :authorize_write, only: [:update]
    before_action :authorize_read, only: [:index]

    def index
      render json: { settings: helpdesk_settings(workspace), company_host: company_host}
    end

    def update
      setting_before_attributes = helpdesk_setting.attributes
      if helpdesk_setting.present?
        helpdesk_setting.assign_attributes(setting_params)
        if helpdesk_setting.save
          if is_custom_email_setting?(helpdesk_setting)
            update_and_verify_email(helpdesk_setting, setting_before_attributes)
          end
          render json: { settings: helpdesk_settings(helpdesk_setting.workspace) }, status: :ok
        else
          render json: { message: helpdesk_setting.errors.full_messages.to_sentence }, status: :unprocessable_entity
        end
      else
        render json: { message: "Unable to locate this help desk setting" }, status: :not_found
      end
    end

    def send_confirmation_email
      email_setting = selected_company.helpdesk_custom_email
      if email_setting && email_setting.email
        SendCustomEmailVerificationWorker.perform_async(email_setting.id)
        render json: { }, status: :ok
      else
        render json: { message: "Unable to locate this help desk setting" }, status: :not_found
      end
    end

    protected
    def helpdesk_settings(workspace)
      @settings ||= begin
        workspace.helpdesk_settings.joins(:default_helpdesk_setting).map do |setting|
          {
            id: setting.id,
            company_id: selected_company.id,
            name: setting.default_helpdesk_setting.title,
            description: setting.default_helpdesk_setting.description,
            enabled: setting.enabled,
            selected_option: setting.selected_option,
            setting_type: setting.default_helpdesk_setting.setting_type,
            custom_name: setting.custom_name,
            verified: selected_company.helpdesk_custom_email&.verified,
          }
        end
      end
    end

    def update_and_verify_email(setting, setting_before_attributes)
      custom_email = selected_company.helpdesk_custom_email.presence || HelpdeskCustomEmail.new

      custom_email.assign_attributes(
        name: setting.custom_name,
        company_id: selected_company.id
      )

      if is_custom_email_updated?(setting, setting_before_attributes)
        custom_email.assign_attributes(
          verified: false,
          email: setting.selected_option
        )
      end

      custom_email.email = nil if custom_email.email.blank?
      custom_email.save!
      SendCustomEmailVerificationWorker.perform_async(custom_email.id) if custom_email.email.present?
    end

    def helpdesk_setting
      @helpdesk_setting ||= HelpdeskSetting.find_by(id: params[:setting][:id])
    end

    def workspace_id
      params[:workspace_id]
    end

    def is_custom_email_updated?(setting, setting_before_attributes)
      setting_before_attributes["selected_option"] != setting.selected_option
    end

    def is_custom_email_setting?(setting)
      setting.default_helpdesk_setting&.setting_type == 'custom_helpdesk_email'
    end

    def setting_params
      params.require(:setting).permit(:enabled, :selected_option, :custom_name)
    end

    def set_workspace_from_param
      @workspace = Workspace.find_by(id: workspace_id)
    end

    def set_workspace_from_setting
      @workspace = helpdesk_setting.workspace
    end
  end
end
