module HelpTickets
  class TicketDraftsController < AuthenticatedController
    include MultiCompany::HelpTicketScoping
    include MultiCompany::GeneralScoping

    before_action :find_ticket_draft, only: [:update, :destroy]
    before_action :help_ticket, only: [:create, :check_draft]

    def index
      drafts = HelpTicketDraft.where(company_id: scoped_company.id, company_user_id: scoped_company_user.id)
                              .select(:id, :help_ticket_id, :company_user_id)
      render json: { drafts: drafts }, status: :ok
    end

    def create
      @draft = HelpTicketDraft.find_or_initialize_by(help_ticket: help_ticket, company: scoped_company, company_user: scoped_company_user)

      @draft.workspace = scoped_workspace
      @draft.assign_attributes(draft_params.slice(:fields_data, :tasks_data, :comments, :time_spents))

      if @draft.save
        render json: { draft: @draft }, status: :ok
      else
        render json: { error: @draft.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def update
      @draft.assign_attributes(draft_params.slice(:fields_data, :tasks_data, :comments, :time_spents))

      if @draft.save
        render json: { draft: @draft }, status: :ok
      else
        render json: { error: @draft.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def destroy
      if @draft.destroy
        render json: { message: 'Draft deleted successfully' }, status: :ok
      else
        render json: { error: 'Unable to delete draft' }, status: :unprocessable_entity
      end
    end

    def check_draft
      draft = HelpTicketDraft.find_by(help_ticket_id: help_ticket, workspace_id: scoped_workspace.id, company_id: scoped_company.id, company_user_id: scoped_company_user.id)
      if draft
        render json: draft.as_json, status: :ok
      else
        render json: nil, status: :ok
      end
    end

    def should_enable_ticket_drafts
      service_option = ServiceOption.find_by(service_name: "help_tickets/drafts")
      render json: { enable_ticket_drafts: !service_option.status, status: :ok }
    end

    private

    def draft_params
      params.require(:ticket_draft).permit(
        :id,
        fields_data: {},
        tasks_data: {},
        comments: {},
        time_spents: {}
      )
    end

    def find_ticket_draft
      @draft = HelpTicketDraft.find_by(id: params[:id])
      render json: { error: 'Draft not found' }, status: :not_found unless @draft
    end

    def help_ticket
      @help_ticket ||= HelpTicket.find_by(id: params[:help_ticket_id])
    end
  end
end
