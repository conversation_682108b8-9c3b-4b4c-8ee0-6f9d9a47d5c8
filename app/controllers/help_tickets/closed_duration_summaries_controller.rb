module HelpTickets
  class ClosedDurationSummariesController < AuthenticatedController
    def index
      render json: summary_info
    end

    protected
    def summary_info
      sql =<<END
      select
        help_tickets.id,
        help_tickets.created_at,
        (select custom_form_values.value_str
          from custom_form_values
          inner join custom_form_fields
            on custom_form_fields.id = custom_form_values.custom_form_field_id
          where custom_form_fields.name = 'subject'
            and custom_form_values.module_id = help_tickets.id
            and custom_form_values.module_type = 'HelpTicket' LIMIT 1
        ) as subject,
        max(help_ticket_activities.created_at) as closed_at
        from help_tickets
        inner join help_ticket_activities
          on help_tickets.id = help_ticket_activities.help_ticket_id
        where help_ticket_activities.activity_type = ?
          and help_tickets.company_id = ?
          and (select custom_form_values.value_str
              from custom_form_values
              inner join custom_form_fields
                on custom_form_fields.id = custom_form_values.custom_form_field_id
              where custom_form_fields.field_attribute_type = ?
                and custom_form_values.module_id = help_tickets.id and custom_form_values.module_type = 'HelpTicket' LIMIT 1) = 'Closed'
          and help_tickets.archived = false
          and help_tickets.active_ticket_id IS NULL
        group by help_tickets.id
        order by help_tickets.created_at desc
        limit 10
END
      activity_type = HelpTicketActivity.activity_types[:status]
      field_type = CustomFormField.field_attribute_types[:status]
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, activity_type, scoped_company.id, field_type])
      results = ActiveRecord::Base.connection.execute(query)
      results.map {|r| {id: r['id'], subject: r['subject'], duration: (DateTime.parse(r['closed_at'].to_s) - DateTime.parse(r['created_at'].to_s)).to_f } }
    end
  end
end
