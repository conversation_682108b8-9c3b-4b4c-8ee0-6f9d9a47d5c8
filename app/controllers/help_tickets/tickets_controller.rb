module HelpTickets
  class TicketsController < ModulesController
    include Utilities::Domains
    include MultiCompany::HelpTicketScoping
    include ApplicationHelper
    include HandleCompanyRequests

    before_action :authorize_write, only: [:new]
    before_action :can_edit_custom_form, only: [:edit]
    before_action :set_resource, only: [:show, :update_notification_status, :filter_activities, :destroy, :archive, :unarchive, :update_ticket_seen_status], if: Proc.new { request.format.json? }
    before_action :authorize_read, only: [:index], unless: Proc.new { !request.format.json? }
    before_action :authorize_read_ticket, only: [:show], unless: Proc.new { !request.format.json? }
    before_action :authorize_write_ticket, only: [:update_notification_status, :destroy, :archive, :unarchive], unless: Proc.new { !request.format.json? }
    before_action :resolve_helpdesk_root_path, only: [:index, :edit]
    before_action :set_body_class, unless: :is_only_basic_read
    before_action :check_log_root_present?, if: :is_log_root_show_request?
    before_action :set_ticket_service, only: [:show, :update_notification_status, :destroy, :archive, :unarchive]
    before_action :disable_footer_content

    PER_PAGE_DEFAULT = 25

    def set_body_class
      @body_class = "module module--help-tickets"
    end

    def index
    end

    def show
      respond_to do |format|
        format.html { render :index }
        format.json { render json: @ticket_service.show_ticket }
      end
      update_view_session if params[:id] && params[:id].to_i != 0
    end

    def edit; end

    def new
      render :index
    end

    def update_notification_status
      @ticket_service.update_notification_status
    end

    def update_ticket_seen_status
      @ticket.update_columns(is_seen: true) if @ticket.present? && !@ticket.is_seen && is_matching_user?
    end

    def destroy
      ticket = @ticket_service.destroy_ticket
      if ticket.destroy
        render json: ticket
      else
        render json: { error: 'Unable to delete that help ticket.  Please try later.' }, status: :unprocessable_entity
      end
    end

    def format_json?
      request.format.json?
    end

    def archive
      response = @ticket_service.archive_ticket(true)
      render json: render_data(response), status: response[:status]
    end

    def unarchive
      response = @ticket_service.archive_ticket(false)
      render json: render_data(response), status: response[:status]
    end

    def move_ticket
      current_ticket = HelpTicket.find(params[:help_ticket_id])
      merged_tickets = current_ticket.merged_help_tickets
      if merged_tickets.count > 0
        return render json: { merged_help_tickets: merged_tickets }, status: :unprocessable_entity
      else
        response = HelpTickets::MoveTicket.new(params, scoped_company, scoped_company_user, scoped_workspace, params[:help_ticket_id]).call
        if response[:missing_fields].present?
          render json: response
        else
          render json: response, status: :ok
        end
      end
    end

    def clone_ticket
      current_ticket = HelpTicket.find_by(id: params[:help_ticket_id])
      response = HelpTickets::CloneTicket.new(params[:help_ticket_id], params[:include_comments], scoped_company_user).call
      render json: response, status: :ok
    end

    def get_ticket_emails
      ticket_email = TicketEmail.find_by(help_ticket_id: params[:id])

      if ticket_email
        ticket_email_contributor_id = Contributor.where(company_id: current_company.id).find_users_by_email(ticket_email.from).pick(:id)
        attachments_with_urls = ticket_email.ticket_email_attachments.where(is_hidden: false).map do |attachment|
          {
            id: attachment.id,
            name: attachment.name,
            url: attachment.attached_file.attached? ? attachment.attached_file.url : nil,
            comment_body: build_comment_body(attachment)
          }
        end
        render json: { 
          email: ticket_email,
          attachments: attachments_with_urls,
          contributor_id: ticket_email_contributor_id
         }
      else
        render json: { error: "Ticket email not found" }, status: :bad_request
      end
    end

    private

    def set_ticket_service
      @ticket_service = TicketsService.new(@ticket, params, scoped_company_user)
    end

    def render_data(response)
      response.key?(:message) ? { message: response[:message] } : {}
    end

    def ticket_id
      params[:id]
    end

    def set_resource
      @ticket = help_ticket
    end

    def resolve_helpdesk_root_path
      if request.path.eql?('/help_tickets/dashboard') && !request.path.eql?(helpdesk_root_path)
        redirect_to helpdesk_root_path
      end
    end

    def update_view_session
      if is_session_requests_enabled? && @ticket.present? && !current_user.super_admin?
        TicketSession.find_or_create_by(window_guid: params[:window_guid], help_ticket_id: params[:id], company_user_id: scoped_company_user.id) do |session|
          session.assign_attributes(
            company_user_name: scoped_company_user.name,
            company_user_avatar: scoped_company_user.avatar.present? ? scoped_company_user.avatar_thumb : nil,
            expired_at: 5.minutes.from_now,
            help_ticket_id: params[:id],
            ip: request.ip,
            rails_guid: session.id,
            request_session_id: request.session.id
          )
        end
      end
    end

    def check_log_root_present?
      root = current_company.custom_forms.find_by(id: params[:id])
      root ||= current_company.workspaces.find_by(id: params[:id])
      root ||= Sla::Policy.find_by(id: params[:id])
      render 'shared/not_found' if root.blank?
    end

    def is_log_root_show_request?
      (request.path.start_with?('/help_tickets/settings/custom_forms/') ||
      request.path.start_with?('/help_tickets/settings/workspaces/') ||
      request.path.start_with?('/help_tickets/settings/sla/policies')) && params[:id]
    end

    def can_edit_custom_form
      authorize_write(custom_form)
    end

    def custom_form
      CustomForm.find_by(id: params[:id])
    end

    def is_matching_user?
      params[:user_matched] || @ticket.assigned_user_ids.include?(scoped_company_user.contributor_id)
    end

    def build_comment_body(attachment)
      file_name = attachment.name.to_s
      attachment_url = attachment.attached_file.attached? ? attachment.attached_file.url : nil

      comment_body = ""
      if attachment.attached_file.image?
        if file_name.downcase.end_with?('.tiff')
          comment_body += "<a href='#{attachment_url}'></a>"
        else
          comment_body += "<br><img style='max-width: 100%;' src='#{attachment_url}'></img>"
        end
      else
        comment_body += "<a href='#{attachment_url}'>#{file_name}</a>"
      end
      comment_body
    end

    def disable_footer_content
      @hide_footer_content = true
    end
  end
end
