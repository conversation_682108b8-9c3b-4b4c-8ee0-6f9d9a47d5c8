module HelpTickets
  class CompanyMailersController < ApplicationController
    include MultiCompany::GeneralScoping
    include HelpdeskSettingsHelper

    def index
      my_mailers, status = company_mailers
      render json: { mailers: my_mailers, helpdesk_email_settings: helpdesk_email_settings }, status: status
    end

    def update
      mailer = CompanyMailer.where(workspace_id: scoped_workspace_ids).find_by(id: params[:id])
      mailer.set_params(scoped_company_user.id, params)
      if mailer.present?
        mailer.opted_in = params[:opted_in]
        if mailer.automated_task.save && mailer.save
          result = company_mailers
          render json: { mailers: result[0] }, status: result[1]
        else
          render json: { message: mailer.errors.full_messages.to_sentence }, status: :unprocessable_entity
        end
      else
        render json: { message: "Unable to locate this email notification" }, status: :not_found
      end
    end

    protected

    def helpdesk_email_settings
      section_names = ['email_settings']
      return filter_helpdesk_settings(scoped_workspace.id, section_names)
    end

    def company_mailers
      default_mailer = DefaultMailer.where(module_name: params[:module]).where.not("event in (?)", ["impacted_device_changed", "follower_changed"])
      if default_mailer.present?
        [mailers, :ok]
      else
        [nil, :not_found]
      end
    end

    def mailers
      @mailers ||= begin
        CompanyMailer
          .includes(:automated_task, :default_mailer)
          .where(workspace_id: scoped_workspace&.id)
          .joins(:default_mailer)
          .where.not("default_mailers.event in (?)", ["impacted_device_changed", "follower_changed"])
          .map do |mailer|
          {
            id: mailer.id,
            module_name: mailer.default_mailer.module_name,
            event: mailer.default_mailer.event,
            description: mailer.default_mailer.description,
            opted_in: mailer.opted_in,
            automated_task_id: mailer.automated_tasks_automated_task_id,
            company_id: mailer.company_id,
            workspace_id: mailer.workspace_id,
          }
        end
      end
    end
  end
end
