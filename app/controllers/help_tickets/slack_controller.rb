module HelpTickets
  class SlackController < ApplicationController
    before_action :set_resources
    
    def create
      initiate_slack_ticket_creation
      
      render json: success_response_block, status: :ok
    end

    private

    def subject_text
      params[:text]
    end

    def set_resources
      @slack_config = Integrations::Slack::Config.find_by(team_id: params[:team_id], channel_id: params[:channel_id])
      return render json: not_found_response_block, status: :ok if @slack_config.blank?
    end

    def not_found_response_block
      {
        'response_type': 'in_channel',
        'text': 'Sorry, this slack channel is not integrated with any company at Genuity'
      }
    end

    def success_response_block
      {
        'response_type': 'in_channel',
        'text': "#{subject_text} ticket creation is in process"
      }
    end
    
    def initiate_slack_ticket_creation
      CreateSlackTicketWorker.perform_async(@slack_config.id, params[:user_id], subject_text)
    end
  end
end
