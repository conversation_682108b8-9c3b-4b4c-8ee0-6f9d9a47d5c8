module HelpTickets
  class Staff<PERSON>romTicketEmailsController < AuthenticatedController
    def create
      @ticket_emails = params[:help_ticket_emails]
      CompanyUser.transaction do
        @ticket_emails.each do |ticket_email_params|
          ticket_email = scoped_company.ticket_emails.find_by(id: ticket_email_params[:id])

          first_name = nil
          last_name = nil

          if ticket_email_params[:first_name] || ticket_email_params[:last_name]
            first_name = ticket_email_params[:first_name] || ""
            last_name = ticket_email_params[:last_name] || ""
          else
            name = ticket_email.from_email.split("@")[0]
            full_name = name.split(/\s*[._-]\s*/x)
            if full_name.count > 1
              first_name = full_name[0]
              last_name = full_name[1]
            else
              first_name = full_name[0]
            end
          end
          user = User.find_by_cache(email: ticket_email.from_email)
          if user
            @company_user = CompanyUser.unscoped.find_or_initialize_by(user_id: user.id, company_id: scoped_company.id)
            if first_name.present? && last_name.present?
              user.update_columns(first_name: first_name, last_name: last_name)
            end
          else
            @company_user = scoped_company.company_users.new(
              user_attributes: {
                first_name: first_name,
                last_name: last_name,
                email: ticket_email&.from_email,
                confirmation_token: SecureRandom.hex(32),
                skip_validations: true
              }
            )
          end
          default_custom_form = scoped_company.custom_forms.find_by(company_module: 'company_user', default: true)
          @company_user.custom_form = default_custom_form
          @company_user.save!
        end
      rescue Exception => e
        CompanyUser.connection.execute "ROLLBACK"
        raise e
      end
      render json: {}, status: :ok
    rescue
      render json: { message: @company_user.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end
end
