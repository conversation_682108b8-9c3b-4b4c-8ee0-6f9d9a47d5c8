module HelpTickets
  class Reports<PERSON><PERSON>roller < AuthenticatedController
    before_action :set_report, only: [:show, :update, :destroy, :export_report]

    def index
      render json: { reports: reports, total_reports: @reports_count }, status: :ok
    end

    def show
      render json: @report.as_json, status: :ok
    end
    
    def create
      report = Report.new(report_params)
      report.creator_id = scoped_company_user.contributor_id
      report.last_edited_by_id = scoped_company_user.contributor_id
      report.company_id = scoped_company.id
      report.workspace_id = scoped_workspace.id
      report.filters = params['report']['filters']

      if report.save
        HelpDesk::CardUpdate.new(params['report']['cards'], report).create
        render json: report, status: :ok
      else
        render json: { message: report.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end

    def update
      params['report']['last_edited_by_id'] = scoped_company_user.contributor_id
      @report.filters = params['report']['filters']
      @report.updated_at = Time.now

      if @report.update(report_params)
        HelpDesk::CardUpdate.new(params['report']['cards'], @report).update
        render json: @report, status: :ok
      else
        render json: { message: @report.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end

    def destroy
      @report.destroy
      render json: {}, status: :ok
    end

    def reports
      reports = scoped_workspace.reports
      reports = reports.search_text(params[:search]) if params[:search].present?
      reports = reports.where(creator_id: params[:report_creator_id]) if params[:report_creator_id].present?
      reports = reports.where(last_edited_by_id: params[:report_editor_id]) if params[:report_editor_id].present?
    
      reports = apply_date_filter(reports, params[:created_at], :created_at)
      reports = apply_date_filter(reports, params[:edited_at], :updated_at)
    
      @reports_count = reports.size
      reports = reports.order(updated_at: :desc)
      reports.offset((params[:offset].to_i - 1) * params[:page_size].to_i).limit(params[:page_size].to_i)
    end

    def export_report
      Pusher.trigger_async(
        scoped_company.guid,
        'download-helpdesk-report',
        {
          percentage: 0.2,
          name: @report.name,
        }
      );
      send_to_s3_worker
      render json: { status: 'ok' }
    end

    def download_report
      report = HelpdeskReport.find_by(id: params[:report_id])
      if report.present?
        report_service = Reporting::ReportsService.new(report.id, 'helpdesk_report')
        report_data = report_service.fetch_report_from_s3(report)
        respond_to do |format|
          format.pdf { send_data Base64.encode64(report_data), content_type: 'application/pdf' }
        end
      else
        render json: {}, status: :not_found
      end
    end

    def recipients
      contributors = Contributor.where(id: params['recipient_ids']).left_joins(:group, company_user: :user)
      recipients_hash = contributors.map do |cont|
        {
          id: cont.id,
          email: cont.email,
          name: cont.name,
          avatar: cont.avatar,
        }
      end
      
      if recipients_hash
        render json: recipients_hash, status: :ok
      else
        render json: { message: "Sorry, there was an error fetching report recipients." }, status: :not_found
      end
    end
    
    private

    def set_report
      @report = Report.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      render json: { error: "Report not found" }, status: :not_found
    end

    def apply_date_filter(scope, date_param, column)
      return scope unless date_param.present?
    
      parsed_dates = JSON.parse(date_param) rescue {}
      start_date = Date.parse(parsed_dates["startDate"]) rescue nil
      end_date   = Date.parse(parsed_dates["endDate"]) rescue nil
    
      if start_date && end_date
        scope.where(column => start_date.beginning_of_day..end_date.end_of_day)
      else
        scope
      end
    end

    def report_params
      params.require(:report).permit(
        :name,
        :description,
        :icon_class,
        :filters,
        :is_default,
        :creator_id,
        :last_edited_by_id,
        :company_id,
        :workspace_id
      )
    end

    def send_to_s3_worker
      HelpdeskReportToS3Worker.perform_async({
        company_id: scoped_company.id,
        report_name: @report.name,
        path: "/help_tickets/reports/#{@report.id}",
        company_guid: scoped_company.guid,
        cookies: request.headers['Cookie'],
        request_host: request.headers['Host'],
        subdomain: subdomain
      }.as_json)
    end
  end
end
