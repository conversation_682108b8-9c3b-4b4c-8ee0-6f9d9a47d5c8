module HelpTickets
  class MergedTicketsController < AuthenticatedController
    include HandleCompanyCache<PERSON><PERSON>s

    def create
      if child_tickets
        parent_follower_field = get_followers_field(parent_ticket)
        contributor_ids = []
        update_child_tickets(child_tickets)

        child_tickets.each do |child_ticket|
          if parent_follower_field.present?
            contributor_ids += child_ticket.creators.map { |cu| cu.contributor_id } if !child_ticket.creators.is_a?(String)
            child_follower_field = get_followers_field(child_ticket)

            contributor_ids << child_ticket.custom_form.custom_form_values.where(custom_form_field_id: child_follower_field.id,
                                                                                 module_id: child_ticket.id,
                                                                                 module_type: "HelpTicket",
                                                                                 company_id: child_ticket.company_id).pluck(:value_int) if child_follower_field.present?
          end
        end

        if parent_follower_field.present? && contributor_ids.flatten.uniq.present?
          contributor_ids.flatten.uniq.each do |contributor_id|
            custom_form_value = parent_ticket.custom_form.custom_form_values.find_or_initialize_by(custom_form_field_id: parent_follower_field.id,
                                                                                                   value_int: contributor_id,
                                                                                                   module_id: parent_ticket.id,
                                                                                                   module_type: "HelpTicket",
                                                                                                   company_id: company.id)
            custom_form_value.save if custom_form_value.new_record?
          end
        end

        HelpDesk::MergeTicketActivityCreate.new(parent_ticket, company_user, child_tickets).create_merge_ticket_activity
        merge_comments if params[:add_comment_to_parent]
        merge_descriptions if params[:add_description_to_parent]
        add_merge_comment
        Pusher.trigger("workspace=#{scoped_workspace.id}", 'ticket', {})
        render json: {}, status: :ok
      else
        render json: {}, status: :bad_request
      end
    end

    private
    def parent_ticket
      @parent_ticket ||= company.help_tickets.find_by(id: params[:parent_ticket])
    end

    def child_tickets
      @child_tickets ||= company.help_tickets.where(id: params[:child_tickets])
    end

    def parent_description_value
    @parent_description_value ||= parent_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "description" }) ||
                                  parent_ticket.custom_form_values.create!(
                                    custom_form_field_id: parent_ticket.custom_form.custom_form_fields.find_by(name: 'description').id,
                                    value_str: '',
                                    custom_form_id: parent_ticket.custom_form_id,
                                    company_id: parent_ticket.company_id
                                  )
    end

    def parent_subject_value
      @parent_subject_value ||= parent_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "subject" })
    end

    def add_merge_comment
      help_ticket_comments = child_tickets.map do |child_ticket|
        {
          comment_body: "<div>Ticket got merged in <strong>##{parent_ticket.ticket_number} #{parent_subject_value&.value_str}</strong></div>",
          help_ticket_id: child_ticket.id,
          contributor_id: company_user.contributor_id
        }
      end
      HelpTicketComment.insert_all(help_ticket_comments)
    end

    def get_followers_field ticket
      ticket.custom_form.custom_form_fields.find_by(name: "followers", field_attribute_type: "people_list") ||
      ticket.custom_form.custom_form_fields.find_by(name: "raise_this_request_on_behalf_of", field_attribute_type: "people_list")
    end

    def merge_comments
      help_ticket_comments = child_tickets.includes(:help_ticket_comments).map do |child_ticket|
        child_ticket.help_ticket_comments.map do |com|
          new_comment = com.dup
          new_comment.help_ticket_id = parent_ticket.id
          new_comment.updated_at = com.updated_at
          if com.created_at >= parent_ticket.created_at
            new_comment.created_at = com.created_at
          else
            new_comment.created_at = Time.now
          end
          new_comment.attributes.except("id")
        end
      end
      help_ticket_comments.flatten!
      HelpTicketComment.insert_all(help_ticket_comments) if help_ticket_comments.present?
    end

    def merge_descriptions
      description = child_tickets.map { |child_ticket| description(child_ticket) }.compact.join
      if description.present?
        parent_description_value.update_columns(value_str: parent_description_value.value_str + description)
        parent_ticket.update_columns(description: parent_description_value.value_str + description)
      end
    end

    def description ticket
      des = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "description" })&.value_str
      if des
        "<br><strong>##{ticket.ticket_number}</strong>#{des}"
      else
        ""
      end
    end

    def company
      @company ||= begin
        if scoped_company.id == params[:company_id]
          scoped_company
        else
          Company.find_cache(id: params[:company_id])
        end
      end
    end

    def company_user
      @company_user ||= begin
        if scoped_company.id == params[:company_id]
          scoped_company_user
        else
          company_user_cache(company, scoped_company_user.email)
        end
      end
    end

    def update_child_tickets(tickets)
      child_tickets_cfvs = CustomFormValue.joins(:custom_form_field)
                           .where(module_id: tickets.map(&:id),
                                  module_type: "HelpTicket",
                                  custom_form_fields: { name: "status" })

      child_tickets_cfvs.update_all(value_str: "Closed")
      child_tickets_cfvs.each(&:set_module_status_value)
      tickets.update_all(active_ticket_id: parent_ticket.id)
    end
  end
end
