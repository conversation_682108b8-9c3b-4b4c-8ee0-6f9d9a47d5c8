module HelpTickets
  class SnippetsController < AuthenticatedController
    include MultiCompany::GeneralScoping

    PER_PAGE_DEFAULT = 10

    def index
      snippets = filter_snippets.paginate(page: params[:page], per_page: params[:per_page] || PER_PAGE_DEFAULT ) if params[:per_page]
      render json: { snippets: snippets.reorder(:title), total: scoped_company.snippets.count, page_count: page_count(filter_snippets) }
    end

    def create
      @snippet = snippets.new(snippet_params)
      snippet.company_id ||= scoped_company.id
      if snippet.save
        render json: { data: scoped_company.snippets }, status: :ok
      else
        errors = snippet.errors.full_messages.to_sentence
        render json: { errors: errors }, status: :unprocessable_entity
      end
    end

    def show
      render json: snippets.find_by(id: params[:id]), status: :ok
    end

    def update
      if snippet&.update(snippet_params)
        render json: { data: scoped_company.snippets }, status: :ok
      else
        errors = snippet.errors.full_messages.to_sentence
        render json: { errors: errors }, status: :bad_request
      end
    end

    def destroy
      if snippet&.destroy
        render json: {}, status: :ok
      else
        errors = snippet.errors.full_messages.to_sentence
        render json: { errors: errors }, status: :bad_request
      end
    end

    private
    def snippet
      @snippet ||= snippets.find_by(id: params[:id])
    end

    def snippets
      @snippets ||= Snippet.where(company_id: scoped_company.id, workspace_id: scoped_workspace.id)
    end

    def parent_child_snippets
      @parent_snippets ||= begin
        if !params[:index_page] && reseller_company && scoped_company_user.is_workspace_agent?(scoped_workspace)
          company_ids = [reseller_company.id, scoped_company.id]
          workspace_ids = [reseller_company.default_workspace.id, scoped_workspace.id]

          if parent_company_user&.is_workspace_agent?(reseller_company.default_workspace) && parent_company_user.granted_access_at.present?
            Snippet.where(company_id: company_ids, workspace_id: workspace_ids)
          end
        end
      end
    end

    def reseller_company
      @reseller_company ||= scoped_company.reseller_company
    end

    def parent_company_user
      @parent_company_user ||= CompanyUser.find_by_cache(user_id: current_user.id, company_id: reseller_company.id)
    end

    def filter_snippets
      selected_snippets = parent_child_snippets.present? ? parent_child_snippets : snippets
      params[:search].present? ? selected_snippets.search_text(params[:search]) : selected_snippets
    end

    def snippet_params
      params[:snippet].permit(:id, :title, :description, :company_id)
    end

    def page_count filtered_snippets
      (filtered_snippets.length / (params[:per_page].try(:to_f) || PER_PAGE_DEFAULT)).ceil
    end
  end
end
