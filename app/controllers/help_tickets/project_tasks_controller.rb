module HelpTickets
  class ProjectTasksController < ModulesController
    include MultiCompany::HelpTicketScoping

    before_action :help_ticket
    before_action :resource, only: [:show, :edit, :update, :destroy]
    before_action :authorize_read_ticket, only: [:show, :index]
    before_action :authorize_write_ticket, only: [:edit, :update, :create, :destroy]
    before_action :project_task_service, only: [:index, :show, :update]
    before_action :delete_task_activity, only: [:destroy]

    def index
      render json: {
        tasks: @project_task.resources,
        total_count: @project_task.total_count,
        completed_count: @project_task.completed_count
      }, status: :ok
    end

    def show
      render json: @project_task.to_json(resource), status: :ok
    end

    def create
      ProjectTask.transaction do
        project_task = ProjectTaskService.new(help_ticket, params, resource_params, assignee_params)
        new_resource = project_task.new_resource
        new_resource.save!
        project_task.create_assignees
        project_task.activity_create(new_resource, scoped_company_user).create_project_task_add_activity
        render json: project_task.to_json(new_resource)
      rescue => e
        render json: { message: "Unable to save task" }, status: :unprocessable_entity
      end
    end

    def destroy
      if resource.destroy
        render json: {}, status: :ok
      else
        render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    end

    def update
      old_task = resource.attributes
      task_old_assignees = resource.task_assignees.map { |assignee| assignee.contributor&.name }
      ProjectTask.transaction do
        project_task = ProjectTaskService.new(help_ticket, params, resource_params, assignee_params, resource)
        resource.assign_attributes(resource_params)
        resource.save!
        project_task.create_assignees
        project_task.activity_create(resource, scoped_company_user).create_project_task_update_activity(old_task, task_old_assignees)
        render json: @project_task.to_json(resource), status: :ok
      rescue => e
        render json: { message: "#{resource.errors.full_messages.to_sentence}" }, status: :unprocessable_entity
      end
    end

    private
    def resource
      @resource ||= help_ticket.project_tasks.find(params[:id])
    rescue ActiveRecord::RecordNotFound => e
      respond_to do |format|
        format.json { render json: { message: "Task was not found." }, status: :not_found }
        format.html { render 'shared/not_found', status: :not_found }
      end
    end

    def resource_params
      params.require(:project_task).permit(
                                    :description,
                                    :priority,
                                    :due_at,
                                    :completed_at,
                                    :contributor_id
                                  )
    end

    def assignee_params
      params.require(:project_task).permit(assignees: [:id])
    end

    def project_task_service
      @project_task = ProjectTaskService.new(help_ticket, params)
    end

    def delete_task_activity
      old_task = resource.attributes
      project_task = ProjectTaskService.new(help_ticket, params)
      project_task.activity_create(resource, scoped_company_user).create_project_task_delete_activity(old_task)
    end
  end
end
