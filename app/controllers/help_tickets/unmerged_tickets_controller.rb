module HelpTickets
  class UnmergedTicketsController < AuthenticatedController
    def create
      if unmerged_ticket_params.present?
        ticket = HelpTicket.find_by(id: unmerged_ticket_params[:id])
        parent_ticket = HelpTicket.find_by(id: ticket.active_ticket_id) if ticket.present?

        if parent_ticket.present?
          unmerged_ticket(ticket)
          update_status_value(ticket)
          create_unmerged_ticket_activity(parent_ticket, ticket)

          render json: { id: parent_ticket.id }, status: :ok
        else
          render json: {}, status: :bad_request
        end
      else
        render json: {}, status: :bad_request
      end
    end

    private

    def unmerged_ticket_params
      params.require(:merged_ticket)
    end

    def unmerged_ticket(ticket)
      ticket.update(active_ticket_id: nil)
    end

    def update_status_value(ticket)
      ticket_value = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "status", field_attribute_type: "status" })
      ticket_value.update(value_str: "Open") if ticket_value.present?
    end

    def create_unmerged_ticket_activity(parent_ticket, ticket)
      HelpDesk::UnmergeTicketActivityCreate.new(parent_ticket, current_company_user, [ticket]).create_unmerge_ticket_activity
    end
  end
end
