module HelpTickets
  class PublicFaqsController < AuthenticatedController
    include FaqsHelper

    skip_before_action :skip_trackable # Must be kept above :authenticate_user!
    skip_before_action :ensure_access
    skip_before_action :authenticate_user!

    helper_method :workspace
    helper_method :scoped_company
    helper_method :isolate_company_setting

    def ensure_free_trial_or_subscribed
      return if can_access_help_center

      respond_to do |format|
        format.json { render json: { message: "Looks like your company's free trial ended and there is no active subscription" }, status: :payment_required }
        format.html { render "shared/free_trial_ended_and_not_subscribed"}
      end
    end

    def index
      @loose_scoping = true
      return redirect_to no_access_path(sub: subdomain) if public_resources_setting_disabled("allow_faq_page_to_logged_out_users")

      if is_modern_design_enabled?
        return redirect_to '/help_center/faqs' if request.path == '/faqs'

        if params[:workspace_id] && request.path.include?('faqs/workspaces/') && !request.path.include?('help_center')
          return redirect_to "/help_center/faqs/workspaces/#{params[:workspace_id]}"
        end
      end

      respond_to do |format|
        format.html {render layout: 'bare'}
        format.json  do
          render json: { questions: public_faqs}
        end
      end
    end
  end
end
