module HelpTickets
  class FaqsController < AuthenticatedController
    PER_PAGE_DEFAULT = 10

    before_action :authorize_read, only: [:index, :show ]
    before_action :authorize_write, only: [:create, :update, :destroy]

    def index
      paginate_faqs = questions.as_json
      paginate_faqs = questions.paginate(page: params[:page], per_page: params[:per_page] || PER_PAGE_DEFAULT ) if params[:per_page]
      render json: { questions: paginate_faqs, total: questions.count, page_count: page_count(questions) }
    end

    def show
      respond_to do |format|
        format.html { }
        format.json  { render json: question }
      end
    end

    def create
      @question = helpdesk_faqs.new(helpdesk_faq_params)
      update_question_and_answer_html
      if @question.save
        update_attachments if params[:attachment_ids]
        render json: { questions: helpdesk_faqs.order(:order) }, status: :ok
      else
        render json: { message: @question.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    end

    def update
      if question.present?
        @question = question
        if params["helpdesk_faq"].include?(:only_category_change)
          @question.update_column(:category_id, helpdesk_faq_params[:category_id])
        else
          @question.assign_attributes(helpdesk_faq_params)
          update_question_and_answer_html
        end
        if question.save
          update_attachments if params[:attachment_ids]
          render json: { questions: helpdesk_faqs.order(:order) }, status: :ok
        else
          render json: { message: @question.errors.full_messages.to_sentence }, status: :unprocessable_entity
        end
      else
        render json: {}, status: :not_found
      end
    end

    def destroy
      if question.present?
        if question.destroy
          render json: { questions: helpdesk_faqs }, status: :ok
        else
          render json: { message: question.errors.full_messages.to_sentence }, status: :unprocessable_entity
        end
      else
        render json: {}, status: :not_found
      end
    end

    private
    def helpdesk_faqs
      @helpdesk_faqs ||= begin
        faqs = HelpdeskFaq.where(company_id: company_id, workspace_id: scoped_workspace.id)
        faqs = faqs.where(public: true) if is_basic_read?
        faqs
      end
    end

    def company_id
      scoped_company.id
    end

    def questions
      @questions ||= begin
        qs = helpdesk_faqs.order(:order)
        qs = qs.search_text(params[:search_terms]) if params[:search_terms]
        qs = qs.where(company_id: company_id)
        qs
      end
    end

    def question
      @question ||= helpdesk_faqs.find_by(id: params[:id])
    end

    def is_basic_read?
      JSON.parse(params[:is_basic_read]) if params[:is_basic_read]
    end

    def helpdesk_faq_params
      params.require(:helpdesk_faq).permit(:question_body, :answer_body, :category_id, :company_id, :public)
    end

    def page_count filtered_faqs
      (filtered_faqs.length / (params[:per_page].try(:to_f) || PER_PAGE_DEFAULT)).ceil
    end

    def update_question_and_answer_html
      @question.question_body = AttachmentService.new({ html: helpdesk_faq_params[:question_body], max_width: 600 }).resize_image_html
      @question.answer_body = AttachmentService.new({ html: helpdesk_faq_params[:answer_body], max_width: 600 }).resize_image_html
    end

    def update_attachments
      attachments = scoped_company.attachment_uploads.where(id: params[:attachment_ids])
      attachments.update_all(attachable_id: @question.id)
    end
  end
end
