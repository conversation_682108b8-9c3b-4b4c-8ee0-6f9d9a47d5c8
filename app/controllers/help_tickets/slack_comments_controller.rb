module HelpTickets 
  class SlackCommentsController < ApplicationController
    before_action :check_integration
    before_action :check_channel_type

    def create
      @help_ticket = HelpTicket.where('? = ANY (slack_message_ids)', @thread_id).first
      return render json: {}, status: :not_found if @help_ticket.blank?
      
      HelpDesk::CommentCreate.new(@help_ticket, comment_params, request_params[:message][:ts]).call do
        notify_comment_creation
      end
      
      render json: {}, status: :ok
    end

    private

    def check_integration
      team_id = request_params[:team][:id]
      channel_id = request_params[:channel][:id]

      @slack_config = Integrations::Slack::Config.find_by(team_id: team_id, channel_id: channel_id)
      render json: {}, status: :not_found if @slack_config.blank?
    end

    def comment_params
      { source: :slack, comment_body: "<div><!--block-->#{request_params[:message][:text]}</div>" }
    end

    def check_channel_type
      if is_not_public_channel? || thread_id.blank?
        notify_comment_creation
        render json: {}, status: :ok
      end
    end

    def thread_id
      @thread_id ||= request_params[:message][:thread_ts]
    end

    def is_not_public_channel?
      !@slack_config.channel_name.start_with?('#')
    end

    def request_params
      @request_params ||= JSON.parse(params[:payload]).deep_symbolize_keys
    end

    def notify_comment_creation
      NotifySlackCommentCreationWorker.perform_async(@slack_config.id, @help_ticket&.id, @thread_id)
    end
  end
end
