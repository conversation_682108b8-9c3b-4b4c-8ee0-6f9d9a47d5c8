module HelpTickets
  class ResponseTimeTicketSummariesController < AuthenticatedController
    def index
      render json: summary_info
    end

    protected
    def summary_info
      sql =<<END
      select
        help_tickets.id,
        help_tickets.created_at,
        min(help_ticket_activities.created_at) as responded_at,
        (select custom_form_values.value_str
          from custom_form_values
          inner join custom_form_fields
            on custom_form_fields.id = custom_form_values.custom_form_field_id
          where custom_form_fields.name = 'subject'
            and custom_form_values.module_id = help_tickets.id and custom_form_values.module_type = 'HelpTicket' LIMIT 1
        ) as subject
        from help_tickets left outer join help_ticket_activities
          on help_tickets.id = help_ticket_activities.help_ticket_id
        where (help_ticket_activities.activity_type = ? or (help_ticket_activities.activity_type = ? AND help_ticket_activities.owner_id <> help_tickets.creator_id)) and help_tickets.company_id = ? and
          help_tickets.archived = false
        group by help_tickets.id
        order by help_tickets.created_at desc
        limit 10
END
      activities = []
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, HelpTicketActivity.activity_types['status'], HelpTicketActivity.activity_types['note'], scoped_company.id])
      results = ActiveRecord::Base.connection.execute(query)
      results.map do |r|
        responded_at = DateTime.parse(r['responded_at'].to_s) || DateTime.now
        {id: r['id'], subject: r['subject'], duration: (responded_at - DateTime.parse(r['created_at'].to_s)).to_f }
      end
    end
  end
end
