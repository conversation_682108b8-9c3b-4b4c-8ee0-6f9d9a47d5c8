module HelpTickets
  class TicketListColumnsController < ModulesController
    def index
      render json: HelpTickets::TicketListColumns.call(scoped_workspace, current_user)
    end

    def update
      user_ticket_list_column = ::TicketListColumn.find_or_initialize_by(company_id: scoped_company.id, workspace_id: ticket_list_column_params[:workspace_id], user_id: current_user.id)
      user_ticket_list_column.assign_attributes(columns: params[:selected_columns])

      if user_ticket_list_column.save
        render json: { message: "Ticket list columns have been saved successfully" }, status: :ok
      else
        render json: { message: user_ticket_list_column.errors.to_sentence }, status: :unprocessable_entity
      end
    end

    private
    def ticket_list_column_params
      params.permit(:workspace_id)
    end
  end
end
