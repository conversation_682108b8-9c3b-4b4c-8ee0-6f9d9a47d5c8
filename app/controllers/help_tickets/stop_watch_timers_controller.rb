module HelpTickets
  class StopWatchTimersController < ModulesController
    include PostgresHelper
    include MultiCompany::HelpTicketScoping

    before_action :set_timer_service

    def index
      render json: @timer_service.index, status: :ok
    end

    def create
      result = @timer_service.create

      if result[:status] == :ok
        render json: {}, status: :ok
      else
        render json: { message: result[:error_message] }, status: :unprocessable_entity
      end
    end

    def update
      result = @timer_service.update

      if result[:status] == :ok
        render json: resource, status: :ok
      else
        render json: { message: result[:error_message] }, status: :unprocessable_entity
      end
    end

    def destroy
      result = @timer_service.destroy

      if result[:status] == :ok
        render json: {}, status: :ok
      else
        render json: {}, status: :not_found
      end
    end

    private
    def set_timer_service
      @timer_service = StopWatchTimerService.new(get_help_ticket, params, scoped_company_user)
    end

    def get_help_ticket
      ['create', 'update'].include?(action_name) ? help_ticket : nil
    end
  end
end
