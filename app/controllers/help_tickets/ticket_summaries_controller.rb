class HelpTickets::TicketSummariesController < ApplicationController
  def summarize
    ticket = HelpTicket.find_by(id: params[:ticket_id])
    GenerateTicketSummaryWorker.perform_async(ticket.id, current_company_user.id)
    
    render json: {}, status: :ok
  end

  def should_display_summary_feature
    service_option = ServiceOption.find_by(service_name: "help_tickets/ai_summary", status: false)
    render json: { display_ai_summary: service_option.present? }, status: :ok
  end
end
