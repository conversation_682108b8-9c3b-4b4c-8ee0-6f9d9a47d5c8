module HelpTickets
  class TicketEmailCountsController < AuthenticatedController
    include MultiCompany::GeneralScoping

    def index
      render json: { count: count }
    end

    private
    def count
      ticket_emails.count
    end

    def email_filter
      @email_filter ||= HelpDesk::InboundEmail::TicketEmailFilter.new([scoped_workspace.id])
    end

    def ticket_emails
      @ticket_emails ||= email_filter.filter
    end
  end
end
