module HelpTickets
  class OpenDurationSummariesController < AuthenticatedController
    def index
      render json: summary_info
    end

    protected
    def summary_info
      sql =<<END
      select
        help_tickets.id,
        help_tickets.created_at,
        (select custom_form_values.value_str
          from custom_form_values
          inner join custom_form_fields
            on custom_form_fields.id = custom_form_values.custom_form_field_id
          where custom_form_fields.name = 'subject'
            and custom_form_values.module_id = help_tickets.id and custom_form_values.module_type = 'HelpTicket' LIMIT 1
        ) as subject
        from help_tickets where help_tickets.company_id = ?
          and help_tickets.archived = false
          and help_tickets.active_ticket_id IS NULL
          and (select custom_form_values.value_str
              from custom_form_values
              inner join custom_form_fields
                on custom_form_fields.id = custom_form_values.custom_form_field_id
              where custom_form_fields.field_attribute_type = ?
                and custom_form_values.module_id = help_tickets.id and custom_form_values.module_type = 'HelpTicket' LIMIT 1) = 'Open'
        group by help_tickets.id
        order by help_tickets.created_at desc
END
      field_type = CustomFormField.field_attribute_types[:status]
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, scoped_company.id, field_type])
      results = ActiveRecord::Base.connection.execute(query)
      results.map do |r|
        {id: r['id'], subject: r['subject'], duration: (DateTime.now - DateTime.parse(r['created_at'].to_s)).to_f }
      end
    end
  end
end
