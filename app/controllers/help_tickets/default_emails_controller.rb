module HelpTickets
  class DefaultEmailsController < AuthenticatedController
    include Utilities::Domains

    before_action :ensure_custom_form

    def index
      render json: { email: custom_form.default_email }
    end

    private
    def custom_form
      @custom_form ||= CustomForm.find_by(id: params[:custom_form_id])
    end

    def ensure_custom_form
      raise "Custom form missing" unless custom_form
    end
  end
end
