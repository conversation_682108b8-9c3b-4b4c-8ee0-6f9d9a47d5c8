# frozen_string_literal: true

class Api::V1::CustomFormAttachmentsController < Api::V1::AuthenticatedController
  def create
    if params[:library_document_id]
      attachments = ::CustomFormAttachmentService.new(scoped_company, scoped_company_user, params, request.post?, resource_params[:library_document_id]).create_from_library
      if attachments.is_a?(Array)
        render json: attachments, status: :ok
      else
        render json: { message: attachments }, status: :unprocessable_entity
      end
    else
      attachment = ::CustomFormAttachmentService.new(scoped_company, scoped_company_user, scoped_workspace, params, request.post?, nil, attachment_params).create_from_file
      if attachment
        render json: { file: attachment }, status: :ok
      else
        render json: {}, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def destroy
    attachment = CustomFormAttachment.find_by(id: params[:id])
    if attachment.present?
      if attachment&.custom_form_value&.module
        cf_attch_service = ::CustomFormAttachmentService.new(scoped_company, scoped_company_user)
        cf_attch_service.set_values(attachment)
        cf_attch_service.create_activity if attachment&.custom_form_value&.help_ticket.present?
        attachment.custom_form_value&.destroy
      else
        attachment&.destroy
      end
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  def fetch_attachments
    attachments = []
    params['values']&.each do |val|
      attachment = CustomFormAttachment.find_by(id: val.to_i)
      if attachment
        attachments << attachment
      end
    end
    render json: { attachments: attachments }, status: :ok
  end

  private

  def attachment_params
    params.require(:custom_form_attachment_attribute).permit(:attachment)
  end

  def resource_params
    params.permit(:library_document_id)
  end
end
