class Api::V1::ForgotPasswordsController < ApplicationController
  def send_reset_password_code
    user = find_user_by_email(params[:email])
    result = ForgotPasswordsService.new(user).send_reset_password_code

    if result[:success]
      render json: {}, status: :ok
    else
      render json: { message: result[:error] }, status: :unprocessable_entity
    end
  end

  def update_password
    user = find_user_by_email(params[:user][:email])
    result = ForgotPasswordsService.new(user).update_password(params)

    if result[:success]
      render json: {}, status: :ok
    else
      render json: { message: result[:error] }, status: :unprocessable_entity
    end
  end

  private

  def find_user_by_email(email)
    User.find_by_cache(email: email.downcase)
  end
end
