# frozen_string_literal: true

class Api::V1::UsersController < Api::V1::AuthenticatedController
  skip_before_action :verify_mfa
  skip_before_action :ensure_free_trial_or_subscribed
  skip_before_action :ensure_confirmed_email
  skip_before_action :ensure_company_agent

  def user_detail
    render json: user_detail_json(current_user)
  end

  def send_email_confirmation
    company = current_user.companies.not_sample.first
    Devise::Mailer.confirmation_instructions(current_user, company).deliver

    render json: {}, status: :ok
  end

  def update_pinned_company_status
    company_id = params["company_id"]
    pinned_company = UserPinnedCompany.find_by(user_id: current_user.id, company_id: company_id)
    if pinned_company
      pinned_company.destroy
    else
      UserPinnedCompany.create!(user_id: current_user.id, company_id: company_id)
    end
    pinned_company_ids = current_user.pinned_companies.pluck(:id)
    render json: { pinned_company_ids: pinned_company_ids }, status: :ok
  end

  private

  def user_detail_json user
    {
      email: user&.email,
      last_name: user&.last_name,
      first_name: user&.first_name,
      full_name: user&.full_name || "Missing name",
      avatar_thumb_url: scoped_company_user.avatar
    }
  end
end
