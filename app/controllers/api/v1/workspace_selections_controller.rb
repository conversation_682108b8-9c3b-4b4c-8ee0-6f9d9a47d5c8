# frozen_string_literal: true

class Api::V1::WorkspaceSelectionsController < Api::V1::AuthenticatedController
  include Api::V1::MobileScoping

  skip_before_action :verify_mfa
  skip_before_action :ensure_free_trial_or_subscribed
  skip_before_action :ensure_confirmed_email
  skip_before_action :ensure_company_agent

  def index
    if params[:company]
      current_workspace = Workspace.find_by(id: params[:id])
      current_company = Company.find_by_cache(id: params[:company]["id"])
      company_user = scoped_mobile_company_user(current_company, current_user)

      render json: login_response(company_user, current_company, current_workspace, current_user, device_token, nil), status: :ok
    end
  end

  private
  def device_token
    request.headers['device-token']
  end
end
