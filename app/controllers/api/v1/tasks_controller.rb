# frozen_string_literal: true

class Api::V1::TasksController < Api::V1::AuthenticatedController
  include Api::V1::MobileScoping
  include MultiCompany::GeneralScoping

  before_action :set_tasks_service

  def index
    response = @tasks_service.get_tasks
    render_response(response)
  end

  def create
    response = @tasks_service.create_task
    render_response(response)
  end

  def show
    response = @tasks_service.show_task
    render_response(response)
  end

  def update
    response = @tasks_service.update_task
    render_response(response)
  end

  def destroy
    response = @tasks_service.destroy_task
    render_response(response)
  end

  private

  def set_tasks_service
    @tasks_service = ::HelpTickets::TasksService.new(params, scoped_company, scoped_workspace)
  end
end
