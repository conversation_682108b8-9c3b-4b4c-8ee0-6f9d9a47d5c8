# frozen_string_literal: true

class Api::V1::AuthenticatedController < ApplicationController
  include MultiCompany::GeneralScoping
  alias devise_current_user current_user

  before_action :authenticate_request
  before_action :verify_mfa
  before_action :ensure_free_trial_or_subscribed
  before_action :ensure_confirmed_email
  before_action :ensure_company_agent

  protect_from_forgery with: :null_session, if: proc { |c| c.request.format == 'application/json' }

  private

  def current_user
    response = AuthorizeApiRequest.call(request.headers)

    @authorization_error ||= response.errors || {}
    @current_user ||= response.result
  end

  def authenticate_request
    if current_user.blank?
      return if @authorization_error.empty?

      render json: { error: 'Unable to authorize user', error_type: @authorization_error[:token][0] }, status: :unauthorized
    end
  end

  def mfa_verification_required?
    device_token = request.headers['device-token']

    if device_token
      mfa_session = UserDevice.find_by(token: device_token)&.mfa_session
      scoped_company_user.mfa_enabled && !mfa_session&.mfa_verified
    end
  end

  def verify_mfa
    if scoped_company&.mfa_enabled && mfa_verification_required? && !current_user.super_admin?
      render json: { error: 'Please verify M<PERSON>', error_type: 'verify_mfa' }, status: :forbidden
    end
  end

  def ensure_free_trial_or_subscribed
    return if scoped_company.blank? || current_user.blank?
    return if current_user.super_admin?
    return if !scoped_company.is_sample_company? && scoped_company.allow_access? && can_access_mobile_or_desktop_app?
    return if scoped_company.is_sample_company? && current_user.has_active_subscription?

    render json: { error: 'Please allow company access', error_type: 'ensure_free_trial_or_subscribed' },
           status: :payment_required
  end

  def ensure_confirmed_email
    if current_user.present? && !current_user.has_confirmed_email? && current_user.created_at < (Rails.application.credentials.unconfirmed_email_days_allowed || 5).to_i.days.ago
      render json: { error: 'Please ensure confirmed email', error_type: 'ensure_confirmed_email' }, status: :bad_request
    end
  end

  def can_access_mobile_or_desktop_app?
    return true if scoped_company.has_current_free_trial? && scoped_company.subscriptions.count == 0

    allowed_modules = ['full_platform', 'it_help_desk']
    subscribed_modules = scoped_company.subscriptions.where.not(status: 'insolvent').pluck(:module_type)
    subscribed_modules.any? { |mod| allowed_modules.include?(mod) }
  end

  def ensure_company_agent
    return if params['is_desktop_app'].present?
    return if current_user.super_admin?
    return if scoped_company_user.contributor.expanded_privileges.find_by(name: "MobileApp")&.permission_type === "write"

    render json: { error: 'Not a company agent', error_type: 'ensure_company_agent' },
           status: :unauthorized
  end
end
