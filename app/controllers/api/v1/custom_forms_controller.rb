# frozen_string_literal: true

class Api::V1::CustomFormsController < Api::V1::AuthenticatedController
  def index
    render json: custom_forms_service.get_custom_forms.to_json, status: :ok
  end

  def show
    custom_form = custom_forms_service.show_custom_form

    if !custom_form.empty?
      render json: custom_form, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  private
  def custom_forms_service
    @custom_forms_service ||= CustomFormsService.new(params, scoped_workspace, scoped_company_user, scoped_company)
  end
end
