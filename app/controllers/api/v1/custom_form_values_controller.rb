# frozen_string_literal: true

class Api::V1::CustomFormValuesController < Api::V1::AuthenticatedController
  include MultiCompany::HelpTicketScoping

  before_action :initialize_custom_form_service, only: [:index, :show, :destroy]

  def index
    render json: @service.get_custom_form_values, status: :ok
  end

  def show
    render json: @service.get_custom_form_value, status: :ok
  end

  def create
    update
  end

  def update
    service = CustomFormValuesService.new(current_user, params, scoped_company, scoped_workspace, module_name, scoped_workspace_ids, 'mobile')

    render_response(service.update_custom_form_value)
  end

  def destroy
    render_response(@service.destroy_custom_form_value)
  end

  def module_name
    "HelpTicket"
  end

  private

  def initialize_custom_form_service
    @service = CustomFormValuesService.new(current_user, params, scoped_company, scoped_workspace, module_name, scoped_workspace_ids)
  end

  def render_response(response)
    render_hash = {}

    if response[:message]
      render_hash[:message] = response[:message]
      render_hash[:is_required] = response[:is_required] if response[:is_required]
    elsif response[:form_value]
      render_hash[:form_value] = response[:form_value]
    end

    render json: render_hash, status: response[:status]
  end
end
