# frozen_string_literal: true

class Api::V1::TicketCommentsController < Api::V1::AuthenticatedController
  include MultiCompany::HelpTicketScoping

  before_action :help_ticket
  before_action :authorize_write_comment, only: :destroy
  before_action :set_ticket_comment

  def index
    render json: {
      comments: @ticket_comment.resources,
      page_count: @ticket_comment.page_count,
      total_comments: @ticket_comment.total_count
    }, status: :ok
  end

  def create
    service = TicketCommentsService.new(params, help_ticket, @ticket_comment, nil, comment_params, time_spent_params)
    response = service.create_comment

    render json: response[:response_body], status: response[:response_status]
  end

  def show
    render json: @ticket_comment.comment_json
  end

  def destroy
    service = TicketCommentsService.new(params, help_ticket, @ticket_comment, @comment, nil, nil, scoped_company_user)
    response = service.destroy_comment

    render json: response[:response_body], status: response[:response_status]
  end

  def update
    service = TicketCommentsService.new(params, help_ticket, @ticket_comment, nil, comment_params, time_spent_params)
    response = service.update_comment

    render json: response[:response_body], status: response[:response_status]
  end

  private
  def ticket_id
    params[:ticket_id] || comment_params[:help_ticket_id]
  end

  def comment_params
    params.require(:help_ticket_comment).permit(
      :comment_text,
      :comment_body,
      :help_ticket_id,
      :private_flag,
      :contributor_id,
      :mute_notification,
      :resolution_flag,
      private_contributor_ids: []
    )
  end

  def time_spent_params
    params.require(:help_ticket_comment).permit(
      time_spent: [
        :time_spent,
        :started_at,
        :company_user_id,
        :id,
        :start_time,
        :end_time
      ]
    )
  end

  def set_ticket_comment
    if params['action'] == 'create' || params['action'] == 'update'
      @ticket_comment = ::HelpTickets::TicketCommentService.new(scoped_company, scoped_company_user, help_ticket, params, comment_params, time_spent_params)
    else
      @ticket_comment = ::HelpTickets::TicketCommentService.new(scoped_company, scoped_company_user, help_ticket, params)
    end
  end

  def total_comments
    help_ticket.help_ticket_comments.where.missing(:time_spent).count
  end
end
