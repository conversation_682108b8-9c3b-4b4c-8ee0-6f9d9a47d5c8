# frozen_string_literal: true

class Api::V1::AbbreviatedTicketsController < Api::V1::BaseTicketsController
  skip_before_action :ensure_free_trial_or_subscribed

  PER_PAGE_DEFAULT = 25

  def index
    if format_json?
      request_email = "helpdesk@#{scoped_company.subdomain}.#{Rails.application.credentials[:root_domain]}"

      render json: {
        tickets: tickets,
        page_count: page_count,
        request_email: request_email,
        total_ticket_count: total_ticket_count,
        current_contributor_ids: current_contributor_ids
      }
    else
      render json: "", status: :not_acceptable
    end
  end

  def format_json?
    request.format.json?
  end
end
