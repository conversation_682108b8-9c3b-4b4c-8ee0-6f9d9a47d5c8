# frozen_string_literal: true

require 'base64'

class Api::V1::AuthenticateUsersController < ApplicationController
  include Api::V1::MobileScoping

  def authenticate
    command = ::AuthenticateMobileUser.call(params[:email], params[:password])

    if command.success?
      render json: json_response(command), status: :ok
    else
      render json: { error: command.errors, error_type: 'authenticate_user' }, status: :unauthorized
    end
  end

  def authenticate_sso_user
    sso_user_info = params[:ms_sso] ? fetch_ms_sso_info : { username: params[:userName], email: params[:email] }

    user_name = sso_user_info[:username]
    email = sso_user_info[:email]

    command = ::AuthenticateMobileUser.call(email, params[:password], user_name)

    if command.success?
      render json: json_response(command), status: :ok
    else
      render json: { error: command.errors, error_type: 'authenticate_user' }, status: :unauthorized
    end
  end

  def user_subdomains
    user = User.find_by_cache(email: params[:email])
    companies = user.present? ? user.companies.where(microsoft_sso_enabled: true).pluck(:subdomain, :name) : []

    render json: { companies: companies }, status: :ok
  end

  def renew_tokens
    dec_token = decoded_auth_token(params[:refresh_token])

    if valid_refresh_token?(dec_token)
      render json: {
        token: encoded_auth_token(@user.id, 8.hours.from_now),
        refresh_token: encoded_auth_token(@user.id, 1.year.from_now)
      }, status: :ok
    else
      render json: { error: 'Invalid refresh token' }, status: :unauthorized
    end
  end

  private

  def fetch_ms_sso_info
    token_headers = {'Authorization' => "Basic " + Base64.strict_encode64("#{client_id}:#{client_secret}")}

    begin
      token_endpoint = client.auth_code.get_token(params[:code], redirect_uri: "genuityhd://", headers: token_headers)
      access_token = token_endpoint.token
      user_info = CognitoService.new().get_user(access_token)

      username = user_info.username
      email = user_info.user_attributes.find {|key| key.name == "email"}.value.downcase

      { username: username, email: email }
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      nil
    end
  end

  def client
    OAuth2::Client.new(client_id, client_secret, oauth_options)
  end

  def client_id
    Rails.application.credentials.aws[:cognito][:client_id]
  end

  def client_secret
    Rails.application.credentials.aws[:cognito][:client_secret]
  end

  def oauth_options
    {
      site: Rails.application.credentials.aws[:cognito][:base_uri],
      authorize_url: '/oauth2/authorize',
      token_url: '/oauth2/token'
    }
  end

  def valid_refresh_token?(dec_token)
    @user ||= User.find(dec_token[:user_id]) if dec_token
  end

  def json_response command
    if command.result[1]
      {
        token: command.result[0],
        multiple: command.result[1],
        refresh_token: command.result[2],
        user_name: command.current_user.full_name
      }
    else
      company_options = get_company_options(command.current_user)
      current_company = get_current_company(company_options)
      workspace_options = get_workspace_options(current_company, command.current_user)
      company_user = scoped_mobile_company_user(current_company, command.current_user)

      login_response company_user, current_company, workspace_options[0], command.current_user, device_token, command.result[0], command.result[2]
    end
  end

  def device_token
    request.headers['device-token']
  end

  def get_current_company(company_options)
    Company.find_cache(id: company_options.find { |x| x[:name] != 'Sample Company' }[:id])
  end

  def get_company_options current_user
    ::Options::CompanyOptions.call(params, current_user)
  end

  def get_workspace_options current_company, current_user
    ::Options::WorkspaceOptions.call(
      params,
      current_user,
      current_company,
      scoped_mobile_company_user(current_company, current_user),
      all_expanded_privileges(current_company, current_user)
    )
  end
end
