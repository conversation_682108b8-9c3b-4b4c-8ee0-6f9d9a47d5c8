# frozen_string_literal: true

class Api::V1::CustomFormTicketsController < Api::V1::AuthenticatedController
  include MultiCompany::CustomFormScoping

  before_action :ensure_custom_form, only: [:create]

  def index
    form_ticket = ::CustomFormTicket.new(scoped_company, params)
    tickets = nil

    return render json: { tickets: [] }, status: :ok unless form_ticket.custom_form
    tickets = form_ticket.tickets
    render json: { tickets: tickets }, status: :ok
  end

  def create
    ticket = CompanyModule::CustomForms::EntityCreate.new(company_module_data: ticket_data).call.custom_entity
    if ticket
      render json: { entity: ticket }, status: :ok
    else
      render json: { entity: {} }, status: :bad_request
    end
  end

  def update
    CompanyModule::CustomForms::EntityUpdate.new(params, nil, nil, params['params']['company_module'], scoped_company_user).call
    help_ticket = HelpTicket.find_by(id: params["id"])
    render json: { entity: help_ticket }, status: :ok
  end

  def ticket_data
    CompanyModule::CustomForms::EntityDataFromController.new(self, nil, is_mobile_app?, is_desktop_app?, scoped_company_user)
  end

  def payload
    @payload ||= JSON.parse(params[:json]).with_indifferent_access
  end

  private
  def ensure_custom_form
    render 'shared/not_found' unless scoped_custom_form
  end

  def is_mobile_app?
    payload[:source] === 'mobile_app'
  end

  def is_desktop_app?
    payload[:source] === 'desktop_app'
  end
end
