# frozen_string_literal: true

class Api::V1::LinkableLinksController < Api::V1::AuthenticatedController
  include MultiCompany::GeneralScoping

  def create
    if !source
      render json: { message: "This item has been removed already." }, status: :bad_request
    elsif params[:target_id].present? && params[:target_id].any?
      delete_linkable_links
      create_linkable_links

      render json: {}, status: :ok
    else
      render json: { message: "Related item has been removed." }, status: :bad_request
    end
  end

  private
  def source
    @source ||= Linkable.find_by(id: params[:source_id], company_id: scoped_company.id)
  end

  def ticket
    @ticket ||= source.linkable_type.constantize.find_by(id: source.linkable_id)
  end

  def delete_linkable_links
    LinkableLink.where(source_id: source.id)
                .where.not(target_id: params[:target_id])
                .each do |linkable_link|
      target_name = linkable_link.target.name
      linkable_link.destroy
      remove_linkable_activity(target_name)
    end
  end

  def create_linkable_links
    params[:target_id].each do |target_id|
      target = Linkable.find_by(company_id: scoped_company.id, id: target_id)
      next unless target.present?

      linkable_link = LinkableLink.find_or_create_by(source_id: source.id, target_id: target.id)
      create_linkable_activity(target.name) if linkable_link.persisted?
    end
  end

  def remove_linkable_activity(target_name)
    my_params = {
      help_ticket_id: ticket.id,
      activity_type: "related_item",
      owner_id: scoped_company_user.id,
      data: {
        currentValue: "",
        previousValue: target_name,
        ticket_subject: ticket.subject,
        activity_label: "Related items",
        ticket_number: ticket.ticket_number
      }
    }

    HelpTicketActivity.create(my_params)
  end

  def create_linkable_activity(target_name)
    my_params = {
      help_ticket_id: ticket.id,
      activity_type: "related_item",
      owner_id: scoped_company_user.id,
      data: {
        previousValue: "",
        currentValue: target_name,
        ticket_subject: ticket.subject,
        activity_label: "Related items",
        ticket_number: ticket.ticket_number,
      }
    }

    HelpTicketActivity.create(my_params)
  end
end
