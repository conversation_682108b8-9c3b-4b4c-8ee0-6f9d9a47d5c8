# frozen_string_literal: true

class Api::V1::CurrentActionableAlertsController < Api::V1::AuthenticatedController
  def index
    actionable_alert = HelpTickets::CurrentActionableAlerts.new(scoped_workspace, scoped_company_user)
    render json: actionable_alert.resources, status: :ok
  end

  def actionable_alerts_count
    actionable_alert = HelpTickets::CurrentActionableAlerts.new(scoped_workspace, scoped_company_user)
    render json: actionable_alert.total_count, status: :ok
  end
end
