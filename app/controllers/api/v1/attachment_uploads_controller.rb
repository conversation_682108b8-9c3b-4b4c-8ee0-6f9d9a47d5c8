# frozen_string_literal: true

class Api::V1::AttachmentUploadsController < Api::V1::AuthenticatedController
  before_action :set_attachment_service

  def create
    render_response(@attachment_service.create_attachment(attachment_params, scoped_company.id, scoped_workspace))
  end

  def destroy
    render_response(@attachment_service.destroy_attachment(scoped_company, params[:id]))
  end

  private
  def set_attachment_service
    @attachment_service = AttachmentUploadService.new
  end

  def attachment_params
    params.permit(:attachment, :attachable_id, :attachable_type)
  end

  def render_response(result)
    if result[:message]
      render json: { message: result[:message] }, status: result[:status]
    elsif result[:attachment]
      render json: { attachment: result[:attachment] }, status: result[:status]
    else
      render json: {}, status: result[:status]
    end
  end
end
