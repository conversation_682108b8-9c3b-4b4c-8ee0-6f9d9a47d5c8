# frozen_string_literal: true

class Api::V1::TimeSpentsController < Api::V1::AuthenticatedController
  include PostgresHelper
  include MultiCompany::HelpTicketScoping

  before_action :set_time_spent, only: [:index, :show, :update, :destroy]
  before_action :ensure_resource, only: [:update, :destroy]
  before_action :set_time_spent_service, only: [:create, :update, :destroy]

  def index
    render json: {
      page_count: page_count,
      total_count: total_count,
      time_spents: @time_spent.resources,
      total_time_spent: total_time_spent
    }, status: :ok
  end

  def create
    resource = @time_spent_service.create_time_spent(resource_attributes)
    if resource.save
      render json: resource, status: :ok
    else
      render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def show
    resource = @time_spent.resource

    render json: {
      resource: resource,
      user_name: resource.company_user.full_name,
      time_spender_id: resource.company_user.contributor_id,
      comment: HelpTicketComment.find_by(id: resource.help_ticket_comment_id)
    }
  end

  def update
    resource = @time_spent.resource
    if @time_spent_service.update_time_spent(resource, resource_attributes)
      render json: resource, status: :ok
    else
      render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def destroy
    resource = @time_spent.resource
    if @time_spent_service.destroy_time_spent(resource, scoped_company_user, help_ticket.id)
      render json: { total_time_spent: total_time_spent }, status: :ok
    else
      render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  private
  def set_time_spent_service
    @time_spent_service = TimeSpentsService.new(scoped_company, help_ticket, params)
  end

  def resource_attributes
    params.require(:time_spent).permit(
                                :hours_spent,
                                :minutes_spent,
                                :started_at,
                                :company_user_id,
                                :start_time,
                                :end_time
                              )
  end

  def page_count
    (total_count / params[:per_page]&.to_f).ceil
  end

  def total_count
    @time_spent.total_count || 0
  end

  def total_time_spent
    help_ticket.total_time_spent
  end

  def ensure_resource
    render json: {}, status: :not_found unless @time_spent.resource
  end

  def set_time_spent
    @time_spent = HelpTickets::TimeSpentService.new(scoped_company, help_ticket, params, true)
  end
end
