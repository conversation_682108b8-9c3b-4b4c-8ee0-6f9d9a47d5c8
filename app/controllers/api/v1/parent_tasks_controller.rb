class Api::V1::ParentTasksController < Api::V1::AuthenticatedController
  include MultiCompany::HelpTicketScoping

  before_action :set_parent_tasks_service

  def create
    if @service.create_parent_task(params[:parent_task_id], params[:sub_task_array])
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  def destroy
    if @service.destroy_parent_task(params[:id])
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  private

  def set_parent_tasks_service
    @service = ParentTasksService.new(help_ticket)
  end
end
