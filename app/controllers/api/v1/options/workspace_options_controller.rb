# frozen_string_literal: true

class Api::V1::Options::WorkspaceOptionsController < Api::V1::Options::OptionsController
  skip_before_action :verify_mfa
  skip_before_action :ensure_free_trial_or_subscribed
  skip_before_action :ensure_confirmed_email
  skip_before_action :ensure_company_agent

  private

  def options
    company = Company.find_by_cache(id: params[:company_id])

    ::Options::WorkspaceOptions.call(
      params,
      current_user,
      company,
      scoped_company_user,
      all_expanded_privileges
    )
  end
end
