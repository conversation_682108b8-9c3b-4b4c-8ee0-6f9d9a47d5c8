# frozen_string_literal: true

class Api::V1::Options::ContributorOptionsController < Api::V1::Options::OptionsController
  before_action :ensure_scoped_company

  def options
    ::Options::ContributorOptions.call(scoped_company, params, scoped_company_user, scoped_workspace, false, true)
  end

  def ensure_scoped_company
    scoped_company
  rescue
    render json: []
  end

  private

  def field_type
    'people_list'
  end
end
