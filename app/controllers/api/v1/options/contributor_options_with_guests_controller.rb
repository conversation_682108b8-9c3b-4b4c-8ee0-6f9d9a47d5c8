# frozen_string_literal: true

class Api::V1::Options::ContributorOptionsWithGuestsController < Api::V1::Options::OptionsController
  before_action :ensure_scoped_company

  def options
    parameters = [scoped_company, params, scoped_company_user, scoped_workspace, true, true]
  
    if is_cache_enabled? && validate_cache_key_requirements(true)
      cache_key = generate_cache_key(scoped_workspace, scoped_company, true, true)
      Rails.cache.fetch(cache_key, expires_in: 8.hours) do
        track_cached_keys(cache_key)
        ::Options::ContributorOptions.call(*parameters)
      end
    else
      ::Options::ContributorOptions.call(*parameters)
    end
  end

  def ensure_scoped_company
    scoped_company
  rescue
    render json: []
  end

  private

  def field_type
    'people_list'
  end
end
