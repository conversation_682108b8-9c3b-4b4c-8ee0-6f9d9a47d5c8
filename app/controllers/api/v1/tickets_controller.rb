# frozen_string_literal: true

class Api::V1::TicketsController < Api::V1::AuthenticatedController
  include Utilities::Domains
  include MultiCompany::HelpTicketScoping
  include ApplicationHelper

  before_action :set_resource, only: [:show, :update_notification_status, :filter_activities, :destroy, :archive, :unarchive, :update_ticket_seen_status], unless: Proc.new { !request.format.json? }
  before_action :set_body_class
  before_action :set_ticket_service, only: [:show, :update_notification_status, :destroy, :archive, :unarchive]

  PER_PAGE_DEFAULT = 25

  def set_body_class
    @body_class = "module module--help-tickets"
  end

  def show
    respond_to do |format|
      format.html { render :index }
      format.json { render json: @ticket_service.show_ticket }
    end
    update_view_session if params[:id] && params[:id].to_i != 0
  end

  def update_notification_status
    @ticket_service.update_notification_status
  end

  def update_ticket_seen_status
    @ticket.update_column(:is_seen, true) if @ticket.present? && !@ticket.is_seen
  end

  def destroy
    ticket = @ticket_service.destroy_ticket
    if ticket.destroy
      render json: ticket
    else
      render json: { error: 'Unable to delete that help ticket.  Please try later.' }, status: :unprocessable_entity
    end
  end

  def archive
    response = @ticket_service.archive_ticket(true)
    render json: render_data(response), status: response[:status]
  end

  def unarchive
    response = @ticket_service.archive_ticket(false)
    render json: render_data(response), status: response[:status]
  end

  private

  def set_ticket_service
    @ticket_service = TicketsService.new(@ticket, params, scoped_company_user)
  end

  def render_data(response)
    response.key?(:message) ? { message: response[:message] } : {}
  end

  def ticket_id
    params[:id]
  end

  def set_resource
    @ticket = help_ticket
  end

  def resolve_helpdesk_root_path
    if request.path.eql?('/help_tickets/dashboard') && !request.path.eql?(helpdesk_root_path)
      redirect_to helpdesk_root_path
    end
  end

  def update_view_session
    if @ticket.present?
      TicketSession.find_or_create_by(window_guid: params[:window_guid], help_ticket_id: params[:id]) do |session|
        session.assign_attributes(
          expired_at: 5.minutes.from_now,
          help_ticket_id: params[:id],
          ip: request.ip,
          rails_guid: session.id
        )
      end
    end
  end
end
