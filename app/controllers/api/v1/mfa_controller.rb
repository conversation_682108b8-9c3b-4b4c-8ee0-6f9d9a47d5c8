class Api::V1::MfaController < Api::V1::AuthenticatedController
  skip_before_action :verify_mfa
  skip_before_action :ensure_free_trial_or_subscribed
  skip_before_action :ensure_confirmed_email
  skip_before_action :ensure_company_agent

  before_action :authenticate_request
  before_action :user_device, only: [:send_code_via_text, :send_code_via_email, :verify]
  before_action :set_mfa_service, only: [:index, :send_code_via_text, :send_code_via_email]

  def index
    render json: @mfa_service.mfa_info
  end

  def verify
    if correct_mfa_code_or_key
      mfa_code_generated_at = mfa_session.mfa_code_generated_at.to_time
      code_generated_past_time = DateTime.current.to_time - mfa_code_generated_at
      if code_generated_past_time <= 5.minutes
        mfa_session.update_columns(
          mfa_verified: true,
          mfa_verified_at: DateTime.current
        )
        render json: { status: true }, status: :ok
      else
        render json: { message: "Code expires in every five minutes. Please enter newly provided code." }, status: :unprocessable_entity
      end
    else
      code_verification_failed
    end
  end

  def code_verification_failed
    attempts = mfa_session.mfa_attempts_remaining - 1
    mfa_session.update_columns(mfa_attempts_remaining: attempts)
    if attempts <= 0
      scoped_company_user.update_columns(locked: true)
      render json: { status: false, message: "Sorry, the user is locked due to multiple incorrect tries, please contact administrator to unlock the user." }, status: :unauthorized
    else
      render json: { status: false, message: "The code you entered does not match the code." }, status: :unprocessable_entity
    end
  end

  def send_code_via_text
    response = @mfa_service.send_code_via_text

    render json: response[:json], status: response[:status]
  end

  def send_code_via_email
    response = @mfa_service.send_code_via_email

    render json: response[:json], status: response[:status]
  end

  private

  def correct_mfa_code_or_key
    mfa_code = params["mfa"]
    if params['verification_with_code']
      mfa_code == mfa_session.mfa_code
    else
      return false if scoped_company_user.mfa_secret_key.nil?
      totp = ROTP::TOTP.new(scoped_company_user.mfa_secret_key)
      totp.verify(params["mfa"])
      start_mfa_session
    end
  end

  def start_mfa_session
    if mfa_session.present?
      mfa_session.update_columns(
        mfa_code: params["mfa"],
        mfa_code_generated_at: DateTime.current
      )
    else
      MfaSession.create!(resource_attributes)
    end
  end

  def set_mfa_service
    @mfa_service = MfaService.new(params, scoped_company_user, user_device.mfa_session&.mfa_code, scoped_company, 'mobile', user_device.id)
  end

  def user_device
    @user_device = find_user_device
  rescue ActiveRecord::RecordNotFound => e
    respond_to do |format|
      format.json { render json: { message: "Device was not found." }, status: :not_found }
    end
  end

  def find_user_device
    UserDevice.find_by(token: device_token)
  end

  def device_token
    request.headers['device-token']
  end

  def mfa_session
    @user_device.mfa_session
  end

  def resource_attributes
    {
      mfa_attempts_remaining: 5,
      user_device_id: user_device.id,
      mfa_code_generated_at: DateTime.current,
      company_user_id: scoped_company_user.id
    }
  end
end
