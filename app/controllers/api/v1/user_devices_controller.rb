# frozen_string_literal: true

class Api::V1::UserDevicesController < Api::V1::AuthenticatedController
  skip_before_action :authenticate_request, only: [:destroy]
  skip_before_action :verify_mfa
  skip_before_action :ensure_free_trial_or_subscribed
  skip_before_action :ensure_confirmed_email
  skip_before_action :ensure_company_agent

  def create
    user_device = UserDevice.find_or_initialize_by(device_info_params.merge({ user_id: current_user.id }))

    if user_device.save
      render json: { fcm_device_token: user_device.token }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def update
    user_device = UserDevice.find_by(token: params[:id], user_id: current_user.id)

    if user_device&.update(device_info_params)
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  def destroy
    user_device = UserDevice.find_by(token: params[:id])

    if user_device&.destroy
      render json: {}, status: :ok
    else
      render json: {}, status: :not_found
    end
  end

  private

  def device_info_params
    params.require(:device_info).permit(:token, :os, :device_type, :screen_size, :app_version)
  end
end
