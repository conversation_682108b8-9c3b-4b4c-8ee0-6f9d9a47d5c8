# frozen_string_literal: true

class Api::V1::TaskChecklistsController < Api::V1::AuthenticatedController
  include Api::V1::MobileScoping
  include MultiCompany::GeneralScoping

  before_action :set_checklists_service

  def index
    response = @checklist_service.get_task_checklists
    render_response(response)
  end

  # The code below is commented because some features have not been added to the mobile app yet.
  # def create
  #   response = @checklist_service.create_task_checklist
  #   render_response(response)
  # end

  # def show
  #   response = @checklist_service.show_task_checklist
  #   render_response(response)
  # end

  # def update
  #   response = @checklist_service.update_task_checklist
  #   render_response(response)
  # end

  # def destroy
  #   response = @checklist_service.destroy_task_checklist
  #   render_response(response)
  # end

  def create_project_task_in_ticket
    response = @checklist_service.create_project_task
    render_response(response)
  end

  private

  def set_checklists_service
    @checklist_service = ::HelpTickets::TaskChecklistsService.new(params, scoped_company, scoped_company_user, scoped_workspace)
  end
end
