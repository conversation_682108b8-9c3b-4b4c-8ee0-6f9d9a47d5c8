# frozen_string_literal: true

class Api::V1::TicketDashboardGraphsController < Api::V1::AuthenticatedController
  include HelpTickets::Dashboard::DashboardHelper

  def index
    begin
      render json: {
        ticket_by_status: ticket_by_status,
        ticket_by_source: ticket_by_source,
        ticket_closed_times: ticket_closed_times,
        ticket_by_assignment: ticket_by_assignment,
        ticket_response_times: ticket_response_times,
        ticket_by_satisfaction: ticket_by_satisfaction
      }
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
    end
  end

  private
  def ticket_by_status
    HelpTickets::Dashboard::Statuses.call(scoped_workspace)
  end

  def ticket_by_source
    HelpTickets::Dashboard::Sources.call(scoped_workspace)
  end

  def ticket_by_assignment
    HelpTickets::Dashboard::Assignments.call(scoped_workspace)
  end

  def ticket_by_satisfaction
    HelpTickets::Dashboard::Satisfaction.call(scoped_workspace)
  end

  def ticket_response_times
    HelpTickets::Dashboard::ResponseTime.call(scoped_workspace, time_frame)
  end

  def ticket_closed_times
    HelpTickets::Dashboard::ClosedTime.call(scoped_workspace, start_date)
  end
end
