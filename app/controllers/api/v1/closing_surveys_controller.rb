# frozen_string_literal: true

class Api::V1::ClosingSurveysController < Api::V1::AuthenticatedController
  skip_before_action :verify_mfa

  before_action :ensure_company_and_ticket_match
  before_action :ensure_email_matches, only: [:update]
  before_action :set_survey_service

  def show
    render json: @survey_service.show_json
  end

  def update
    if @survey_service.update_action.save
      render json: { survey: survey}, status: :ok
    else
      render json: { message: survey.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  private

  def set_survey_service
    @survey_service = ClosingSurveyService.new(help_ticket, params, scoped_company, current_user)
  end

  def resource_params
    params.require(:closing_survey).permit(
      :positive,
      :comment,
      :email
    )
  end

  def survey
    @survey ||= ClosingSurvey.find_or_initialize_by(help_ticket: help_ticket)
  end

  def ensure_company_and_ticket_match
    if scoped_company.id != help_ticket.company_id
      @error_message = "Sorry, but only the creator of the ticket can provide feedback."
      respond_to do |format|
        format.html { render 'help_tickets/error', status: :not_found }
        format.json { render json: { message: @error_message }, status: :not_found }
      end
    end
  end

  def help_ticket
    @help_ticket ||= HelpTicket.find_by(id: params[:id])
  end

  def ensure_email_matches
    if !creator_emails.include?(given_email) && !current_user
      @error_message = "Sorry, but the email must match the person who created the ticket."
      respond_to do |format|
        format.html { render 'help_tickets/error', status: :not_found }
        format.json { render json: { message: @error_message }, status: :not_found }
      end
    end
  end

  def creator_emails
    result = help_ticket.creator_emails
    if result.is_a?(String)
      help_ticket.creator_emails&.strip&.downcase
    elsif result.is_a?(Array)
      result.map { |email| email&.strip&.downcase}
    end
  end

  def given_email
    resource_params[:email]&.strip&.downcase
  end
end
