class Api::V2::BaseController < ApplicationController
  protect_from_forgery with: :null_session, if: Proc.new { |c| c.request.format == 'application/json' }
  attr_reader :current_user

  def authenticate_request
    @current_user = AuthorizeApiRequest.call(request.headers).result
    render json: { error: 'Not Authorized' }, status: :unauthorized unless @current_user
  end

  protected

    def decoded_secret_key
      @company = Company.find_by_cache(guid: params[:companyGuid])
      render json: { error: 'Not Authorized' }, status: :unauthorized unless @company
    end

    def http_auth_header
      @username = nil, @password = nil
      authenticate_with_http_basic { |u, p|
        @username = u
        @password = p
      }
    end

    def response_obj obj
      respond_to do | format |
        if obj.present? and @current_user.present?
          format.json{ return render json: {result: obj}, status: :ok }
        else
          format.json{ return render json: false, status: :not_found }
        end
      end
    end
end
