class Api::V2::SessionsController < Api::V2::BaseController
  # skip_before_action :authenticate_request, only: [:retrieve_token]
  before_action :http_auth_header, only: [:retrieve_token]

  def retrieve_token
    command = AuthenticateUser.call(@username, @password, params[:company_id], params[:subdomain], params[:login_code])
    if command.success?
      result command
    else
      render json: { error: command.errors }, status: :unauthorized
    end
  end

  def token_verified
    response_obj params[:_token]
  end

  private
    def result command
      respond_to do | format |
        format.json { return render json: { companies: command.result[0], multiple: true }, status: :ok } if ! command.result[0].is_a?(String)
        format.json { return render json: login_result(command)}
      end
    end

    def login_result command
      {_token: command.result[0], company_id: (command.result[1] || params[:company_id]), company_guid:  command.result[2], company_user_guid:  command.result[3],  multiple: false}
    end
end
