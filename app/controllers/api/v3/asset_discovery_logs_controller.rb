class Api::V3::AssetDiscoveryLogsController < ApplicationController
  include DiscoveredManagedAssetFinder

  def create
    @company = Company.find_by_cache(guid: params['company_guid'])
    if @company
      Logs::AssetDiscoveryLog.create(attachment: params['file'], company: @company, source: source, locationable_type: location_type, locationable_id: location&.id)
      render json: {message: "File uploaded successfully"}, status: 200
    else
      render json: {message: "Company not found"}, status: :not_found
    end
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
    render json: {message: "Oops.. Something went wrong on our side. Please contact admin."}, status: :unprocessable_entity
  end

  private
  def source
    params[:source]
  end

  def location_type
    'AgentLocation'
  end

  def location
    comp_locations =  @company.agent_locations
    app_id =  params['app_id']
    mac_addresses = params['mac_addresses'] ? JSON.parse(params['mac_addresses'])['macList'] : []
    find_agent_probe_location(comp_locations,
                              app_id,
                              get_valid_serial(params['pc_serial_no']),
                              mac_addresses,
                              params['computer_name'],
                              params['manufacturer'])
  end
end
