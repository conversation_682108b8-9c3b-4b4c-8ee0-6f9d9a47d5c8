# frozen_string_literal: true

class Api::V3::CompanyUsersController < ApplicationController
  def json_request?
    request.format.json?
  end

  def create
    body = request.body.read
    json_request = JSON.parse(body)
    company_users = []
    ActiveRecord::Base.transaction do
      json_request['companyUsers'].each do |company_user_json|
        service = CompanyUserService.new(company_user_json)
        service.update
        company_user = service.company_user.as_json
        company_user.delete(:company)
        company_users << company_user
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
    render json: company_users
  rescue => e
    render json: {error: e.message}
  end
end
