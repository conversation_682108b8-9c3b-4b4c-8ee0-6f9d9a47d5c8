class Api::V3::MacAppVersionsController < ApplicationController
  def index
    if app_build_number.eql?(latest_build_number)
      render json: { message: "all set for now!", need_update: false }, status: :ok
    else
      render json: { message: "needs to update", need_update: true, new_version: latest_build_number }, status: :ok
    end
  rescue => e
    Bugsnag.notify(e) unless Rails.env.development?
    render json: { message: "Oops.. Something went wrong on our side. Please contact admin." }, status: :unprocessable_entity
  end

  private

  def app_source
    params[:source]
  end

  def is_agent?
    app_source.eql?('Mac Agent')
  end

  def is_self_onboarding?
    app_source.eql?('Mac SelfOnboarding')
  end

  def app_build_number
    params["build_number"]
  end

  def latest_build_number
    if is_agent?
      AppVersion.MAC.order(:created_at).last&.version&.to_i&.to_s
    elsif is_self_onboarding?
      WindowsExe.MAC.order(:created_at).last&.version&.to_i&.to_s
    end
  end
end
