# frozen_string_literal: true

class Api::V3::BaseAssetsController < ApplicationController
  include HandleCompanyCacheKeys
  include ReadReplicaDb

  def create
    @json_array = JSON.parse(request.body.read)
    if is_valid_request?
      if bg_mode.nil? || bg_mode
        AssetCreation::ManagedAsset::SyncDataWorker.perform_async(@json_array)
        render json: { application_id: managed_asset["appId"] }, status: :ok if is_agent_application?
      else
        agent_location = CreateManagedAssetService.new.create_managed_assets(@json_array)
        render json: { application_id: agent_location&.system_uuid }, status: :ok if is_agent_application?
      end
    else
      return
    end
  end

  private

  def managed_asset
    @managed_asset ||= @json_array[0]["managedAsset"]
  end

  def valid_app_release
    if is_cache_enabled?('desktop_app_release')
      Rails.cache.fetch(cache_key, expires_in: 8.hours, skip_nil: true) do
        fetch_app_release
      end
    else
      fetch_app_release
    end
  end

  def fetch_app_release
    set_read_replica_db do
      AppRelease.where(app_type: app_type,
                       app_name: extract_app_name,
                       is_admin_app: is_admin_app?)
                .last
    end
  end

  def app_type
    managed_asset['os'] == 'MAC OS' ? 'MAC' : 'Windows'
  end

  def is_admin_app?
    value = @json_array[0]['is_admin_app']
    value.nil? || value.to_s == "true"
  end

  def is_valid_request?
    valid_params? && valid_app_release.app_enabled && valid_app_release.version == app_version
  end

  def valid_params?
    managed_asset['companyGuid'].present? &&
    managed_asset['source'].present? &&
    managed_asset['os'].present?
  end

  def app_version
    @app_version ||= @json_array[0]["app_version"] || @json_array[0].dig("managedAsset", "app_version")
  end

  def is_agent_application?
    managed_asset["source"]&.downcase == 'agent'
  end

  def extract_app_name
    managed_asset["source"]&.downcase == 'selfonboarding' ? 'self_on_boarding' : managed_asset["source"]
  end

  def cache_key
    if app_type == 'Windows'
      "Windows_#{extract_app_name}_app_release"
    elsif is_agent_application?
      is_admin_app? ? 'MAC_agent_admin_app_release' : 'MAC_agent_non_admin_app_release'
    else
      'MAC_self_on_boarding_app_release'
    end
  end

  def bg_mode
    # Added both cases for bg mode to handled the code of old and new versions mac agent.
    @bg_mode ||= @json_array[0]["bg_mode"] || @json_array[0]["bgMode"]
  end
end
