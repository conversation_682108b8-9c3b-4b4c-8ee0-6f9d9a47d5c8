# frozen_string_literal: true

class Api::V3::DiscoveredAssetsController < ApplicationController
  include AppVersionUtils
  include HandleCompanyCacheKeys
  include ReadReplicaDb

  def create
    if is_request_allowed?
      if params[:bg_mode].nil? || params[:bg_mode]
        AssetCreation::DiscoveredAsset::SyncDataWorker.perform_async(params.to_json)
      else
        CreateDiscoveredAssetService.new.create_discovered_assets(params.to_json)
      end
    else
      return
    end
  end

  def app_name
    "network_discovery"
  end

  def app_release
    if is_cache_enabled?('desktop_app_release')
      cache_key = "Windows_#{app_name}_app_release"
      Rails.cache.fetch(cache_key, expires_in: 8.hours, skip_nil: true) do
        AppRelease.Windows.where(app_name: app_name).last
      end
    else
      set_read_replica_db do
        AppRelease.Windows.where(app_name: app_name).last
      end
    end
  end

  def is_request_allowed?
    app_release&.app_enabled && app_release&.version == params["app_version"]
  end
end
