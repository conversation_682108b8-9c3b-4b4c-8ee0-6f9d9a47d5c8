class Api::V3::StaffImportServiceLogsController < ApplicationController
  def create
    @company = Company.find_by_cache(guid: params['company_guid'])
    if @company
      file_data = {
        original_filename: params['file'].original_filename,
        content_type: params['file'].content_type,
        base64_data: Base64.encode64(params['file'].read)
      }
      log_params = { file_data: file_data, company_id: @company.id }
      LogCreationWorker.perform_async('Logs::StaffImportServiceLog', log_params.to_json)
      render json: {message: "File uploaded successfully"}, status: :ok
    else
      render json: {message: "Company not found"}, status: :not_found
    end
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
    render json: { message: "Something went wrong on our side. Please contact admin." }, status: :unprocessable_entity
  end
end
