# frozen_string_literal: true

class Api::V3::ProbeLocationsController < ApplicationController
  include DiscoveredManagedAssetFinder

  def create
    @company = Company.find_by_cache(guid: params['company_guid'])
    if @company.present?
      @probe_location = find_agent_probe_location(@company.probe_locations,
                                                  params['system_uuid'],
                                                  params['pc_serial_no'],
                                                  params['mac_addresses'],
                                                  params['computer_name'],
                                                  params['manufacturer'])

      @probe_location ||= ProbeLocation.new(company_id: @company.id)
      @probe_location.assign_attributes(
        computer_name: params['computer_name'],
        mac_addresses: params['mac_addresses'],
        manufacturer: params['manufacturer'],
        secondary_mac_addresses: params['sec_mac_addresses'],
        system_uuid: params['system_uuid'],
        ip_address: params['ip_address'],
        app_version: params['app_version'],
        machine_serial_number: params['pc_serial_no'],
        last_scanned_at: @probe_location.last_scanned_at.present? ? @probe_location.last_scanned_at : nil
      )
      @probe_location.save!
    else
      render json: {message: "Company not found"}, status: :not_found
    end
    log_params = { response: params.to_s, company_id: @company.id, source: "probe", locationable_type: "ProbeLocation", locationable_id: @probe_location&.id }
    LogCreationWorker.perform_async('Logs::AssetDiscoveryLog', log_params.to_json)
  end
end
