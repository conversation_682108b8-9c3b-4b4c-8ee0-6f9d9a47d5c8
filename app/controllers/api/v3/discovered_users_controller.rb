class Api::V3::DiscoveredUsersController < ApplicationController
  def create
    log_users_discovery
    if company.present?
      user_service = DiscoveredUserService.new(company, users_data)
      user_service.handle_users

      render_user_service_response(user_service)
    else
      render_not_found
    end
  end

  private

  def log_users_discovery
    log_params = { response: params.to_s, company_id: company.id }
    LogCreationWorker.perform_async('Logs::StaffImportServiceLog', log_params.to_json)
  end

  def render_user_service_response(user_service)
    if user_service.errors.present?
      render json: { message: user_service.errors }
    end
  end

  def render_not_found
    render json: { message: "Company not found" }, status: :not_found
  end

  def company
    @company ||= Company.find_by_cache(guid: params['company_guid'])
  end

  def users_data
    JSON.parse(params['data'].to_json)
  end
end
