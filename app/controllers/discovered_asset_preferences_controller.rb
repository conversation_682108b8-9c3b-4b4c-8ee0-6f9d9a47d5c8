class DiscoveredAssetPreferencesController < ApplicationController
  around_action :set_read_replica_db, only: [:index]

  def index
    render json: { table_data: table_data}, status: :ok
  end

  def update
    asset_preference = DiscoveredAssetPreference.find_by(company_id: current_company.id)
    asset_preference.assign_attributes(preference: params[:selected_columns].presence || asset_preference.preference)

    if asset_preference.save
      render json: { message: "Discovered Asset preferences have been saved successfully" }, status: :ok
    else
      render json: { message: asset_preference.errors.to_sentence }, status: :unprocessable_entity
    end
  end

  private

  def table_data
    default_cols = ManagedAsset::DISC_ASSET_UNSELECTED_COLUMNS
    selected_cols = current_company.discovered_asset_preference.preference
    unselected_cols = default_cols.reject { |col| selected_cols.pluck("id").include?(col[:id]) }.compact

    {
      asset_preference: current_company.discovered_asset_preference,
      default_selected_cols: selected_cols,
      default_unselected_cols: unselected_cols,
    }
  end
end
