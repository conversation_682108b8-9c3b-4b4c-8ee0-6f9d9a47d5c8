class AuthorizeApiRequest
  prepend SimpleCommand
  include Api::V1::MobileScoping

  def initialize(headers = {})
    @headers = headers
  end

  def call
    user
  end

  private

  attr_reader :headers

  def user
    auth_token = decoded_auth_token_from_header
    return nil if auth_token.nil?

    decoded_token = decoded_auth_token(auth_token)
    return nil unless decoded_token_valid?(decoded_token)

    @user ||= User.find(decoded_token[:user_id])
    @user || errors.add(:token, 'authenticate_request') && nil
  end

  def decoded_token_valid?(decoded_token)
    return true if decoded_token && decoded_token[:exp] > Time.now.to_i

    errors.add(:token, 'invalid_token')
    false
  end

  def decoded_auth_token_from_header
    if headers['Authorization'].present?
      return headers['Authorization'].split(' ').last
    else
      errors.add(:token, 'invalid_token') && nil
    end
  end
end
