class AuthenticateUser
  prepend SimpleCommand

  def initialize(email, password, company_id, subdomain, login_code)
    @email = email if email.present?
    @password = password
    @company_id = company_id
    @subdomain = subdomain
    @login_code = login_code if login_code.present?
  end

  def call
    result_obj = user
    return [result_obj] if ! result_obj[0].instance_of?(User)
    [JsonWebToken.encode(user_id: result_obj[0].id), result_obj[1], result_obj[2], result_obj[3]] if result_obj[0]
  end

  private

  attr_accessor :password, :company_id, :current_user, :login_code, :user_guid

  def user
    if current_user.present? && valid_user?(current_user)
      companies = multiscope_user
      user_companies_count = companies.try(:count) || 0
      if user_companies_count == 1
        current_company = current_user.companies.find_cache(id: companies.first[:id])
        current_company_user_guid = current_user.company_users.where(company_id: current_company.id).first.guid

        return [current_user, current_company.id, current_company.guid, current_company_user_guid] unless company_id.present?
        return [current_user, company_id, Company.find_by_cache(id: company_id).guid, current_user.company_users.where(company_id: company_id).first.guid]
      elsif user_companies_count == 0
        errors.add :no_company, 'could not find any company.'
        return [nil]
      end
      return companies if user_companies_count > 1
    else
      errors.add :user_authentication, 'invalid credentials'
      return [nil]
    end
  end

  def current_user
    @current_user = User.find_by_cache(email: @email)
    @current_user ||= User.find_by_cache(guid: decoded_login_code) if login_code.present?
    @current_user
  end

  def valid_user? current_user
    if @email.present? && @password.present?
      @client = Aws::CognitoIdentityProvider::Client.new(
        region: Rails.application.credentials.aws[:region],
        access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
        secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
      )

      resp = validate_from_cognito current_user.email

      case resp
      when "success"
        return true
      when "fail"
        return false
      end
    else
      true
    end
  end

  def multiscope_user
    if @company_id.present?
      return [ Company.not_sample.where(id: @company_id).select(:id, :name).first ]
    elsif @subdomain.present?
      return [ Company.not_sample.where(subdomain: @subdomain).select(:id, :name).first ]
    end
    res = Company.not_sample.joins(:users).where("users.email = ?", parsed_email).select(:id, :name)
    companies = res.collect { |company|
      if company.present?
        {:id => company.id, :name => company.name}
      end
    }
  end

  def parsed_email
    current_user&.email || @email || ""
  end

  def compact companies
    companies_collection = []
    companies.map { |c| if c.present?
        companies_collection << c
                        end
    }
  end

  def validate_from_cognito username
    ret = "success"
    begin
      @resp = @client.initiate_auth({
        client_id: Rails.application.credentials.aws[:cognito][:client_id],
        auth_flow: "USER_PASSWORD_AUTH",
        auth_parameters: {
          "USERNAME" => username,
          "PASSWORD" => password,
          "SECRET_HASH" => secret_hash
        }
      })
    rescue Aws::CognitoIdentityProvider::Errors::UserNotFoundException => e
      ret = "fail"
    rescue
      ret = "fail"
    end
    ret
  end

  def decoded_login_code
    crypt = ActiveSupport::MessageEncryptor.new(Rails.application.secrets.secret_key_base[0..31])
    @user_guid = crypt.decrypt_and_verify(login_code)
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
    errors.add :user_authentication, 'invalid credentials'
    "missing"
  end

  def secret_hash
    Base64.encode64(OpenSSL::HMAC.digest('sha256', Rails.application.credentials.aws[:cognito][:client_secret], "#{current_user.email}#{ Rails.application.credentials.aws[:cognito][:client_id]}" )).gsub("\n", "")
  end
end
