class AuthenticateMobileUser
  prepend SimpleCommand
  include Api::V1::MobileScoping

  attr_accessor :password

  def initialize(email, password, user_name = nil)
    @email = email
    @password = password
    @user_name = user_name
  end

  def call
    validation_result = validate_user
    return [validation_result] unless validation_result[0].is_a?(User)

    user_id = validation_result[0].id
    [encoded_auth_token(user_id, 8.hours.from_now), validation_result[1], encoded_auth_token(user_id, 1.year.from_now)]
  end

  def current_user
    @current_user ||= User.find_by_cache(email: @email)
  end

  private

  def validate_user
    if current_user.present? && is_valid_user?
      user_companies_count = user_companies.try(:count) || 0

      if user_companies_count == 0
        errors.add :no_company, 'could not find any company.'
        return [nil]
      elsif user_companies_count == 1
        return [current_user, false]
      elsif user_companies_count > 1
        return [current_user, true]
      end
    else
      errors.add :user_authentication, 'invalid credentials'
      return [nil]
    end
  end

  def is_valid_user?
    if @email.present? && @password.present?
      return validate_from_cognito current_user.email
    elsif @user_name.present?
      return validate_sso_from_cognito(@user_name, 0)
    else
      return false
    end
  end

  def user_companies
    res = Company.not_sample
    res = res.joins(:users).where("users.email = ?", parsed_email).select(:id, :name) unless current_user.super_admin?

    companies = res.collect { |company|
      if company.present?
        {
          :id => company.id,
          :name => company.name
        }
      end
    }
  end

  def validate_sso_from_cognito(user_name, count)
    begin
      count += 1
      @resp = client.admin_get_user({
        username: user_name,
        user_pool_id: user_pool_id
      })
      CognitoLog.create(
        status: "success",
        user_email: user_name,
        user_id: current_user&.id,
        api_type: 'admin_get_user',
        response: @resp.to_h.to_json
      )
      true
    rescue Aws::CognitoIdentityProvider::Errors::UserNotFoundException
      count == 1 ? validate_sso_from_cognito(@email, count) : false
    rescue StandardError => e
      notify_bugsnag(e)
      false
    end
  end

  def validate_from_cognito user_email
    begin
      @resp = client.initiate_auth({
        client_id: cognito_client_id,
        auth_flow: "USER_PASSWORD_AUTH",
        auth_parameters: {
          "USERNAME" => user_email,
          "PASSWORD" => password,
          "SECRET_HASH" => secret_hash(user_email)
        }
      })
      true
    rescue Aws::CognitoIdentityProvider::Errors::UserNotFoundException,
           Aws::CognitoIdentityProvider::Errors::NotAuthorizedException => e
      false
    rescue StandardError => e
      notify_bugsnag(e)
      false
    end
  end

  def client
    @client ||= Aws::CognitoIdentityProvider::Client.new(
      region: Rails.application.credentials.aws[:region],
      access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
    )
  end

  def secret_hash(user_email)
    secret = Rails.application.credentials.aws[:cognito][:client_secret]
    Base64.encode64(OpenSSL::HMAC.digest('sha256', secret, "#{user_email}#{cognito_client_id}")).gsub("\n", "")
  end

  def cognito_client_id
    Rails.application.credentials.aws[:cognito][:client_id]
  end

  def user_pool_id
    Rails.application.credentials.aws[:cognito][:user_pool_id]
  end

  def parsed_email
    current_user&.email || @email || ""
  end

  def notify_bugsnag(error)
    Bugsnag.notify(error) if Rails.env.staging? || Rails.env.production?
  end
end
