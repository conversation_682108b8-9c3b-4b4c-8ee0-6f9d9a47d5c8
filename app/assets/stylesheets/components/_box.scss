.box,
.half-box {
  align-items: center;
  background-color: $themed-box-bg;
  border-radius: $border-radius;
  box-shadow: $shadow-base;
  color: $themed-base;
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  overflow: visible;
  padding: map-get($spacers, 5) / 2;
  position: relative;

  &:hover {
    color: $themed-base;
    text-decoration: none;
  }
}

.box--block {
  background-color: $themed-box-bg;
  border-radius: $border-radius;
  box-shadow: $shadow-base;
  display: block;
  padding: map-get($spacers, 5) / 2;
}

.box--extra-padding {
  padding: map-get($spacers, 4);
}

.box--flat {
  border: 1px solid $themed-moderate-lighter;
  box-shadow: none;
}

.box--square-border {
  border-radius: 0;
}

.box--transparent {
  background-color: transparent;
  box-shadow: none;
}

.box--with-title {
  height: calc(100% - 2.5rem); // Assumes .h5.mb-3 title above the box
}

.box--with-short-title {
  height: calc(100% - 2rem); // Assumes .h5.mb-2 title above the box
}

// This starts at an opacity of 0, so the opacity will need to be changed to var(--themed-box-hover-opacity) wherever this is used and should be visible.
%box-elevation-overlay {
  background: $white;
  border-radius: $border-radius;
  content: '';
  height: 100%;
  left: 0;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  transition: $transition-base;
  top: 0;
  width: 100%;
}

.box--with-hover {
  cursor: pointer;
  position: relative;
  transition: $transition-base;

  // excluding tr elements due to content shift when applied to some tables
  &:not(tr):before {
    @extend %box-elevation-overlay;
  }

  // Add elevation overlay effect for themed hover
  &:hover {
    box-shadow: $shadow-hover;

    &:not(tr):before {
      opacity: var(--themed-box-hover-opacity);
    }
  }

  // Apply the themed overlay to the tds for tr elements to avoid content shift on hover
  &:is(tr) {
    > td:not(.table-data--sticky) {
      position: relative;

      &:before {
        @extend %box-elevation-overlay;
        border-radius: 0;
        z-index: -1;
      }
    }
  
    &:hover {
      > td:not(.table-data--sticky):before {
        opacity: var(--themed-box-hover-opacity);
      }
    }
  }
}

.box--disabled {
  box-shadow: none;
  cursor: default;
  background-color: $themed-light;
  opacity: 0.5;

  &:hover {
    box-shadow: none;
  }
}

.box--disabled-overlay {
  align-items: center;
  background: $overlay-light;
  border-radius: $border-radius;
  display: flex;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  text-align: center;
  top: -2.25rem;
  width: 100%;
  z-index: 1;
}

.featured-box {
  border-top: 3px solid $primary;
}

@each $theme-color, $color in $theme-colors {
  .featured-box--#{$theme-color} {
    border-top: 3px solid $color;
  }
}

.half-box {
  height: calc(50% - 0.9375rem); // 1.875/2

  &:last-child {
    margin-top: 1.5rem;
  }
}

.half-box--with-title {
  height: calc(50% - 2.1875rem);
}

.half-box--with-short-title {
  height: calc(50% - 1.75rem); // Half of the title height (2rem) + half of the margin height (1.875rem)
}

.box__inner,
.box__footer {
  display: block;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
}

.box__footer {
  .box--full-footer & {
    border-bottom-left-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
    margin-bottom: -#{map-get($spacers, 5) / 2};
    margin-left: -#{map-get($spacers, 5) / 2};
    margin-right: -#{map-get($spacers, 5) / 2};
    width: calc(100% + #{map-get($spacers, 5)});
  }
}

.box--natural-height {
  height: auto;
}

.box-locked-icon {
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
}

.box--selected {
  box-shadow: $shadow-base, inset 0 0 0 2px $primary;

  &.box--disabled,
  &.box--flat,
  &.box--transparent {
    box-shadow: inset 0 0 0 2px $primary;
  }
  
  &.box--with-hover:hover:not(.box--disabled) {
    box-shadow: $shadow-hover, inset 0 0 0 2px $primary;
  }
}

.box__source {
  background-color: $themed-light;
  border-radius: $border-radius/2 0 $border-radius 0;
  bottom: 0;
  font-size: 0.6875rem;
  padding: 0.125rem 0.25rem;
  position: absolute;
  right: 0;
  vertical-align: text-bottom;

  &.left_box {
    right: unset;
    left: 0;
    border-radius: 0 0.25rem 0 0.5rem;
  }
}

/* 
 *  Boxes with Headings include a simple version of the maastricht blue bar on top of the primary content.
 *  Example markup for .box--with-heading:
 *  <div class="box box--with-heading">
 *    <h6 class="box__heading">This Box Looks Great</h6>
 *    <div class="box__inner">Main Box Content</div>
 *  </div>
 */

.box--with-heading,
.box--with-header {
  height: auto;
  padding: 0 0 0 0;

  > .box__inner {
    padding: map-get($spacers, 5) / 2;
    border-bottom-left-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
  }
}

.box__heading,
.box__header {
  margin: 0;
  padding: 0.75rem 1rem;
  width: 100%;
}

.box__heading {
  background-color: $themed-dark-drawer-bg;
  border-radius: $border-radius $border-radius 0 0;
  color: $white;
}

.box__subheader {
  padding: 1rem;
  position: relative;
  width: 100%;
  z-index: 100; // Ensures the subheader is above the box__inner content
}

.box--display-container,
.box--flat {
  .box__header,
  .box__heading {
    margin-left: -1px;
    margin-right: -1px;
    margin-top: -1px;
    width: calc(100% + 2px);
  }
}

.box--display-container {
  @extend .box--with-heading;
  @extend .box--flat;
  background: $themed-light;

  // Featured container boxes are currently just used for toggle-view layouts, which handles padding on their own.
  .box__inner {
    padding: 0;
  }
}

.box--display-height {
  flex-direction: column;
  flex-wrap: nowrap;

  // Content will still overflow from this, so inner heights will need to be set on a per case basis.
  // Currently the scrollable table is-contained should correctly accomomodate the overflow without adjustment.
  max-height: calc(100vh - #{$contained-content-min-margin + $module-header-height} - var(--module-content-margin, #{$module-content-default-margin}));

  .module-vertical-nav & {
    // This subtracts the default top page and bottom page margin from the height of the box, as well as the expected header height and the margin between header and module content.
    // This should rarely need to be overridden as everything except the distance between header and module content should stay consistent. As such,
    // The module-content margin accepts a variable that can be passed in as a style attribute wherever the margin itself is set. For example:
    // <div class="help-tickets-list mt-4" style="--module-content-margin: 1.5rem;">
    max-height: calc(100vh - #{$contained-content-min-margin + $module-header-height--vertical-nav} - var(--module-content-margin, #{$module-content-default-margin}));
  }

  > .box__inner {
    display: flex;
    flex: 1; // Forces the inner content to fill the available space after any box__header or box__subheader
    flex-direction: column; // Ensures the inner content still acts like a 100% width block element
    min-height: 0; // Prevents flex children from overflowing their parent
    overflow: hidden; // Prevents the page scrollbar from appearing due to height of nested content. (Flex box results in a scrollable parent even when parent heights are limited.)
    width: 100%; // Ensures the inner content still acts like a 100% width block element
  }
}

@media only screen and (max-height: 820px) {
  // This is a little silly to have the class and not the class wrapper, but it prevents the harsher !important being used and makes later scoping more flexible.
  body.module-vertical-nav .box--display-height,
  body:not(.module-vertical-nav) .box--display-height {
    max-height: calc(100vh - #{$contained-content-min-margin});
  }
}

// Ideally the immediate descendant of the box__inner is the scrollable container.  
// However, due to some wrapping elements in items like the table, and due to the markup change from scrollbar js, this is not always the case. 
// Place this class on all descendants of the box__inner that wrap the primary scrollable element.
.accomodate-inner-box-scrolling-content {
  display: flex;
  min-height: 0;
  min-width: 0;

  > * {
    flex: 1;
    min-height: 0;
    min-width: 0;
  }
}

.box__header {
  @extend .box__heading;

  .box-title {
    align-items: center;
    display: flex;
    margin-bottom: 0;
  }

  .box-title__icon {
    font-size: 1.125rem;
    margin-right: 0.5rem;
    opacity: 0.65;
  }
}
