.filter-section {
  background-color: $themed-lighter;
}

.filter-item {
  color: $themed-secondary;
  line-height: 3rem;
  letter-spacing: 0.5px;
  vertical-align: middle;
  transition: $transition-base;
  position: relative;

  a {
    display: block;
    color: $themed-secondary;

    &:hover {
      text-decoration: none;
    }
  }
  &.active,
  &:hover {
    cursor: pointer;
    box-shadow: $shadow-base;
    border-right: 2px solid orange;
    background: white;
  }
}

.display-callout-border {
  border-left: 5px solid orange;
}

.select-per-page-filter {
  font-size: 0.875rem;
  padding: 0;
  padding-left: 0.3125rem;
  width: 3.5rem;
  min-height: 1.875rem;
  max-height: 1.875rem;

  @media only screen and (-webkit-min-device-pixel-ratio : 1.5), only screen and (min-device-pixel-ratio : 1.5) {
    padding-top: 0;
  }
}

kbd {
  color: $themed-base;
  font-size: .75rem;
  padding: 0.125rem 0.25rem;
}

.onboarding-rounded-circle, .rounded-circle--step {
  border-radius: 50% !important;
  height: 30px;
  line-height: 30px;
  width: 30px;
}
