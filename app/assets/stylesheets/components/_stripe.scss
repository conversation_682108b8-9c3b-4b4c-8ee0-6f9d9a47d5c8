.StripeElement {
  background-clip: padding-box;
  background-color: #fff;
  border: 1px solid $themed-fair;
  border-radius: 0.5rem;
  color: $themed-base;
  display: block;
  font-size: 0.875rem;
  height: calc(2.25rem + 2px);
  line-height: 1.5;
  padding: 0.375rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 100%;

  &:focus {
    background-color: #fff;
    border-color: $blue-light;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    color: $themed-secondary;
    outline: 0;
  }
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 $themed-fair;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}
