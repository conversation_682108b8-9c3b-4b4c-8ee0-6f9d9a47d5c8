.module-breadcrumb {
  background-color: transparent;
  border-radius: 1rem;
  display: inline-block;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  margin-left: -0.5rem;
  padding: 0 0.5rem;
  vertical-align: top;

  .displayed-label {
    font-size: 0.75rem !important;
  }
}

.breadcrumb-separator {
  color: rgba(var(--themed-dropdown-hover-bg-rgb-values), .2);

  @each $module, $color in $module-colors-dark {
    .module--#{$module} & {
      color: $color;
      opacity: 0.5;
    }
  }
}

.breadcrumb-link {
  color: $themed-muted;

  @each $module, $color in $module-colors-dark {
    .module--#{$module} & {
      color: var(--themed-#{$module}-dark);
    }
  }

  &:hover {
    text-decoration: underline;
  }

  &.router-link-exact-active {
    color: $themed-secondary;
  }
}
