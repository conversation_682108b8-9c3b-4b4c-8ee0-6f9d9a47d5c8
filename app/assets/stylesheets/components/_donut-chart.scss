.donut-wrapper {
  position: relative;
}

.inner-donut-data {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
}

.category-label {
  border-bottom: 1px solid $themed-light;
  color: $themed-muted;
  cursor: pointer;

  &:last-child {
    border-bottom: 0;
  }

  &:hover {
    background-color: $themed-lighter;
  }

  &.is-active,
  &.is-active:hover {
    background-color: $themed-light;
  }
}


.custom-legend {
  border-radius: 50%;
  display: inline-block;
  height: 0.5rem;
  margin-right: 0.5rem;
  width: 0.5rem;
}
