.page-link {
  background-color: transparent;
  border: 0;
  position: relative;

  .active & {
    background-color: transparent;
    border: 0;
    color: $themed-link;
    font-weight: bold;
  }

  .prev-item & {
    font-size: 0;
    min-height: 1.5625rem;
    min-width:  1.5625rem;

    &:before {
      border: solid transparent;
      border-color: rgba(255, 255, 255, 0);
      border-right-color: $secondary;
      border-width: 0.3125rem;
      content: " ";
      left: 0.3125rem;
      position: absolute;
      pointer-events: none;
      top: 0.5rem;
    }


    &:hover {
      background: $themed-light;
    }
  }

  .next-item & {
    font-size: 0;
    min-height: 1.5625rem;
    min-width:  1.5625rem;

    &:before {
      border: solid transparent;
      border-color: rgba(255, 255, 255, 0);
      border-left-color: $secondary;
      border-width: 0.3125rem;
      content: " ";
      left: 0.625rem;
      position: absolute;
      pointer-events: none;
      top: 0.5rem;
    }

    &:hover {
      background: $themed-light;
    }
  }
}

.page-item {
  .page-link {
    padding: 0.125rem 0.3rem;
    color: $themed-muted;

    &:hover {
      background: $themed-light;
    }
  }

  &.disabled {
    .page-link {
      background: $themed-main-bg;
      color: $gray-500;
    }
  }

  &.active {
    .page-link {
      background-color: transparent;
      border: 0;
      color: $themed-link;
      font-weight: bold;
    }
  }
}
