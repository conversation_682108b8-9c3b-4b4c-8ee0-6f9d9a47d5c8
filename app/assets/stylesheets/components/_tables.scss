$sticky-header-z-index: 200;
$sticky-cell-outer-right-shadow: (
  1.2px 0px 1.1px -1.2px rgba(0, 0, 0, 0.14),
  3.6px 0px 3.4px -2.5px rgba(0, 0, 0, 0.11),
  9.8px 0.1px 9.2px -3.7px rgba(0, 0, 0, 0.08)
);
$sticky-cell-inner-right-shadow: 4px 0 0 var(--sticky-column-bg);

table {
  --sticky-column-bg: #{$themed-light};
}

.table-borderless {
  td {
    border-top: 0;
  }
}

td {
  word-break: break-word;
}

.header-min-width {
  min-width: 8rem;
  @media($max: $medium) {
    min-width: 5rem;
  }
}

.header-min-width--short {
  min-width: 5rem;
  
  @include media-breakpoint-up(lg) {
    width: 6rem;
  }
}

.header-min-width--extra-short {
  min-width: 2rem;

  @include media-breakpoint-up(sm) {
    width: 2rem;
  }
}

.header-min-width--medium {
  min-width: 7rem;

  @include media-breakpoint-up(lg) {
    width: 12rem;
  }
}

.header-min-width--long {
  min-width: 12rem;
}

.header-min-width--very-long {
  min-width: 16rem;
}

@include media-breakpoint-up(md) {
  .header-data--sticky,
  .table-data--sticky {
    position: sticky;
    left: 0;
    transform: translate3d(0,0,0);
    transition: $transition-base;
    transition-property: box-shadow, background-color;
    will-change: box-shadow, background-color;
    z-index: 101;
  }

  .header-data--sticky + .header-data--sticky,
  .table-data--sticky + .table-data--sticky {
    z-index: 100;
  }
}

.sticky-columns {
  .header-data--sticky,
  .table-data--sticky {
    box-shadow: $sticky-cell-inner-right-shadow;

    @include media-breakpoint-down(md) {
      left: unset !important;
    }
  }

  .header-data--sticky {
    background-color: $themed-moderate-light;

    &:has(+ th:not(.header-data--sticky)) {
      box-shadow: $sticky-cell-outer-right-shadow;
    }

    @include media-breakpoint-up(md) {
      z-index: 300 !important;

      .resize-handle {
        opacity: 0 !important;
      }
    }
  }

  .table-data--sticky {
    background-color: var(--sticky-column-bg);

    &:has(+ td:not(.table-data--sticky)) {
      box-shadow: $sticky-cell-outer-right-shadow;
    }
  }
}

tr.box--with-hover {
  z-index: 1;

  &:hover {
    z-index: 2;
  }
}

.sticky-header th {
  position: sticky;
  transition: $transition-bg-color;
  z-index: $sticky-header-z-index !important;
}

.table tbody + tbody {
  border-top-width: 1px;
}

tr.resizing th.hovering,
tr.resizing th:has(.resize-handle.active) {
  z-index: #{$sticky-header-z-index + 1} !important; // Need to ensure the drag line is above any other <th>
}

.column-resizing {
  cursor: col-resize;
  height: 100%;
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  position: absolute;
  right: 0;
  top: 0;
}

.resize-ghost-line::after {
  border-left: 0.125rem dashed var(--themed-muted);
  content: "";
  display: block;
  height: 100%;
  left: var(--after-left, 0);
  position: absolute;
  visibility: var(--visibility, hidden);
  width: 0.125rem;
}
  
.resize-handle {
  background-color: $themed-moderate-lighter;
  cursor: col-resize;
  height: 100%;
  position: absolute;
  right: 0;
  transition: $transition-base;
  top: 0;
  width: 0.0625rem;
  
  .hovering & {
    background-color: $themed-moderate-light;
    border-radius: $border-radius-sm;
    width: 0.25rem;

    &.active,
    &:hover {
      background-color: var(--themed-link);
    }
  }
}

// --------------------------------------------------------------
// TABLE VARIANTS
// --------------------------------------------------------------

/*
 * Basic Spaced Table 
 * --------------------------
 * Uses `.table--spaced` class on <table> element
*/
$spaced-table-row-background: $themed-light;
$spaced-table-spacing: 0.625rem;
$spaced-table-outer-padding: map-get($all-spacers, 4);

.table--spaced {
  border-collapse: separate;
  border-spacing: 0 $spaced-table-spacing;
  margin-top: -$spaced-table-spacing;
  
  thead {
    th {
      color: $themed-black-white;
      border: none;
      padding-left: 0.75rem;
      padding-right: 0.25rem;
    }
  }

  tbody { 
    tr {
      background-color: $spaced-table-row-background;
      border-radius: $border-radius;
      padding: 0;

      &.box--with-hover {
        transition-property: box-shadow;

        &:hover {
          box-shadow: $shadow-hover--small;
        }

        td {
          border: 0;
          padding-left: 0.625rem;
          padding-right: 0.25rem;

          &:first-of-type {
            border-bottom-left-radius: $border-radius;
            border-top-left-radius: $border-radius;
          }

          &:last-of-type {
            border-bottom-right-radius: $border-radius;
            border-top-right-radius: $border-radius;
          }
        }

        &.row-selected {
          box-shadow: inset 0 0 0 2px $themed-link;
        }
      }
    }
  }

  .sticky-columns & {
    tbody {
      tr {
        .table-data--sticky {
          &:nth-child(1) {
            border-bottom-left-radius: $border-radius;
            border-top-left-radius: $border-radius;
          }
        }
      }
    }
  }

  .sticky-header & {
    thead {
      position: sticky;
      top: 0;
      z-index: 102;
    }

    th {
      box-shadow: none;
      top: auto;
    } 
  }

  .resize-handle {
    height: calc(100% - 1rem);
    top: 0.5rem;
    transition: $transition-base;
  }

  th:hover .resize-handle,
  th:has(.resize-handle.active) .resize-handle {
    height: 100%;
    top: 0;
  }
}


/*
 * Contained Table
 * --------------------------
 * Primarily to be used with the scrollable table component.
 * Unlike `.table--sapced`, these changes primarily accommodate style changes on the wrapper element, which is why we've opted for a wrapper class instead of a table modification class.
 * This style expects a background and padding to be present on some table ancestor, which is done automatically in the scrollable table component.
 *
 * If not using the scrollable table component, you can add `.is-contained-table` to a table ancestor manually, as well as `.p-4.bg-themed-light` to a table ancestor. 
 * (`.is-contained-table` is not required to be on the same element as `.p-4.bg-themed-light`.)
 */

$container-background: $themed-light;

.is-contained-table {
  table {
    --sticky-column-bg: #{$themed-moderate-light};
  }

  .table--spaced {
    thead {
      &:after {
        background: $themed-lighter;
        border-radius: $border-radius;
        bottom: 0.25rem;
        box-shadow:
          $shadow-container-box-inner-heading,
          0 -3rem 0 1rem $container-background;
        content: "";
        position: absolute;
        right: -0.375rem;
        top: -0.375rem;
      }

      th {
        padding-bottom: 0.5rem;
        padding-top: 0.5rem;
      }
    }
    

    tbody tr {
      background-color: $themed-box-bg;
    }
  }

  .sticky-header {
    .table--spaced {
      margin-top: -0.25rem; // Removes the 0.375rem from the table spacing of 0.625rem, but includes the negative spacing margin
      padding-top: 0 !important
    }

    th {
      background-color: $themed-lighter;
    }
  }

  .sticky-columns {
    .table-data--sticky {
      background-color: var(--sticky-column-bg);
    }
  } 
}

.is-asset-list-view-table {
  table {
    --sticky-column-bg: #{$themed-moderate-light};
  }

  .table--spaced {
    tbody tr {
      background-color: $themed-box-bg;
    }
  }

  .sticky-columns {
    .table-data--sticky {
      background-color: var(--sticky-column-bg);
    }
  }
}

/* 
 * Display Box (Contained Table Variant)
 *
 * This table style assumes the first sticky column is also the first column. 
 * If that's not the case, override the left value to 0 and 
 * let the sticky element just stick to the far left side.
 */

$box-container-left-padding: 24px; // Using pixels to exactly match the expected scrollLefThreshold.

.box--display-container .is-contained-table {
  @include media-breakpoint-up(md) {  
    .header-data--sticky,
    .table-data--sticky {
      left: $box-container-left-padding; // 1.5rem is the default left padding for the contained table
    }
  }

  %sticky-column-pseudo-base-styles {
    content: "";
    opacity: 0;
    position: absolute;
    transition: $transition-base;
    transition-property: opacity;
    will-change: opacity;
  }

  .header-data--sticky:first-of-type {
    border-radius: $border-radius; 
    
    &:after {
      @extend %sticky-column-pseudo-base-styles;
      background: var(--sticky-column-bg);
      border-radius: $border-radius;
      inset: 0;
      z-index: -1;
    }
  }

  .table-data--sticky:first-of-type {
    &:before,
    &:after {
      @extend %sticky-column-pseudo-base-styles;
    }

    &:before {
      background: linear-gradient(-90deg, transparent, #{$container-background} 6px); // Second color should match box--display-container background color.
      bottom: -6px;
      right: 0;
      top: -4px;
      // Same reasoning here as in the sticky header.
      width: 20rem; 
      z-index: -2;
    }

    &:after {
      background-color: var(--sticky-column-bg);
      border-bottom-left-radius: $border-radius;
      border-top-left-radius: $border-radius;
      inset: 0;
      transition-property: opacity, box-shadow;
      will-change: opacity, box-shadow;
      z-index: -1;
    }
  }

  tr.box--with-hover:hover .table-data--sticky:first-of-type:after {
    box-shadow: $shadow-hover--sticky-column;
  }

  .sticky-columns {
    .header-data--sticky:first-of-type,
    .table-data--sticky:first-of-type {
      &:before,
      &:after {
        opacity: 1;
      }
    }
  }
}

.is-asset-list-view-table {
  %sticky-column-pseudo-base-styles {
    content: "";
    opacity: 0;
    position: absolute;
    transition: $transition-base;
    transition-property: opacity;
    will-change: opacity;
  }

  .table-data--sticky:first-of-type {
    &:before,
    &:after {
      @extend %sticky-column-pseudo-base-styles;
    }

    &:before {
      background: linear-gradient(-90deg, transparent, #{$container-background} 6px); // Second color should match box--display-container background color.
      bottom: -6px;
      right: 0;
      top: -4px;
      width: 20rem; 
      z-index: -2;
    }

    &:after {
      background-color: var(--sticky-column-bg);
      border-bottom-left-radius: $border-radius;
      border-top-left-radius: $border-radius;
      inset: 0;
      transition-property: opacity, box-shadow;
      will-change: opacity, box-shadow;
      z-index: -1;
    }
  }

  tr.box--with-hover:hover .table-data--sticky:first-of-type:after {
    box-shadow: $shadow-hover--sticky-column;
  }

  .sticky-columns {
    .table-data--sticky:first-of-type {
      &:before,
      &:after {
        opacity: 1;
      }
    }
  }
}
