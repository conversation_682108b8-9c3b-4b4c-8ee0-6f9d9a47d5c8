.tooltip {
  display: block !important;
  font-size: 0.8125rem;
  max-width: 320px;
  z-index: map-get($zIndex, 'tooltip');

  .tooltip-inner {
    background: rgba(var(--themed-tooltip-bg-rgb-values), .85);
    border-radius: $border-radius !important;
    color: $themed-white-black;
    padding: 8px 12px;
    word-break: break-word;

    a {
      color: $themed-white-black;
      font-weight: bold;
    }
  }

  .tooltip-arrow {
    border-color: rgba(var(--themed-tooltip-bg-rgb-values), 0.85);
    border-style: solid;
    height: 0;
    margin: 5px;
    position: absolute;
    width: 0;
    z-index: 1;
  }

  &[x-placement^="top"] {
    margin-bottom: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 0 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      bottom: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^="bottom"] {
    margin-top: 5px;

    .tooltip-arrow {
      border-width: 0 5px 5px 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-top-color: transparent !important;
      top: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^="right"] {
    margin-left: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 5px 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      border-bottom-color: transparent !important;
      left: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &[x-placement^="left"] {
    margin-right: 5px;

    .tooltip-arrow {
      border-width: 5px 0 5px 5px;
      border-top-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      right: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &.popover {
    $color: $themed-lighter;
    box-shadow: $shadow-hover;

    .popover-inner {
      background: $color;
      color: black;
      padding: 24px;
      border-radius: 5px;
    }

    .popover-arrow {
      border-color: $color;
    }
  }

  &[aria-hidden='true'] {
    opacity: 0;
    transition: all 0.25s ease-in-out;
    visibility: hidden;
  }

  &[aria-hidden='false'] {
    animation: 0.15s ease-in-out fadeIn; // Uses animation to prevent 'display' issues
    opacity: 1;
    visibility: visible;
  }

  &.popover-white-bg {
    $color: $themed-box-bg;

    .popover-inner {
      background: $color;
    }

    .popover-arrow {
      border-color: $color;
    }
  }

  &.popover-short {
    max-height: 400px;
    overflow-y: scroll;
  }
}

@keyframes fadeIn {
  from { opacity: 0; visibility: hidden; }
  to { opacity: 1; visibility: visible; }
}

body.module:not(.menu-closed) .tooltip {
  opacity: 0 !important;
  visibility: hidden !important;
}
