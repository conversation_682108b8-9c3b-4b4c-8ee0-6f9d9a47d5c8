.box-badge,
.box-badge--new,
.box-badge--unread,
.box-badge--ticket-draft,
.box-badge--archived,
.box-badge--overdue,
.box-badge--almost-overdue,
.box-badge--filters,
.box-badge--company {
  background-color: $color-caution;
  border-radius: $border-radius-sm;
  box-shadow: 0 2px 2px rgba(0,0,0,0.25);
  color: rgba(0,0,0,0.65);
  display: inline-block;
  padding: 0.125rem 0.325rem;
  position: absolute;
  right: -0.25rem;
  top: -0.375rem;
  z-index: 1;
}

.box-badge--locked {
  display: inline-block;
  background-color: $themed-box-bg;
  color: rgba(0,0,0,0.65);
  padding: 0.125rem 0.325rem;
  border-radius: 1.25rem;
  position: absolute;
  left: -.65rem;
  top: -0.55rem;
  z-index: 1;
}

.box-badge--new {
  background-color: $color-info-dark;
  color: white;
}

.box-badge--ticket-draft {
  background-color: #f4861f;
  color: white;
}

.box-badge--unread {
  background-color: $red-orange;
  color: white;
}

.box-badge--overdue {
  background-color: $color-danger;
  color: white;
}

.box-badge--almost-overdue {
  background-color: $color-warning;
}

.box-badge--draft {
  background-color: $color-info;
  color: white;
}

.box-badge--filters {
  background-color: $color-info;
  right: unset;
  left: -0.75rem;
  z-index: map-get($zIndex, 'above');
}

.box-badge--company {
  align-items: center;
  background-color: $themed-badge-bg;
  border: 1px solid $themed-badge-border;
  border-radius: 0 $border-radius-sm 0 $border-radius-sm;
  bottom: 0;
  display: flex;
  left: 0;
  padding: .1875rem .3125rem 0.0625rem .3125rem;
  right: unset;
  top: unset;
}

.box-checkbox {
  @extend .checkbox;
}

.box-checkholder {
  border-radius: 50%;
  cursor: pointer;
  height: 2rem;
  padding: 0.25rem;
  position: absolute;
  right: 0.5rem;
  transition: $transition-base;
  text-align: center;
  top: 0.75rem;
  width: 2rem;
  z-index: 2;

  &:hover {
    background-color: $themed-light;
  }

  // Since we extend the basic checkbox, we need a bit more specificity here to override parts of it.
  .box-checkbox {
    border-color: $gray-500;
    display: inline-block;
    height: 0.875rem;
    width: 0.875rem;

    &:before {
      top: 0;
    }
  }
  
  .box-checkbox--checked {
    background-color: $themed-link;
    border-color: $themed-link;
  }
}
