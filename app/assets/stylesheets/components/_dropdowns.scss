.dropdown {
  .nulodgicon-arrow-down-b {
    font-size: 0.875rem;
  }
}

.dropdown-menu--inline {
  border: 0;
  position: relative;
}

.dropdown-hover,
.dropdown-toggle {
  transition: $transition-base;
  transition-property: background;

  &:hover {
    background: rgba(var(--themed-dropdown-hover-bg-rgb-values), 0.05);
  }
}

.dropdown-hover .dropdown-toggle:hover {
  background: none;
}

.inline-assign-staff-dropdown {
  height: 22rem; // This is superfluous because we pass this value into the basic-dropdown component, but showing here for css reference
  max-width: 100vw;
  padding: 0;
  width: 25rem;

  .multiselect {
    overflow: hidden; // Clearfix for .multiselect__tags
  }

  .multiselect__select {
    right: 1rem;
    top: 1rem;
  }

  .multiselect__tags {
    margin: 1rem;
  }

  // The multiselect JS handles cases where 17.5rem is too large a max height, 
  // so we only set the max according to the height of the dropdown
  .multiselect__content-wrapper {
    border-radius: $border-radius;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: 1px solid $themed-fair;
    margin: 0 -1px -1px; // A bunch of negative margins to make it a bit tighter within the bordered box
    max-height: 17.5rem !important; // 22rem - 4.5rem (the height of the input.multiselect__tags and 2px border)
    position: relative;
    width: calc(100% + 2px);
  }

  &.basic-dropdown-content--full-height .multiselect__content-wrapper {
    // max-height of the basic dropdown (100vh - 1.5rem) - height of input.multiselect__tags and 2px border
    max-height: calc(100vh - 1.5rem - 4.5rem) !important;
  }
}

.dropdown-text-menu {
  padding: 0.25rem 1.5rem;
  cursor: pointer;
  display: block;

  &:hover {
    background-color: $themed-light;
    color: $themed-base;
  }
}

.basic-dropdown-content--full-height{
  overflow-y: scroll;
}