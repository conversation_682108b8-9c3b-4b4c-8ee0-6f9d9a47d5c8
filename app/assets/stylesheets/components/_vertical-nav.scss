.module-content {
  container-name: module-content;
}

.module-content--container {
  container-type: inline-size;
}

@each $break-name, $break-size in $container-breakpoints {
  @container module-content (min-width: #{$break-size}) {
  
    // Grid
    @for $i from 1 through $grid-columns {
      .col-#{$break-name}-#{$i} {
        @include make-col($i, $grid-columns);
      }
    }

    // Basic Margin/Padding
    @each $prop, $abbrev in (margin: m, padding: p) {
      @each $size, $length in $all-spacers {
        @if (str-index('#{$size}', '.')) {
          $dot-index: str-index($size, ".");
          $integer: str-slice($size, 0, $dot-index - 1);
          $decimal: str-slice($size, $dot-index + 1);

          .#{$abbrev}-#{$break-name}-#{$integer}\.#{$decimal} { #{$prop}: $length !important; }
          .#{$abbrev}t-#{$break-name}-#{$integer}\.#{$decimal},
          .#{$abbrev}y-#{$break-name}-#{$integer}\.#{$decimal} {
            #{$prop}-top: $length !important;
          }
          .#{$abbrev}r-#{$break-name}-#{$integer}\.#{$decimal},
          .#{$abbrev}x-#{$break-name}-#{$integer}\.#{$decimal} {
            #{$prop}-right: $length !important;
          }
          .#{$abbrev}b-#{$break-name}-#{$integer}\.#{$decimal},
          .#{$abbrev}y-#{$break-name}-#{$integer}\.#{$decimal} {
            #{$prop}-bottom: $length !important;
          }
          .#{$abbrev}l-#{$break-name}-#{$integer}\.#{$decimal},
          .#{$abbrev}x-#{$break-name}-#{$integer}\.#{$decimal} {
            #{$prop}-left: $length !important;
          }
        } @else {
          .#{$abbrev}-#{$break-name}-#{$size} { #{$prop}: $length !important; }
          .#{$abbrev}t-#{$break-name}-#{$size},
          .#{$abbrev}y-#{$break-name}-#{$size} {
            #{$prop}-top: $length !important;
          }
          .#{$abbrev}r-#{$break-name}-#{$size},
          .#{$abbrev}x-#{$break-name}-#{$size} {
            #{$prop}-right: $length !important;
          }
          .#{$abbrev}b-#{$break-name}-#{$size},
          .#{$abbrev}y-#{$break-name}-#{$size} {
            #{$prop}-bottom: $length !important;
          }
          .#{$abbrev}l-#{$break-name}-#{$size},
          .#{$abbrev}x-#{$break-name}-#{$size} {
            #{$prop}-left: $length !important;
          }
        }
      }
    }

    // Negative Margin
    @each $size, $length in $all-spacers {
      @if "#{$size}" != "0" {
        @if (str-index('#{$size}', '.')) {
          $dot-index: str-index($size, ".");
          $integer: str-slice($size, 0, $dot-index - 1);
          $decimal: str-slice($size, $dot-index + 1);

          .m-#{$break-name}-n#{$integer}\.#{$decimal} { margin: -$length !important; }
          .mt-#{$break-name}-n#{$integer}\.#{$decimal},
          .my-#{$break-name}-n#{$integer}\.#{$decimal} {
            margin-top: -$length !important;
          }
          .mr-#{$break-name}-n#{$integer}\.#{$decimal},
          .mx-#{$break-name}-n#{$integer}\.#{$decimal} {
            margin-right: -$length !important;
          }
          .mb-#{$break-name}-n#{$integer}\.#{$decimal},
          .my-#{$break-name}-n#{$integer}\.#{$decimal} {
            margin-bottom: -$length !important;
          }
          .ml-#{$break-name}-n#{$integer}\.#{$decimal},
          .mx-#{$break-name}-n#{$integer}\.#{$decimal} {
            margin-left: -$length !important;
          }
        } @else {
          .m-#{$break-name}-n#{$size} { margin: -$length !important; }
          .mt-#{$break-name}-n#{$size},
          .my-#{$break-name}-n#{$size} {
            margin-top: -$length !important;
          }
          .mr-#{$break-name}-n#{$size},
          .mx-#{$break-name}-n#{$size} {
            margin-right: -$length !important;
          }
          .mb-#{$break-name}-n#{$size},
          .my-#{$break-name}-n#{$size} {
            margin-bottom: -$length !important;
          }
          .ml-#{$break-name}-n#{$size},
          .mx-#{$break-name}-n#{$size} {
            margin-left: -$length !important;
          }
        }
      }
    }

    // Auto Margin
    .m-#{$break-name}-auto { margin: auto !important; }
    .mt-#{$break-name}-auto,
    .my-#{$break-name}-auto {
      margin-top: auto !important;
    }
    .mr-#{$break-name}-auto,
    .mx-#{$break-name}-auto {
      margin-right: auto !important;
    }
    .mb-#{$break-name}-auto,
    .my-#{$break-name}-auto {
      margin-bottom: auto !important;
    }
    .ml-#{$break-name}-auto,
    .mx-#{$break-name}-auto {
      margin-left: auto !important;
    }

    // Displays
    @each $value in $displays {
      .d-#{$break-name}-#{$value} { display: $value !important; }
    }

  }
}
