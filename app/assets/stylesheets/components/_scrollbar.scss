/* Base Simplebar CSS */

[data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}

.simplebar-wrapper {
  overflow: clip;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

.simplebar-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

.simplebar-content-wrapper {
  box-sizing: border-box !important;
  direction: inherit;
  display: block;
  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  max-width: 100%; /* Not required for horizontal scroll to trigger */
  max-height: 100%; /* Needed for vertical scroll to trigger */
  overflow: auto;
  position: relative;
  scrollbar-width: none;
  -ms-overflow-style: none;
  width: auto;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.simplebar-content:before,
.simplebar-content:after {
  content: ' ';
  display: table;
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

.simplebar-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0;
}

.simplebar-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-simplebar].simplebar-dragging {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

.simplebar-scrollbar {
  position: absolute;
  left: 0;
  right: 0;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: $black;
  border-radius: $simplebar-size / 2;
  left: 4px;
  right: 4px;
  top: 2px;
  bottom: 2px;
  opacity: 0;
  transition: opacity 0.2s 0.5s linear;
}

.simplebar-horizontal .simplebar-scrollbar:before {
  left: 2px;
  right: 2px;
  top: 4px;
  bottom: 4px;
}

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  transition-delay: 0s;
  transition-duration: 0s;
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: $simplebar-size;

  &.global-simplebar-track {
    bottom: $bottom-bar-height;
    top: $header-height;
  }
}

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: $simplebar-size;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  left: 2px;
  right: 2px;
}

/* Rtl support */
[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0;
}

.hs-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
  -ms-overflow-style: scrollbar !important;
}

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.simplebar-dummy-scrollbar-size > div {
  width: 200%;
  height: 200%;
  margin: 10px 0;
}

/* End Base Simplebar CSS */


/* Custom Simplebar CSS */

.global-simplebar-content {
  overflow-x: clip;
}

.simplebar-horizontal .simplebar-scrollbar {
  height: $simplebar-size;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

.simplebar-visible {
  will-change: transform;
}

.simplebar-scrollbar:before {
  background-color: $themed-muted;
  border-radius: $simplebar-size / 2;
}

.simplebar-track.simplebar-hover,
.simplebar-dragging .simplebar-track {
  background: rgba(033, 37, 41, 0.05);
}

.simplebar-hover .simplebar-scrollbar:before,
.simplebar-dragging .simplebar-scrollbar:before {
  background-color: $themed-dark;
}

[data-simplebar-themed] .simplebar-hover .simplebar-scrollbar:before,
[data-simplebar-themed] .simplebar-dragging .simplebar-scrollbar:before {
  background-color: $themed-fair;
}

.light-theme [data-simplebar-themed] .simplebar-hover .simplebar-scrollbar:before,
.light-theme [data-simplebar-themed] .simplebar-dragging .simplebar-scrollbar:before {
  background-color: $themed-muted;
}

.scrollable--hidden-bar {
  padding-top: 0.125rem;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .scroll-btn-gradient {
    height:2.8rem;
    width:7rem;
    top:0;

    .scroll-btn {
      cursor: pointer;
      top:0;
      font-size: 1.1575rem;

      &:focus {
        outline: 0;
      }
    }
    .scroll-btn-right {
      right:0;
    }
    .scroll-btn-left {
      left:0;
    }
  }
  .scroll-btn-right-gradient {
    right:0;
    background: linear-gradient(to left, $themed-scroll-gradient 30%,rgba(255,255,255,0) 100%);
  }
  .scroll-btn-left-gradient {
    z-index:3;
    left:0;
    background: linear-gradient(to right, $themed-scroll-gradient 30%,rgba(255,255,255,0) 100%);
  }
  /deep/ .show-gradient {
    background: linear-gradient(to left, $themed-scroll-gradient 30%,rgba(255,255,255,0) 100%);
  }
}
