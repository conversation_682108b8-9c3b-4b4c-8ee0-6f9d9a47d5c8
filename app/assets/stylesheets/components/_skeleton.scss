.skeleton {
  overflow: hidden;

  .pre-vue & {
    animation: none;
    opacity: 0.5;
  }
}

.skeleton--image {
  background: $themed-fair;
  display: inline-block;
}

@mixin skeleton-base {
  overflow: hidden;
  isolation: isolate;
  position: relative;
  
  &::after {
    animation: shimmer 2s infinite alternate ease-in-out;
    content: "";
    bottom: 0;
    background: linear-gradient(90deg,rgba(255, 255, 255, 0),rgba(255, 255, 255, 0.5),rgba(255, 255, 255, 0));
    pointer-events: none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    will-change: transform;
    z-index: 1;
  }
}

.skeleton__item {
  @include skeleton-base;
  background: $themed-light;
  border-radius: 0.25rem;
  display: inline-block;
  height: 0.8em;
  width: 100%;

  &:last-child {
    width: 75%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (prefers-reduced-motion) {
  .skeleton__item::after {
    animation: none;
    opacity: 0;
  }

  .skeleton__avatar::after {
    animation: none;
    opacity: 0;
  }
}

.skeleton__item--thin {
  height: 0.375em;
}

.skeleton__item--square {
  height: 1em;
  width: 1em;
}

.skeleton__avatar {
  @include skeleton-base;
  background: $themed-light;
  border-radius: 50%;
  display: flex;
  height: 30px;
  width: 30px;
}

.skeleton__avatar--small {
  height: 25px;
  width: 25px;
}

@keyframes fadeIn {
  from { opacity: 0.5; }
}
