.sub-menu {
  border-bottom: 1px solid $themed-moderate-lighter;
  font-size: 1rem;
  width: 100%;
  list-style: none;
}

.sub-menu-item,
.sub-sub-menu-item {
  align-items: center;
  color: $themed-base;
  display: inline-flex;
  float: left;
  font-size: 0.875rem;
  margin-right: 1rem;
  padding: 0 0.25rem 0.5rem;
  position: relative;
  transition: $transition-base;
}

.sub-menu-item {
  font-weight: 500;

  &:after {
    background-color: $themed-secondary;
    border-radius: 0.125rem;
    bottom: -0.1875rem;
    content: "";
    left: 0;
    height: 0.1875rem;
    position: absolute;
    transition: $nav-transition-base;
    transform: scaleX(0);
    transform-origin: left;
    width: 100%;
  }

  &:hover:not(s) {
    color: $themed-dark;

    &:after {
      transform: scaleX(1);
    }
  }

  &.sub-menu-item--active,
  &.router-link-exact-active {
    color: $themed-base;

    &:after {
      background-color: $primary;
      transform: translateY(0);
      opacity: 1;

      @each $module, $color in $module-colors {
        .module--#{$module} & {
          background-color: $color;
        }
      }
    }
  }

  &.router-btn-active {
    &:after {
      background-color: $themed-secondary;
      transform: translateY(0);
      opacity: 1;
    }
  }
}

.sub-menu-item__icon {
  color: $themed-base;
  display: inline-block;
  font-size: 1.125rem;
  line-height: 1.125rem;
  margin-right: 0.25rem;
  opacity: 0.8;
  vertical-align: top; // Probably superfluos given the flex parent, but kept in case this is used elsewhere

  .module-vertical-nav & {
    opacity: 1;
  }
}

.sub-menu-item__settings-icon {
  display: inline-block;
  transform: translateY(-1px);
}

.sub-menu-item__settings-text {
  font-size: 0.75rem;
  vertical-align: text-top;
}

.sub-sub-menu-item {
  background-color: transparent;
  border-radius: $border-radius-sm;
  font-size: 0.8125rem;

  @include media-breakpoint-up(sm) {
    padding: 0.125rem 0.875rem;
  }

  @include media-breakpoint-up(md) {
    padding: 0.125rem 1.5rem;
  }

  &:hover {
    background-color: $themed-light;
  }

  &.router-link-active,
  &.router-link-active:hover {
    background-color: $themed-branded-light;
    color: $primary;
  }
}

.sub-menu-badge {
  background-color: $color-accent;
  border-radius: 0.625rem;
  color: white;
  display: inline-block;
  font-size: 0.625rem;
  font-weight: 500;
  height: 1rem;
  line-height: 1;
  margin: 0 -0.1875rem;
  min-width: 1.25rem;
  padding: 0.1875rem 0.25rem;
  position: relative;
  text-align: center;
  top: -0.5rem;

  @media($max: $medium) {
    font-size: 0.425rem;
    min-width: 0.75rem;
    height: 0.7rem;
  }
}

.sub-menu-separator {
  background-color: $de-france-blue;
  border-radius: 0.5rem;
  display: block;
  float: left;
  height: 0.325rem;
  margin-left: -0.25rem;
  margin-right: 1rem;
  position: relative;
  top: -1rem;
  width: 0.325rem;

  @each $module, $color in $module-colors {
    .module--#{$module} & {
      background-color: $color;
    }
  }
}

.subpage-menu {
  border-radius: 1rem;
  border: 1px solid $themed-moderate-lighter;
  display: inline-block;
  margin-left: -0.1875rem;
}

.subpage-menu__item {
  border-radius: 20rem;
  color: $themed-dark;
  display: inline-block;
  font-size: 0.875rem;
  margin: 0.1875rem;
  margin-left: 0;
  padding: 0 1rem;
  transition: $transition-base;

  &:first-child {
    margin-left: 0.1875rem;
  }

  &:hover:not(.router-link-active) {
    background-color: $themed-light;
  }

  &.router-link-exact-active {
    background-color: $iceberg-blue;
    color: $black;

    @each $module, $color in $module-colors-light {
      .module--#{$module} & {
        background: var(--themed-#{$module}-light);
        color: white;
      }
    }

    @each $module, $color in $module-colors-dark {
      .module--#{$module} & {
        color: var(--themed-#{$module}-dark);
      }
    }
  }

  &.subpage-menu__item--with-split-button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-right: 0;

    &.disabled {
      background-color: var(--themed-very-light);
      border-color: var(--themed-very-light);
    }
  }
}

.subpage-menu__item--split-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  color: $themed-link;
  margin-left: -0.75rem;
  padding: 0 .5rem 0 0 !important;

  &:hover {
    color: var(--themed-link-hover) !important;
  }

  &.disabled {
    background-color: var(--themed-very-light);
    border-color: var(--themed-very-light);
    color: $themed-light;
  }
}

.subpage-menu__item--disabled {
  background: $themed-light;
  color: $themed-muted;
}

.side-menu-item {
  border-radius: $border-radius;
  color: $themed-base;
  display: block;
  font-size: 0.9375rem;
  margin-bottom: 0.5rem;
  padding: 0.325rem 0.75rem;
  transition: $transition-base;

  &:hover {
    background-color: $themed-light;
    color: $themed-base;
  }

  &.is-active {
    background-color: $themed-light;
    color: $themed-link;
  }
}

.side-menu-item--small {
  border-radius: $border-radius;
  color: $themed-secondary;
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
  padding: 0 0.75rem;
  transition: $transition-base;

  &:hover {
    background-color: $themed-light;
    color: $themed-base;
  }

  &.is-active {
    background-color: $themed-light;
    color: $themed-link;
  }
}

.side-menu-icon {
  display: inline-block;
  font-size: 1.15rem;
  line-height: 2.25rem;
  margin-right: 0.25rem;
  vertical-align: middle;
}

.side-menu-icon--large {
  display: inline-block;
  font-size: 1.5rem;
  vertical-align: middle;
}

.sub-menu-item,
.sub-sub-menu-item {
  $padding-small: 0.25rem;
  @media($max: 1035px) {
    font-size: 0.8125rem;
    margin-right: 1.5rem;
  }
  @media($max: 925px) {
    font-size: 0.6875rem;
    margin-right: 0.7rem;
  }
  @media($max: $medium) {
    font-size: 0.75rem;
    padding: $padding-small;
    margin-bottom: 0;
    margin-right: 0.45rem;
  }
  @media($max: $small) {
    font-size: 0.6875rem;
    padding: $padding-small;
    margin-bottom: 0.7rem;
    margin-right: 0.65rem;
  }
}

.sub-menu-item--icon {
  @media($max: 925px) {
    font-size: 1rem !important;
    margin-left: 0.8rem !important;
  }
  @media($max: 800px) {
    margin-left: 0.4rem !important;
  }
  @media($max: $medium) {
    font-size: 1.0625rem !important;
    margin-left: 0.6rem !important;
    margin-right: 0;
  }
}

.sub-sub-menu {
  background: rgba(13, 202, 240, 0.05);
  border-radius: $border-radius;
  display: inline-grid;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
  padding: 0.5rem;
  position: relative;
  width: calc(100% - 0.45rem);
}

.side-menu-item__sub-label {
  font-size: 0.6875rem;
}
