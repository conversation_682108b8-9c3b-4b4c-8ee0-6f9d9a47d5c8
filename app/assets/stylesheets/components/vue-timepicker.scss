.timepicker-holder {

  .time-picker {
    width: 5em !important;
  }

  .timepicker-input {
    display: block;
    line-height: 30px;
    color: $themed-base;
    padding: 0 !important;
    height: 100% !important;
    text-align: center;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 4rem !important;

    &:focus {
      outline: 0;
      box-shadow: 0;
    }
  }

  .timepicker-disabled-input {
    &:disabled {
      color: black !important;
    }
  }

  &.ending-time {
    max-width: 225px;
  }
}

.manual-timepicker-holder {
  .genuicon-android-time {
    z-index: 9;
  }
  .manual-timepicker-input {
    display: block;
    height: calc(2.25rem + 2px) !important;
    padding: 0.375rem 4px 0.375rem 40px !important;
    font-size: 1rem !important;
    line-height: 1.5;
    color: $themed-base;
    background-color: $themed-box-bg;
    background-clip: padding-box;
    border: 1px solid #ced4da !important;
    border-radius: 0.5rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    &:focus {
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
  }
  &.ending-time {
    max-width: 225px;
  }
}
