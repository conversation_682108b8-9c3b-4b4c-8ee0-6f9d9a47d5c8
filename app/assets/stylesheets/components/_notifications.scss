.vue-notification-group {
  bottom: 0.5rem !important;

  .homepage & {
    bottom: 0.5rem !important;
  }
}

.hide-chat-button {
  .minimized-box,
  .intercom-lightweight-app,
  .intercom-lightweight-app--placeholder {
    display: none !important;
  }
}

.show-chat-button {
  .intercom-lightweight-app,
  .intercom-lightweight-app--placeholder {
    display: none !important;
  }
}

body:has(.theme-right.is-visible),
body:has(.dismissible-menu--right.is-visible) {
  .intercom-lightweight-app,
  .intercom-lightweight-app--placeholder {
    display: none !important;
  }
}

.vue-notification {
  padding: 10px;
  margin: 5px !important; // overrides vendor defaults
  font-size: 0.875rem !important; // overrides vendor defaults
  letter-spacing: 1px;
  color: #ffffff;

  &.warn {
    background: $color-warning;
    border-left-color: darken($color-warning, 10%);
  }

  &.error {
    background: $color-error;
    border-left-color: darken($color-error, 10%);
  }

  &.success {
    background: $color-success;
    border-left-color: darken($color-success, 10%);
  }
}
.custom-close-btn {
  background: transparent;
  border: none;
  color: white;
  font-size: 1rem;
  font-weight: 400;
  margin-left: 0.625rem;
  cursor: pointer;
  position: absolute;
  right: 0.725rem;
  top: 0.4125rem;
}

.custom-close-btn:focus {
  outline: none;
  box-shadow: none;
}

// Bounce Flip Animation
.ns-box {
  background: rgba($maastricht-blue, 0.85);
  color: rgba($titan-white, 0.95);
  font-family: $font-family-sans-serif;
  font-size: 90%;
  line-height: $line-height-base;
  padding: 1rem;
  position: fixed;
  pointer-events: none;
  z-index: map-get($zIndex, 'notification');
}

.ns-box.ns-show {
  pointer-events: auto;
}

.ns-box-inner {
  display: flex;
}

.ns-box a {
  color: inherit;
  opacity: 0.7;
  font-weight: 700;
}

.ns-box a:hover,
.ns-box a:focus {
  opacity: 1;
}

.ns-box p {
  margin: 0;
}

.ns-box.ns-show,
.ns-box.ns-visible {
  pointer-events: auto;
}

.ns-close {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 4px;
  top: 4px;
  overflow: hidden;
  text-indent: 100%;
  cursor: pointer;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.ns-close:hover,
.ns-close:focus {
  outline: none;
}

.ns-close::before,
.ns-close::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 60%;
  top: 50%;
  left: 50%;
  background: $themed-muted;
}

.ns-close:hover::before,
.ns-close:hover::after {
  background: #fff;
}

.ns-close::before {
  -webkit-transform: translate(-50%,-50%) rotate(45deg);
  transform: translate(-50%,-50%) rotate(45deg);
}

.ns-close::after {
  -webkit-transform: translate(-50%,-50%) rotate(-45deg);
  transform: translate(-50%,-50%) rotate(-45deg);
}

.ns-attached {
  right: 0;
  max-width: 300px;
}

[class^="ns-effect-"].ns-attached.ns-hide,
[class*=" ns-effect-"].ns-attached.ns-hide {
  -webkit-animation-direction: reverse;
  animation-direction: reverse;
}

.ns-effect-bouncyflip {
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
  background-color: $themed-lighter;
  color: $themed-secondary;
  top: 0;
  box-shadow: 0 13px 10px -5px rgba(0,0,0,0.2);
}

.ns-effect-bouncyflip img {
  padding: 0rem;
  height: 6rem;
}

.ns-effect-bouncyflip p {
  padding: 0.5rem 0.8rem 0.8rem 0rem;
}

.ns-effect-bouncyflip .ns-close::after,
.ns-effect-bouncyflip .ns-close::before {
  background: $themed-secondary;
}

.ns-effect-bouncyflip .ns-close:hover::after,
.ns-effect-bouncyflip .ns-close:hover::before {
  background: $themed-muted;
}

.ns-effect-bouncyflip.ns-show,
.ns-effect-bouncyflip.ns-hide {
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
}

@-webkit-keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,-90deg);
    -webkit-transition-timing-function: ease-in;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,20deg);
    -webkit-transition-timing-function: ease-out;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,-10deg);
    -webkit-transition-timing-function: ease-in;
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,5deg);
    -webkit-transition-timing-function: ease-out;
  }

  100% {
    -webkit-transform: perspective(400px);
  }
}

@keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,-90deg);
    transform: perspective(400px) rotate3d(1,0,0,-90deg);
    -webkit-transition-timing-function: ease-in;
    transition-timing-function: ease-in;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,20deg);
    transform: perspective(400px) rotate3d(1,0,0,20deg);
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,-10deg);
    transform: perspective(400px) rotate3d(1,0,0,-10deg);
    -webkit-transition-timing-function: ease-in;
    transition-timing-function: ease-in;
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1,0,0,5deg);
    transform: perspective(400px) rotate3d(1,0,0,5deg);
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
  }

  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

.ns-effect-bouncyflip.ns-hide {
  -webkit-animation-name: flipInXSimple;
  animation-name: flipInXSimple;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}

@-webkit-keyframes flipInXSimple {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -90deg);
    -webkit-transition-timing-function: ease-in;
  }
  100% {
    -webkit-transform: perspective(400px);
  }
}

@keyframes flipInXSimple {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -90deg);
    -webkit-transition-timing-function: ease-in;
    transition-timing-function: ease-in;
  }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

@media screen and (max-width: 25em) {
  .ns-attached {
    left: 30px;
    max-width: none;
    right: 30px;
  }
}



// Bottom box slide-in

.ns-box.ns-bar {
  bottom: 0;
  left: 0;
  width: 100%;
}

.ns-bar .ns-close {
  background: transparent;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.ns-bar .ns-close::before,
.ns-bar .ns-close::after {
  background: $gray-500;
}

[class^="ns-effect-"].ns-bar.ns-hide,
[class*=" ns-effect-"].ns-bar.ns-hide {
  -webkit-animation-direction: reverse;
  animation-direction: reverse;
}

/* Slide on top */
.ns-effect-slidetop {
  box-shadow: inset 4.5em 0 rgba(0,0,0,0.6);
}

.ns-effect-slidetop .icon {
  position: absolute;
  display: block;
  font-size: 109%;
  top: 50%;
  left: 1em;
  -webkit-transform: translate3d(0,-50%,0);
  transform: translate3d(0,-50%,0);
}

.ns-effect-slidetop p {
  padding: 0 4.2em;
  font-size: 1.2em;
  display: inline-block;
}

.ns-effect-slidetop .ns-close::before,
.ns-effect-slidetop .ns-close::after {
  width: 2px;
  background: $themed-base;
}

.ns-effect-slidetop .ns-close:hover::before,
.ns-effect-slidetop .ns-close:hover::after {
  background: #fff;
}

.ns-effect-slidetop.ns-show .icon,
.ns-effect-slidetop.ns-show p {
  -webkit-animation-name: animScaleUp;
  animation-name: animScaleUp;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.ns-effect-slidetop.ns-show p {
  -webkit-animation-name: animFade;
  animation-name: animFade;
}

@-webkit-keyframes animScaleUp {
  0% { opacity: 0; -webkit-transform: translate3d(0,-50%,0) scale3d(0,0,1); }
  100% { opacity: 1; -webkit-transform: translate3d(0,-50%,0) scale3d(1,1,1); }
}

@keyframes animScaleUp {
  0% { opacity: 0; -webkit-transform: translate3d(0,-50%,0) scale3d(0,0,1); transform: translate3d(0,-50%,0) scale3d(0,0,1); }
  100% { opacity: 1; -webkit-transform: translate3d(0,-50%,0) scale3d(1,1,1); transform: translate3d(0,-50%,0) scale3d(1,1,1); }
}

.ns-effect-slidetop.ns-show,
.ns-effect-slidetop.ns-hide {
  -webkit-animation-name: animSlideTop;
  animation-name: animSlideTop;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}

@-webkit-keyframes animSlideTop {
  0% { -webkit-transform: translate3d(0,100%,0); }
  100% { -webkit-transform: translate3d(0,0,0); }
}

@keyframes animSlideTop {
  0% { -webkit-transform: translate3d(0,100%,0); transform: translate3d(0,100%,0); }
  100% { -webkit-transform: translate3d(0,0,0); transform: translate3d(0,0,0); }
}
