trix-editor {
  ul {
    list-style: disc;
  }
  background: $themed-box-bg !important;
  border-color: $themed-fair !important;
  border-radius: $border-radius !important;
  padding-top: 35px !important;
  min-height: 7em !important;

  .border-bottom & {
    border-bottom: 0px !important;
  }

  pre {
    color: #d01f3e;
  }

  .trix-editor--small & {
    max-height: 10rem;
    min-height: 6.5em !important;
    overflow: auto;
    padding-top: 28px !important;

    &.trix-content {
      line-height: 1.25;
    }
  }
}

trix-toolbar {
  position: absolute !important;
  left: 1px !important;
  width: calc(100% - 0.125rem);

  .trix-button-row {
    border-bottom: 1px solid $themed-light !important;
    flex-wrap: wrap !important;
    padding-right: .25em !important;
  }

  .trix-button-group {
    border: none !important;
    margin-bottom: 0 !important;

    &:not(:first-child) {
      margin-left: 0 !important;
    }

    &:not(:last-child) {
      margin-right: 1vw !important;
    }
  }

  .trix-button {
    border-bottom: none !important;
    font-size: .875rem !important;
    padding: 0rem 1em .125em !important;
  }

  .trix-button--icon {
    height: 1.875em !important;
    width: 1.5em !important;

    &:not(.trix-active):before {
      filter: var(--themed-light-icon-filter);
    }
  }

  .trix-editor--small & {
    background: var(--themed-box-bg) !important;
  }
}

.trix-wrapper {
  a,
  a:link {
    color: $themed-link;
  }
}

.trix-toolbar--very-small{
  .trix-button {
    font-size: 0.65rem !important;
  }

  .trix-button-row {
    flex-wrap: nowrap !important;

    &::-webkit-scrollbar {
      transition: opacity 0s linear;
      height: 0.35rem;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background: $themed-very-muted;
      border-radius: 0.25rem;
    }
    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
}

.trix-toolbar--small {
  .trix-button {
    font-size: 0.75rem !important;
  }
}

.trix-content--large {
  .trix-content {
    min-height: 15rem !important;
  }
}

.suggestion-item {
  trix-editor {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }
}
