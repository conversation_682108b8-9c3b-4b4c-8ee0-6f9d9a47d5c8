.form-control.vue-tel-input {
  border: 1px solid $themed-fair !important;
  border-radius: $border-radius !important;

  .vti__dropdown {
    padding-left: 0px !important;

    .vti__selection {
      .vti__flag {
        margin-left: 0px !important;
      }
    }

    ul li strong {
      font-weight: 500 !important;
    }
  }

  .vti__input {
    background-color: $themed-box-bg;
    font-size: 14px !important;
    color: $themed-base !important;
  }

  &.disabled {
    border: 1px solid $themed-light !important;

    .vti__input {
      background: $themed-box-bg !important;
    }
  }
}
