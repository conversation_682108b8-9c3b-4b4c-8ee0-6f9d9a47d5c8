html {
  -moz-osx-font-smoothing: grayscale;  
  -webkit-font-smoothing: antialiased;
  font-size: $font-size-base-percentage;
}

body.module {
  overflow: hidden;

  & main {
    flex: 1;
  }

  .page-scroll-wrap {
    display: flex;
    height: 100vh;
    flex-direction: column;
    max-height: 100vh;
    max-width: 100vw;
    min-height: 100vh;
    padding-left: $drawer-collapse-width;
    padding-top: $header-height;
    position: relative;
    width: 100vw;
    overflow-x: hidden;

    main {
      background: $themed-main-bg;
      flex: 1;
      min-width: calc(100vw - #{$drawer-collapse-width});
    }
  }

  &.no-drawer {
    .page-scroll-wrap {
      padding-left: 0;

      main {
        min-width: 100vw;
      }
    }
  }

  .global-simplebar-content.simplebar-content {
    display: flex;
    flex-direction: column;
    max-width: 100vw;
    min-height: 100vh;
    min-width: 100vw;
    position: relative;
    width: 100vw;
  }
}

.module.no-footer-content .main-page-content {
  padding-bottom: $content-bottom-margin;

  &:has(.box--display-height) {
    padding-bottom: $contained-content-bottom-margin;
  }
}

.container,
.container-fluid {
  position: relative;
  z-index: map-get($zIndex, 'main-content');
}

.module-vertical-nav {
  .container {
    max-width: map-get($container-max-widths, 'xl') + $vertical-nav-width;
  }
}

.page-scroll-wrapper {
  overflow-x: hidden;
  height: calc(100vh - #{$header-height});
  left: $drawer-collapse-width;
  position: fixed;
  transition: z-index 0s 0.5s linear;
  width: calc(100vw - #{$drawer-collapse-width});
  top: $header-height;

  body[style*="overflow: hidden"] &,
  body[style*="overflow:hidden"] & {
    z-index: map-get($zIndex, 'modal');
    transition: z-index 0s 0s linear;
  }

  .no-drawer & {
    left: 0;
    width: 100%;
  }
}

body[style*="overflow: hidden"],
body[style*="overflow:hidden"] {
  main {
    z-index: map-get($zIndex, 'modal')
  }
}

body[style*="overflow: hidden"],
body[style*="overflow:hidden"] {
  .arrows-holder {
    opacity: 0;
    visibility: hidden;
  }

  .left-arrow, .right-arrow {
    z-index: map-get($zIndex, 'modal')
  }
}

.page-content-wrapper {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - #{$header-height});
  padding: 0 32px;

  > main {
    flex: 1;
  }

  .no-drawer & {
    padding: 0;
  }
}

// .page-wrapper is used where fancy scrolls aren't needed, simplifying the markup
.page-wrapper {
  @extend .page-scroll-wrapper;
  @extend .page-content-wrapper;
}

// The additional body scoping is for clarification, as this class is only intended for the body tag
body.split-layout:before {
  background-color: $themed-lighter;
  content: "";
  left: 0;
  position: absolute;
  top: 0;
  width: 25%;
}

.sample-company-warning {
  background-color: $red;
  border-radius: $border-radius-sm $border-radius-sm $border-radius $border-radius;
  box-shadow: $shadow-base;
  color: white;
  font-size: 0.8125rem;
  left: 50%;
  letter-spacing: 0.5px;
  padding: 0.325rem 2rem 0.325rem 1rem;
  position: fixed;
  text-align: center;
  top: $header-height;
  transform: translateX(-50%);
  z-index: map-get($zIndex, 'header-sample-toggle');

  &:after {
    background-color: inherit;
    content: "";
    height: 0.125rem;
    left: 50%;
    position: absolute;
    top: 0;
    transform: translateX(-50vw);
    width: 100vw;
  }
}

.sample-company-warning__close {
  background-color: shift-color($red, 30%);
  border-radius: 50%;
  bottom: 0.325rem;
  color: white;
  cursor: pointer;
  height: 1rem;
  line-height: 1rem;
  position: absolute;
  right: 0.45rem;
  width: 1rem;
}

.vh-100 {
  height: 100vh;
}

.min-vh-100 {
  min-height: 100vh;
}

.right-auto {
  right: auto;
}

.left-auto {
  left: auto;
}

.h-fit-content {
  height: fit-content;
}

.w-fit-content {
  width: fit-content;
}

.b-0 {
  bottom: 0 !important;
}

.t-0 {
  top: 0 !important;
}

@for $i from 0 through 100 {
  @if $i % 10 == 0 {
    .right-#{$i} {
      right: #{$i}px;
    }
  }
}

@for $i from 0 through 100 {
  @if $i % 10 == 0 {
    .left-#{$i} {
      left: #{$i}px;
    }
  }
}
