.badgification-text {
  max-width: 7rem;
  height: 3rem;
}

.locked-badge {
  filter: grayscale(1);
  opacity: 0.35;
}

.badgification {
  background-color: #fff;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  border: 4px solid rgba(0,0,0,0.25);
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(0,0,0,0.25);
  height: 4.5rem;
  width: 4.5rem;
}

.badgification--teammate {
  background-color: $color-staff;
}

@for $i from 1 through 5 {
  .badgification--teammate#{$i} {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/staffMember#{$i}.svg");
  }
}

.badgification--contract {
  background-color: $color-contracts;
}

@for $i from 1 through 5 {
  .badgification--contract#{$i} {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/contract#{$i}.svg");
  }
}

.badgification--telecomService {
  background-color: $color-telecom;
}

@for $i from 1 through 5 {
  .badgification--telecomService#{$i} {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/telecomService#{$i}.svg");
  }
}

.badgification--asset {
  background-color: $color-assets;
}

@for $i from 1 through 7 {
  .badgification--asset#{$i} {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/asset#{$i}.svg");
  }
}

.badgification--helpTicket {
  background-color: $color-helpdesk;
}

@for $i from 1 through 5 {
  .badgification--helpTicket#{$i} {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/helpTicket#{$i}.svg");
  }
}

.badgification--vendor {
  background-color: $color-vendor;
}

@for $i from 1 through 5 {
  .badgification--vendor#{$i} {
    background-image:  url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/badges/vendor#{$i}.svg");
  }
}

.status-badge {
  color: white;
  display: inline-block;
  font-size: 11px;
  font-weight: 700;
  height: 16px;
  line-height: 8px;
  padding: 4px 6px;
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 0 5px 0 5px;

  &.success {
    background: $pastel-green-100;
  }

  &.warning, &.danger {
    background: $color-warning;
  }

  &.disabled {
    background: $color-fair;
  }
}

.active-filter-badge {
  align-items: center;
  background: $themed-box-bg;
  border-radius: $border-radius-lg;
  border: 1px solid $themed-badge-border;
  display: flex;
  font-weight: normal;
  padding: 0.125rem 0.5rem;
  transition: $transition-base;
}

.active-filter-badge--clickable:hover {
  background: $cyan-subtle;
}

.icon-box {
  background: $biscay-blue;
  border-radius: 0.625rem;
  color: $white;
  font-size: 1rem;
  padding: 0.375rem .5rem;
}
