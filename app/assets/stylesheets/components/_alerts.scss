.alert {
  border: 0;
  color: white;
}

$alerts: primary secondary success danger warning info light dark;
@each $alert in $alerts {
  .alert-#{$alert} {
    background-color: map-get($theme-colors, $alert);
  }

  .alert-basic-#{$alert} {
    background-color: theme-color-level($alert, $alert-bg-level);
    border: 1px solid theme-color-level($alert, $alert-border-level);
    color: theme-color-level($alert, $alert-color-level);

    .alert-badge {
      background-color: theme-color-level($alert, $alert-bg-level);
    }
  }
}

.alert-warning {
  color: $gray-900;
}

.alert-error {
  @extend .alert-danger;
}

.alert-alert {
  @extend .alert-warning;
}

.alert-notice {
  @extend .alert-info;
}
