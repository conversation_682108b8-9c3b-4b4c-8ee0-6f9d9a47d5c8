a,
.btn {
  cursor: pointer;

  &:hover {
    text-decoration: none;
  }
}

.btn-group {
  border-radius: $border-radius;

  &:not(.btn-group-toggle):not(.type-selector) {
    box-shadow: map-get($shadows, 'button');
  }
}

.btn-sm {
  border-radius: $border-radius;
}

.btn-group,
.btn-group-sm {
  .btn {
    border-radius: $border-radius;
  }
}

.btn-primary,
.btn-secondary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-light,
.btn-lighter,
.btn-dark,
.btn-default {
  box-shadow: $shadow-button;

  .btn-group & {
    box-shadow: none;
  }
}

.btn[disabled] {
  background-color: $themed-fair;
  border-color: $themed-fair;
  color: $themed-muted;
  cursor: not-allowed !important;
  opacity: 0.55;
}

.btn.btn-link[disabled] {
  background-color: transparent;
  border-color: transparent;

  &:hover {
    background-color: transparent !important;
  }
}

.btn-round {
  border: none;
  border-radius: 50%;
  height: 2.25rem;
  line-height: 2.25rem;
  padding: 0;
  text-align: center;
  width: 2.25rem;

  // TODO: Drop this whole hover state and use a `.btn-THEME-COLOR` anywhere .btn-round` is used
  &:hover:not(
    .btn-primary, .btn-secondary, .btn-success, .btn-info, .btn-warning, .btn-danger,
    .btn-accent, .btn-alternate, .btn-body, .btn-caution, .btn-error, .btn-safe,
    .btn-dark, .btn-fair, .btn-very-fair, .btn-light, .btn-lighter, .btn-very-light, 
    .btn-blue, .btn-green, .btn-red, .btn-yellow, .btn-orange, .btn-cyan, .btn-teal, 
    .btn-maastricht-blue, .btn-biscay-blue, .btn-blue-oblivion, .btn-de-france-blue, .btn-azure-blue, .btn-iceberg-blue, .btn-titan-white
  ) {
    background-color: $themed-very-fair;
  }

  &.btn-sm {
    font-size: 0.875rem;
    height: 1.5rem;
    line-height: 1.5rem;
    padding: 0; // Necessary to reoverride the defaults
    width: 1.5rem;
  }

  &.btn-xs {
    font-size: 0.75rem;
    height: 1rem;
    line-height: 1rem;
    padding: 0; // Necessary to reoverride the defaults
    width: 1rem;
  }
}

.btn-xs {
  font-size: 0.6875rem;
  letter-spacing: 0.25px;
  line-height: 1.65;
  padding: 0.125rem 0.5rem;
}

.btn-outline-light {
  border-color: $themed-very-fair;
  color: $themed-secondary;

  &:focus,
  &:hover {
    background-color: $themed-very-fair;
    color: $color-secondary;
  }
}

.btn-outline-not-as-light {
  border-color: $themed-fair;
  color: $themed-secondary;

  &.active,
  &:hover {
    background-color: $themed-light;
  }
}

// Adds a 'default' style
.btn-default {
  background-color: $themed-very-fair;
  border-color: $themed-very-fair;
  color: $themed-dark;

  &:hover {
    background-color: $themed-fair;
    border-color: $themed-fair;
  }
}

.btn-light:focus,
.btn-link.focus {
  box-shadow: 0 0 0 0.2rem rgba($color-primary, 0.25); // Mimics the "primary" focus state, since the bootstrap light focus is too light to see
}

.btn-lighter {
  background-color: $themed-lighter;
  border-color: $themed-light;
  color: $themed-secondary;

  &:hover {
    background-color: $themed-light;
    border-color: $themed-very-fair;
    color: $themed-secondary;
  }
}

.btn-white {
  background-color: white;
  border-color: white;
  color: $gray-800;

  &:hover {
    background-color: $themed-light;
    border-color: $themed-very-fair;
  }
}

.btn-faded-light,
.btn-less-faded-light {
  background-color: rgba(255,255,255,0.3);
  border: 0;
  color: white;

  &:hover {
    background-color: rgba(255,255,255,0.5);
  }
}

.btn-primary--dark {
  background-color: darken($primary, 5%);
}

.btn-group.dropdown {
  &:hover {
    .btn-primary,
    .btn-primary--dark {
      background-color: darken($primary, 7.5%);
    }
  }
}

.btn-shadow {
  box-shadow: $shadow-button;
}

.btn-flat {
  box-shadow: none;
}

.btn-featured {
  box-shadow: 0 0 4px rgba($primary, .5);
}

.button-loader {
  left: 50%;
  position: absolute;
  top: 55%;
  transform: translate(-50%,-50%);
}

.btn-lg-search {
  font-size: 1.15rem;
  padding: 0.5rem 1rem;
}

.button-copy {
  cursor: pointer;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  margin-right: .5rem;
  line-height: 1rem;
  transition: $transition-base;

  &:hover {
    background-color: $themed-light;
  }
}

.btn-link {
  transition: $transition-base;

  &:hover {
    background-color: $themed-light !important;
    text-decoration: none;
  }

  &:focus {
    text-decoration: none;
  }
}

.back-to-link {
  float: right;
}

.edit-delete-btn.edit:before { content: "Edit"; }
.edit-delete-btn.qr:before { content: "QR Code"; }
.edit-delete-btn.delete:before { content: "Delete"; }
.edit-delete-btn.archive:before { content: "Archive"; }
.edit-delete-btn.unarchive:before { content: "Unarchive"; }
.edit-delete-btn.mute:before { content: "Mute"; }
.edit-delete-btn.unmute:before { content: "Unmute"; }
.edit-delete-btn.unlink:before { content: "Unlink";}
.edit-delete-btn.print:before { content: "Print";}
.edit-delete-btn.move:before { content: "Move";}
.edit-delete-btn.close:before { content: "Close";}


.edit-delete-btn {
  background-color: $themed-light;
  border-radius: 50%;
  box-shadow: map-get($shadows, 'button');
  float: right;
  padding: 0.5rem 0.6rem;
  position: relative;
  overflow: hidden;
  text-align: center;
  text-decoration: none;
  transition: $transition-base;

  &:active {
    background-color: $themed-very-fair !important;
  }

  &:before {
    display: flex;
    font-weight: bold;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0px;
    width: 100%;
    height: 100%;
    text-align: center;
    font-size: 0.6rem;
    transform: scale(0,1);
    transition: all 0.2s linear 0s;
  }

  &:hover {
    text-indent: -9999px;

    &:before {
      transform: scale(1,1);
      text-indent: 0;
    }
  }
}

.survey-feedback-btn {
  background-color: $white;

  &:hover {
    background-color: $themed-light;
  }

  &.btn-danger {
    color: $danger;
    
    &:active {
      background-color: $danger;
      color: $white !important;
    }
  }

  &.btn-success {
    color: $success;

    &:active {
      background-color: $success;
      color: $white !important;
    }
  }
}

.icon-arrow-right:before {
  content: "\2192";
}

.icon-arrow-right:before {
  content: "\2713";
}

.btn-icon {
  font-size: 0;
  padding: 1.25rem 1.75rem;
  border: 0;
  position: relative;
}

.btn-icon:after {
  content: '';
  position: absolute;
  z-index: -1;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

.btn-icon:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 26px;
  line-height: 40px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
}

.btn-icon-circle {
  // background-color: $gray-200;
  border: 0;
  border-radius: 20rem;
  font-size: 1rem;
  height: 2.625rem;
  line-height: 2.625rem;
  margin-bottom: -0.125rem;
  margin-top: -0.125rem;
  padding: 0;
  transition: $transition-base;
  width: 2.625rem;

  &:hover {
    // background-color: $themed-fair;
    border: 0;
  }
  
  &.btn-icon-circle-sm {
    font-size: .875rem;
    height: 2rem;
    line-height: 2rem;
    width: 2rem;
  }

  &.btn-icon-circle-xs {
    font-size: .75rem;
    height: 1.625rem;
    line-height: 1.625rem;
    width: 1.625rem;
  }

  &.btn-icon-circle-xxs {
    font-size: .875rem;
    height: 1.125rem;
    line-height: 1.125rem;
    width: 1.125rem;
  }
}

.btn-pill {
  border-radius: 6rem;
  font-size: 0.875rem;
  line-height: 0.8125rem;
  height: calc(1.8125rem + 2px);
  padding: 0.3125rem 0.625rem .375rem;

  i {
    font-size: 1rem;
  }

  .badge {
    padding-bottom: 0.1875rem;
    padding-right: 0.3125rem;
  }
}

.btn-effect {
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
  font-size: 0;
  background: $color-primary;
  color: #fff;
  overflow: hidden;
}

.btn-effect:before {
  color: #fff;
  z-index: 1;
}

.btn-effect:after {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 0;
  width: 0;
  background: darken($color-primary, 10%);
  -webkit-transition: none;
  -moz-transition: none;
  transition: none;
}

.btn-effect.btn-activated:after {
  -webkit-animation: fillToRight 0.7s forwards;
  -moz-animation: fillToRight 0.7s forwards;
  animation: fillToRight 0.7s forwards;
}

.btn-close-basic {
  background-color: transparent;
  border-radius: 50%;
  color: $themed-base;
  cursor: pointer;
  font-size: 1.5rem;
  font-weight: bold;
  height: 2rem;
  line-height: 2rem;
  opacity: 0.65;
  position: absolute;
  right: 0.25rem;
  text-align: center;
  transition: $transition-base;
  top: 0.25rem;
  width: 2rem;

  &:hover {
    background-color: $themed-light;
    opacity: 1;
  }
}

.sticky-btn-holder {
  bottom: 0;
  left: 0;
  padding: 1.5rem 0;
  position: fixed;
  width: 100%;
  z-index: 11;

  .simple-layout & {
    position: sticky;
    width: auto; // Assume each page can handle the width on its own, so default to block width (100% of parent)
  }
}

@-webkit-keyframes fillToRight {
  to {
    width: 100%;
  }
}

@-moz-keyframes fillToRight {
  to {
    width: 100%;
  }
}

@keyframes fillToRight {
  to {
    width: 100%;
  }
}

.btn--quick-filter {
  position: relative;

  .nulodgicon-android-close:before {
    color: $themed-very-muted;
  }

  .nulodgicon-android-close:hover::before {
    color: $themed-secondary;
  }
}

// Non-functional intercom chat button styling to staging and dev visual testing
.intercom-lightweight-app-launcher--placeholder {
  background: #007bff;
  border: none;
  border-radius: 50%;
  bottom: 20px;
  box-sizing: content-box;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06), 0 2px 32px 0 rgba(0, 0, 0, 0.16);
  cursor: pointer;
  height: 48px;
  margin: 0 !important;
  max-height: 48px;
  max-width: 48px;
  padding: 0 !important;
  position: fixed;
  right: 20px;
  transition: transform 167ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
  width: 48px;
  z-index: 2147483003;
}

.intercom-lightweight-app-launcher-icon--placeholder {
  align-items: center;
  display: flex;
  height: 48px;
  justify-content: center;
  left: 0;
  position: absolute;
  top: 0;
  transition: transform 100ms linear, opacity 80ms linear;
  width: 48px;
}
