.integrations {
  .status-image {
    margin-left: 5px;
    padding-bottom: 4px;
  }

  .alert-warning {
    margin-top: 20px;
  }

  .tips-icon {
    height: 2rem;
    width: 2rem;
  }

  .beta-badge {
    background-color: $color-accent;
    border-radius: 4px;
    height: 16px;
    min-width: 20px;
    text-align: center;
    color: white;
    font-size: 11px;
    line-height: 8px;
    padding: 4px 6px;
  }

  .nulodgicon-checkmark {
    background-color: white;
    border-radius: 50%;
    border: 1px solid $themed-fair;
    color: white;
    display: block;
    font-size: 0.875rem;
    height: 1.5rem;
    line-height: 1.5rem;
    position: absolute;
    left: 1rem;
    text-align: center;
    top: 50%;
    transition: $transition-base;
    transform: translateY(-50%);
    width: 1.5rem;
  }

  :checked ~ .nulodgicon-checkmark {
    background-color: $red;
    border-color: $red;
  }

  .bank-card-box {
    height: 14rem;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  [type='radio'] {
    display: none;
  }

  .instructions-list {
    list-style: disc;
  }

  .comming-soon-btn {
    max-width: 50px;
  }

  .toggle-float .slide-toggle-wrap {
    float: right;
  }

  .heading-holder {
    border-bottom: 1px solid $themed-light;
  }

  .item-block{
    position: relative;
    perspective: 800px;
    margin-right: 15px;
    transform-style: preserve-3d;
    transition: transform 5s;
    position:relative;
  }

  .company-block {
    position: relative;
  }

  .company-block .divider {
    height: 10px;
    background-color: $themed-light;
    height: 5px;
    position: absolute;
    width: 100%;
    z-index: 9;
  }

  .company-block .divider:after {
    content: '';
    position: absolute;
    width: 37%;
    left: 0;
    right: 0;
    background-color: $color-primary;
    height: 5px;
  }

  .company-block .image-holder{
    position: relative;
    max-height: 205px;
    overflow: hidden;
    min-height: 155px;
    display: flex;
    align-items: center;
  }

  .company-block .stats-container .icon-holder {
    position: absolute;
    right: 18px;
    width: 20px;
    height: 17px;
    border-radius: 100%;
  }

  .company-block .stats-container .icon-holder .account-status {
    width: 13px;
    height: 13px;
    display: inline-block;
    border-radius: 100%;
    border: 1px solid #fff;

    &.danger {
      background: $color-danger;
      border: 1px solid $color-danger;
    }

    &.success {
      background: $color-success;
      border: 1px solid $color-success;
    }

    &.warning {
      background: $color-warning;
      border: 1px solid $color-warning;
    }

    &.disabled {
      background: $gray-500;
      border: 1px solid $gray-500;
    }
  }

  .company-block .stats-container .icon-holder .customize-meraki {
    z-index: 999;
    height: 25px;
    width: 25px;
    right: 28px;
    position: absolute;
    bottom: -4px;

    &:hover {
      background-color: $themed-very-fair;
      border: 1px solid $themed-very-fair;
      border-radius: 16%;
    }
  }

  .company-block .image-holder .logo-holder {
    max-width: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
  }

  .company-block .image-holder i {
    background: $teal;
    position: absolute;
    right: 15px;
    top: 25px;
    border-radius: 100%;
    width: 18px;
    height: 18px;
    font-size: 17px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 16px;
  }

  #product-front,
  #product-back {
    background:#fff;
    transition: all 100ms ease-out;
  }

  #product-back{
    display:none;
    transform: rotateY( 180deg );
  }

  #product-card.animate #product-back,
  #product-card.animate #product-front {
    top: 0;
    left: 0;
    transition: all 100ms ease-out;
  }

  #product-card{
    border: 1px solid $themed-lighter;
    border-radius: 5px;
    box-shadow: 0px 9px 15px -9px rgba(0, 0, 0, 0.3);
    overflow:hidden;
    transform-style: preserve-3d;
    transition: 100ms ease-out;
  }

  #product-card.back-drop .company-block .module,
  #product-card.back-drop .company-block img {
    opacity:0.3;
  }

  #product-card:hover
  #product-card.animate{
    top: 5px;
    left: 5px;
    width: 335px;
    height: 500px;
    box-shadow: 0px 13px 21px -5px rgba(0, 0, 0, 0.3);
    transition: 100ms ease-out;
  }

  .stats-container{
    min-height: 67px;
    display: flex;
    align-items: flex-start;
    background:#fff;
    text-align: left;
    border-top: 1px dashed $themed-light;
    padding:15px;
    transition: all 200ms ease-out;
  }

  .stats-container p {
    margin-bottom:0;
  }

  #product-card.animate .stats-container{
    transition: all 200ms ease-out;
  }

  .stats-container .company_name {
    font-size: 16px;
    color: $themed-dark;
    font-weight: bold;
    vertical-align: top;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    position: relative;
    padding-right: 20px;
  }

  .stats-container .company-tag {
    padding: 2px 5px;
    border-radius: 2px;
    font-size: 10px;
    color: #fff;
    vertical-align: text-bottom;
    position: absolute;
    top: 8px;
    right: 0px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    &.active {
      background: $color-primary;
    }

    &.inactive {
      background: $color-warning;
    }
  }

  .stats-container p {
    font-size: 12px;
    color: $themed-muted;
    padding: 2px 0 0 0;
  }

  .stats-container .stats-icon{
    float: right;
    color: $color-safe;
    font-size: 18px;
    font-weight: 600;
    position: absolute;
    top: 0;
    right: 0;
  }

  .stats-container .stats-icon .warrning-icon{
    color: $color-danger;
  }

  #product-card.animate .stats {
      display: block;
  }

  .image_overlay{
    position:absolute;
    top: 0;
    left: 0;
    background: $color-primary;
    opacity: 0;
  }

  #product-card.animate .image_overlay{
    opacity: 0.9;
    transition: all 200ms ease-out;
  }

  .product-options {
    padding: 2px 0 0;
  }

  .product-options strong {
    font-weight:700;
    color:$themed-dark;
    font-size:14px;
  }

  .product-options span {
    color: $gray-500;
    font-size: 14px;
    display: block;
    margin-bottom: 8px;
  }

  #view_details,
  #coming_soon {
    position: absolute;
    top: 112px;
    left: 50%;
    margin-left: -85px;
    border: 2px solid #fff;
    color: #fff;
    font-size: 19px;
    text-align: center;
    text-transform: uppercase;
    font-weight: 700;
    padding: 10px 0;
    width: 172px;
    opacity: 0;
    transition: all 200ms ease-out;
  }

  #view_details:hover,
  #coming_soon:hover {
    background: #fff;
    color: $color-primary;
    cursor: pointer;
  }

  #product-card.animate #view_details,
  #product-card.animate #coming_soon {
    opacity: 1;
    max-width: 152px;
    font-size: 15px;
    margin-left: -75px;
    top: 25%;
    transition: all 200ms ease-out;
  }

  .module {
    overflow: hidden;
    line-height: 1;
  }

  .module p {
    margin: 0;
  }

  .line-clamp {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .integration-info {
    min-height: 69px;
  }

  .small-muted {
    display: inline-block;
    font-size: 65%;
    font-weight: 400;
    line-height: 12px;
  }

  .tooltip-heading {
    padding-top: 10px;
  }
}

.sync-status {
  width: 13px;
  height: 13px;
  display: inline-block;
  border-radius: 100%;
  border: 1px solid #fff;
  position: absolute;
  right: 20px;
  top: 20px;

  &.danger {
    background: $color-danger;
    border: 1px solid $color-danger;
  }

  &.success {
    background: $color-success;
    border: 1px solid $color-success;
  }

  &.warning {
    background: $color-warning;
    border: 1px solid $color-warning;
  }

  &.disabled {
    background: $gray-500;
    border: 1px solid $gray-500;
  }
}
