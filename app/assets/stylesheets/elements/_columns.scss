/* Adjustments and additional classes for the .row, .col bootstrap classes */

// This is a flex-box hack that will force any `.col` to wrap as expected 
// instead of forcing any subsequent .col-auto to drop to the next line.
// It's main use is going to be when you want to limit the width of child elements to the available parent width,
// (for white-space ellipses functionality) but the parent is a flexible `.col`. E.g.
// 
// <div class="row">
//   <div class="col-auto">...</div>
//   <div class="col col--force-wrap-x">
//     <div class="truncate">Some really long content that I want to automatically cut off and ellipses.</div>
//   </div>
// </div>
// 
// If a child element has a nowrap white space or flex on it, though, this class will cause the children to shrink, instead.
// This is useful for forcing the `overflow: ellipses;` functionality, so the `.col--force-shrink-x` class is here for semnatic clarity.
//
// All this functionality also works with manual uses of flex boxes, 
// so the `flex` versions of these are included for further semantic clarity.
.col--force-wrap-x,
.col--force-shrink-x,
.flex-child-force-wrap-x,
.flex-child-force-shrink-x {
  min-width: 0;
}

.col--force-wrap-y,
.col--force-shrink-y,
.flex-child-force-wrap-y,
.flex-child-force-shrink-y {
  min-height: 0;
}
