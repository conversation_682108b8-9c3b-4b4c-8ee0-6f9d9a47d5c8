// CSS Grid (true grid)
.grid-wrapper {
  align-items: stretch;
  column-gap: 2.25rem;
  display: grid;
  grid-auto-rows: minmax(16rem, auto);
  grid-template-columns: 1fr 1fr;
  row-gap: 2.25em;

  @include content-breakpoint-down('lg') {
    grid-template-columns: 100% !important;

    .grid-item {
      grid-column: 1 !important;
      grid-row: auto !important;
    }
  }
}

.grid-wrapper--5-7 {
  grid-template-columns: 5fr 7fr;
}

.rowspan-1 { 
  grid-row: span 1;
}

.rowspan-2 {
  grid-row: span 2;
}

.rowspan-3 {
  grid-row: span 3;
}

.colspan-2 {
  grid-column: span 2;
}
