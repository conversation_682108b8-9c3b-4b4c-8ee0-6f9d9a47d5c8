.pos-index {
  z-index: 0!important;
  position: relative;
}

.hidden-market-link {
  min-height: 3px;
  min-width: 3px;
  position: absolute;
  bottom: 0px;
  left: -40px;
  display: inline-block;
}

.page-scroll-wrap {
  .site-footer {
    background-color: $themed-footer-bg;
    min-width: calc(100vw - #{$drawer-collapse-width});
  }

  .bottom-module-line {
    background-color: $themed-dark-drawer-bg;
    bottom: 0;
    height: $bottom-bar-height;
    left: $drawer-collapse-width;
    position: fixed;
    transition: $nav-transition-out;
    width: calc(100vw - #{$drawer-collapse-width});
    z-index: map-get($zIndex, 'drawer-closed') + 1;

    @each $module, $color in $module-colors {
      .module--#{$module} & {
        background: linear-gradient(90deg, $themed-dark-drawer-bg 5%, $color 45%);
      }
    }

    &:before {
      background-color: transparent;
      border-bottom-left-radius: 1rem;
      bottom: $bottom-bar-height;
      box-shadow: 0 1.5rem 0 0 $themed-dark-drawer-bg;
      content: "";
      position: absolute;
      height: 3rem;
      width: 1.5rem;
    }

    @media only screen and ($max: $medium) {
      left: 0;
      width: 100vw;

      @each $module, $color in $module-colors {
        .module--#{$module} & {
          background: $color;

          &:before {
            box-shadow: 0 1.5rem 0 0 $color;
          }
        }
      }
    }
  }
}

// Simple alternative for non-module pages, 
// in particular **public facing open ticket pages**
.simple-layout {
  .site-header__left {
    padding-left: 1.5rem;
  }

  .page-scroll-wrap,
  .simplebar-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .main-page-content {
    flex: 1;
    padding-top: $header-height;
  }

  .site-footer {
    background: none !important; // See note below
    min-width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .bottom-module-line {
    display: none;
  }
}

.simple-layout--dark-bg {
  .site-footer {
    color: white!important;

    // Only using important in these sections because it's a unique customer facing page 
    // that we want to always look balanced with our co-branding, 
    // so the footer color needs to not be accidentally overriden.
    a {
      color: white!important;
      text-decoration: underline;
    }
  }
}

.footer-love {
  color: $red;

  @each $module, $color in $module-colors {
    .module--#{$module} & {
      color: $color;
    }
  }
}
