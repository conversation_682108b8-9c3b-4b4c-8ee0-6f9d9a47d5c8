code {
  background-color: $themed-light;
  border-radius: $border-radius-sm;
  padding: 0.125em;
}

h1, .h1 {
  line-height: round(map-get($heading-multiplyers, 'h1') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h1'));
}

h2, .h2 {
  line-height: round(map-get($heading-multiplyers, 'h2') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h2'));
}

h3, .h3 {
  line-height: round(map-get($heading-multiplyers, 'h3') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h3'));
}

h4, .h4 {
  line-height: round(map-get($heading-multiplyers, 'h4') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h4'));
}

h5, .h5 {
  line-height: round(map-get($heading-multiplyers, 'h5') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h5'));
}

h6, .h6 {
  line-height: round(map-get($heading-multiplyers, 'h6') * $headings-line-height * $font-size-base-pixels) / ($font-size-base-pixels * map-get($heading-multiplyers, 'h6'));
}
