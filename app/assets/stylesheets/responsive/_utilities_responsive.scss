/*
 * Borders
 */
.border-r-300 {
  @include media-breakpoint-up(sm) {
    border-right: 0;
  }
  @include media-breakpoint-up(md) {
    border-right: 1px solid $themed-very-fair;
  }
}


/*
 * Display
 */
.nulodgic-desktop {
  @media($max: $medium) {
    display: none;
  }
}

.nulodgic-mobile {
  display: none;
  @media($max: $medium) {
    display: flex;
  }
}

.nulodgic-tablet {
  display: none;
  @media(min-width: 1024px) {
    display: inline-block;
  }
}

.nulodgic-tablet--responsive {
  display: none;
  @media($max: 1023px) {
    display: flex;
  }
}

.d-flex-column--medium {
  @media ($max: $medium) {
    display: flex;
    flex-direction: column;
  }
}

.d-flex-column--large {
  @media ($max: $large) {
    display: flex;
    flex-direction: column;
  }
}

/*
 * Positioning
 * Responsive specfic classes assigned in
 * app/assets/stylesheets/utilities/_positioning.scss
 */

/*
 * Existing Typography
 */
.text-secondary {
  @media($max: $medium) {
    font-size: 0.85rem;
  }
  @media($max: $small) {
    font-size: 0.8rem;
  }
}

.not-as-small {
  @media($max: $medium) {
    font-size: 0.75rem;
  }
}

.display-3 {
  @media($max: 900px) {
    font-size: 3.5rem;
  }
  @media($max: $small) {
    font-size: 2.7rem;
  }
}

/*
 * Responsive Specific Typography Classes
 */
.h2--responsive {
  @media($max: $medium) {
    font-size: 1.625rem;
  }
  @media($max: $small) {
    font-size: 1.5rem;
  }
}

.h3--responsive {
  @media($max: $medium) {
    font-size: 1.375rem;
  }
  @media($max: $small) {
    font-size: 1.2rem;
  }
}

.h4--responsive {
  @media($max: $medium) {
    font-size: 1.3rem;
  }
  @media($max: $small) {
    font-size: 1.17rem;
  }
}

.h5--responsive {
  @media($max: $medium) {
    font-size: 1.2rem;
  }
  @media($max: $small) {
    font-size: 0.85rem !important;
  }
}

.h6--responsive {
  @media($max: $medium) {
    font-size: 0.9rem;
  }
  @media($max: $small) {
    font-size: 0.8rem;
  }
}

.p--responsive {
  @media($max: $medium) {
    font-size: 0.75rem;
  }
  @media($max: $small) {
    font-size: 0.7rem;
  }
}

.xs--responsive {
  @media($max: $small) {
    font-size: 0.8rem;
  }
}

.muted--responsive {
  @media($max: $medium) {
    font-size: 0.6rem;
  }
  @media($max: $small) {
    font-size: 0.5rem;
  }
}

.sample-company-warning {
  @media($max: $medium) {
    font-size: 0.6875rem;
    padding: 8px 15px;
  }
}
