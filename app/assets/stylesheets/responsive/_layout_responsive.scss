/*
 * Header
 */
.menu-closed {
  @media only screen and ($max: $medium) {
    .drawer {
      transform: translateX(-$drawer-width);
    }
  
    .drawer-subscription {
      display: none;
    }
  }
}

.company-drawer__name,
.workspace-drawer__name {
  max-width: 20rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @media only screen and ($max: $large) {
    max-width: 11rem;
  }
  @media only screen and ($min: $large) and ($max: map-get($grid-breakpoints, 'xl')) {
    max-width: 14rem;
  }
  @media only screen and ($min: map-get($grid-breakpoints, 'xl')) and ($max: map-get($grid-breakpoints, 'xxl')) {
    max-width: 17rem;
  }
}

#app_toggle_menu {
  @media only screen and ($max: map-get($grid-breakpoints, 'lg')) {
    right: 2rem;
  }
}

@media only screen and ($max: 33rem) {
  .drawer-subscription {
    width: 4rem;
  }

  .drawer-subscription-menu-open {
    display: none;
  }

  .drawer-subscription-menu-closed {
    display: flex;
  }
}

.site-header-link {
  @media only screen and ($max: $medium) {
    padding: 0 0.2rem;
  }
}

/*
 * Responsive design banner
 * Scoped to ID as a precaution, since this won't be touched or tested most releases
 */
@media only screen and ($max: $large) {
  #small_viewport_warning {
    display: block !important;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 9999;

    .module--help-tickets & {
      display: none !important;
    }
  }

  .main-page-content {
    padding-top: 4rem;
    position: relative;

    .module--help-tickets & {
      @media only screen and ($max: $large) {
        padding-top: 0;
      }
    }
  }

  .module--assets {
    #small_viewport_warning {
      display: none !important;
    }
    .main-page-content {
      padding-top: 2.5rem;
    }
  }
}
