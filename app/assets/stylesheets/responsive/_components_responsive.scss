/*
 * Box
 */
.box,
.half-box {
  @media($max: $small) {
   padding: 0.9rem;
  }
}

/*
 * Buttons
 */
$btn-height: 3rem;
$btn-mbl-height: 2.5rem;

.back-to-link {
  @include media-breakpoint-up(sm) {
    display: block;
    height: $btn-mbl-height;
    line-height: $btn-mbl-height;
    text-align: right;
    width: 100%;
  }
  @include media-breakpoint-up(md) {
    display: inline;
    height: $btn-height;
    line-height: $btn-height;
    text-align: unset;
    width: auto;
  }
}

.edit-delete-btn {
  @include media-breakpoint-up(sm) {
    font-size: 0.85rem;
    height: 2rem;
    line-height: 1.3rem;
    width: 2rem;
  }
  @include media-breakpoint-up(md) {
    font-size: 1.25rem;
    height: $btn-height;
    line-height: 2rem;
    width: $btn-height;
  }
  &:before {
    @media($max: $medium) {
      font-size: 0.5rem;
    }
  }
}

.quick-view-edit-btn {
  @include media-breakpoint-up(sm) {
    font-size: 0.5rem;
    height: 1.5rem;
    line-height: 1rem;
    width: 1.5rem;
  }
  @include media-breakpoint-up(md) {
    font-size: 0.8rem;
    height: 2.2rem;
    line-height: 1.2rem;
    width: 2.2rem;
  }
  &:before {
    @media($max: $medium) {
      font-size: 0.3rem;
    }
  }
}

.form-btn--responsive {
  @media($max: $medium) {
    font-size: 0.8rem !important;
  }
  @media($max: $small) {
    font-size: 0.7rem !important;
  }
}

.vertical-theme-toggle {
  @media($max: 1100px) {
    display: none;
  }
}
/*
 * Form
 */
.form-group {
  @media($max: $medium) {
    margin-bottom: 1.3rem;
  }

  label {
    @media($max: $medium) {
      font-size: 0.78rem;
    }
    @media($max: $small) {
      font-size: 0.7375rem;
    }
  }
}

.form-control {
  @media($max: $medium) {
    font-size: 0.8rem;
  }
  @media($max: $small) {
    font-size: 0.7rem;
  }
}

.multiselect {
  .multiselect__placeholder {
    @media($max: $small) {
      font-size: 12px;
    }
  }
}

select {
  @media($max: $medium) {
    background-position-y: 2px;
  }
}

/*
 * General Components
 */
.select-per-page-filter {
  @media($max: $medium) {
    line-height: 1;
    padding-left: 0.525rem;
  }
}

/*
 * Layouts
 */
.global-simplebar-content.simplebar-content {
  @media($max: $medium) {
    padding: 48px 0 0 0 !important;
  }
}

.container,
.container-fluid {
  transition: $transition-base;

  @include media-breakpoint-up(sm) {
    padding-left: $container-padding-sm;
    padding-right: $container-padding-sm;
  }

  @include media-breakpoint-up(md) {
    padding-left: $container-padding;
    padding-right: $container-padding;
  }

  @include media-breakpoint-up(lg) {
    padding-left: $container-padding-lg;
    padding-right: $container-padding-lg;
  }

  @include media-breakpoint-up(xl) {
    padding-left: $container-padding-xl;
    padding-right: $container-padding-xl;
  }
}

// Remove content restrictions when > xl and have wide-theme active
@media (min-width: map-get($grid-breakpoints, 'xl')) {
  .content-wide-theme .container {
    max-width: 100%;
    transition: $transition-base;
  }
}

// Unsure restricted content when > xxl to when wide-theme not actice
@media (min-width: map-get($grid-breakpoints, 'xxl')) {
  .container {
    max-width: map-get($container-max-widths, 'xl');
  }

  .content-wide-theme .container {
    max-width: 100%;
  }
}

// Create conditional grid col-xxl-* classes that only apply when wide-theme is active
@media (min-width: map-get($grid-breakpoints, 'xxl')) {
  .content-wide-theme {
    .col-xxl-wide-theme {
      flex-basis: 0;
      flex-grow: 1;
      max-width: 100%;
    }

    .col-xxl-wide-theme-auto {
      flex: 0 0 auto;
      width: auto;
      max-width: none;
    }

    @for $i from 1 through $grid-columns {
      .col-xxl-wide-theme-#{$i} {
        @include make-col($i, $grid-columns);
      }

      .offset-xxl-wide-theme-#{$i} {
        @include make-col-offset($i, $grid-columns);
      }
    }
  }
}

.container-btn--responsive {
  @media($max: $medium) {
    font-size: 0.7rem !important;
  }
}

.avatar-logo--responsive {
  @media($max: $medium) {
    font: 16px / 40px Helvetica, Arial, sans-serif !important;
    height: 40px !important;
    width: 40px !important;
  }
  @media($max: $small) {
    font-size: 14px !important;
    line-height: 36px !important;
    height: 35px !important;
    width: 35px !important;
  }
}

.sweet-modal.is-mobile-fullscreen.is-visible {
  @media($max: $medium) {
    width: 100% !important;
  }
}

/*
 * Pagination
 */
.page-link {
  @include media-breakpoint-up(md) {
    font-size: 0.675rem;
  }
  @include media-breakpoint-up(lg) {
    font-size: 0.875rem;
  }
}

/*
 * Submenu
 * submenu components were not styling properly here and
 * are currently still integrated in app/assets/stylesheets/components/_submenu.scss
 */

 /*
 * Subtabs
 */

 .module-sub-tabs {
  @include media-breakpoint-up(sm) {
    flex-wrap: wrap;
  }
  @include media-breakpoint-up(md) {
    flex-wrap: nowrap;
  }
}

trix-toolbar {
  .trix-button {
    @media($max: $medium) {
      font-size: 0.575rem !important;
    }
    @media($max: $small) {
      font-size: 0.45rem !important;
    }
  }
}

/*
 * vue-tel-phone
 */
.vti__input {
  @media($max: $medium) {
    font-size: 12.5px !important;
  }
}

/*
 * nice-date-picker
 */
.friendly-date {
  @media($max: $medium) {
    display: none;
  }
}
.mx-datepicker-popup {
  @media($max: $medium) {
    padding-left: 0 !important;
  }
}

/*
 * inputs focus
 */

@media screen and (-webkit-min-device-pixel-ratio:0) {
  select,
  textarea,
  input {
    @media($max: $medium) {
      font-size: 16px !important;
    }
  }
}
/*
 * sweet-modal
 */
.sweet-modal {
  @media($max: $medium) {
    height: 100% !important;
  }
}
.sweet-modal-overlay {
  @media($max: $medium) {
    height: 100% !important;
  }
}

.sweet-modal .sweet-title > h2 {
  @media($max: $medium) {
    text-align: left;
    font-size: 14px !important;
    padding-left: 10px !important;
  }
}
.popup-menu {
  @media($max: $medium) {
    min-width: 100px !important;
    left: auto !important;
    right: -5px;
  }
}
