.logo-outline {
  box-shadow: $shadow-base;
  color: $white !important;
  object-fit: cover;

  span {
    padding-top: 1px;
    vertical-align: middle;
    display: inline-block;
    line-height: normal;
  }
}

.logo-circle {
  border-radius: 50%;
}

.vue-avatar--wrapper {
  background-position: center !important;
  background-repeat: no-repeat;
  background-size: cover !important;
  font-weight: 500 !important;
  font-family: $font-family-sans-serif !important;

  // The Vue Avatar uses a gray color that clashes with our bluer grays. 
  // Rather than manually pass in a whole new array of colors just to change the single gray,
  // we manually override that gray color here.
  &[style*="background-color:rgb(158,158,158)"],
  &[style*="background-color: rgb(158,158,158)"],
  &[style*="background-color: rgb(158, 158, 158)"] {
    background-color: $themed-muted !important;
  }
}