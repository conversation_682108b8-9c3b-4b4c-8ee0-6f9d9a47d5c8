.clear-both {
  clear: both !important;
}

.align-sub {
  vertical-align: sub !important;
}

.align-inherit {
  vertical-align: inherit !important;
}

// Adds negative and partial margin classes for more flexible spacing
// Note: Please use the partial spacers carefully, so as not to upset the vertical rhythm.

$positions:
  "t" "y" "top",
  "b" "y" "bottom",
  "l" "x" "left",
  "r" "x" "right";

$breaks: "sm", "md", "lg", "xl";

@each $size, $space in $advanced-spacers {
  $dotIndex: str-index($size, ".");
  $integer: str-slice($size, 0, $dotIndex - 1);
  $decimal: str-slice($size, $dotIndex + 1);
  
  .m-#{$integer}\.#{$decimal} {
    margin: $space;
  }

  .p-#{$integer}\.#{$decimal} {
    padding: $space;
  }

  @each $break, $value in $grid-breakpoints {
    @if "#{$value}" != "0" {
      @media (min-width: $value) {
        .m-#{$break}-#{$integer}\.#{$decimal} {
          margin: $space !important; // using important to align with bootstrap specificity standards
        }

        .p-#{$break}-#{$integer}\.#{$decimal} {
          padding: $space !important; // using important to align with bootstrap specificity standards
        }
      }
    }
  }
}

@each $dir, $axis, $name in $positions {
  @each $size, $space in $all-spacers {
    @if (str-index('#{$size}', '.')) {
      $dotIndex: str-index($size, ".");
      $integer: str-slice($size, 0, $dotIndex - 1);
      $decimal: str-slice($size, $dotIndex + 1);

      .m#{$dir}-#{$integer}\.#{$decimal},
      .m#{$axis}-#{$integer}\.#{$decimal} {
        margin-#{$name}: $space;
      }

      .p#{$dir}-#{$integer}\.#{$decimal},
      .p#{$axis}-#{$integer}\.#{$decimal} {
        padding-#{$name}: $space;
      }

      .m#{$dir}-n#{$integer}\.#{$decimal},
      .m#{$axis}-n#{$integer}\.#{$decimal} {
        margin-#{$name}: -1 * $space;
      }

    } @else {
      @if "#{$size}" != "0" {
        .m#{$dir}-n#{$size},
        .m#{$axis}-n#{$size} {
          margin-#{$name}: -1 * $space;
        }
      }
    }
  }
}

@each $break, $value in $grid-breakpoints {
  @if "#{$value}" != "0" {
    @media (min-width: $value) {
      @each $dir, $axis, $name in $positions {
        @each $size, $space in $all-spacers {
          @if (str-index('#{$size}', '.')) {
            $dotIndex: str-index($size, ".");
            $integer: str-slice($size, 0, $dotIndex - 1);
            $decimal: str-slice($size, $dotIndex + 1);

            .m#{$dir}-#{$break}-#{$integer}\.#{$decimal},
            .m#{$axis}-#{$break}-#{$integer}\.#{$decimal} {
              margin-#{$name}: $space !important; // using important to align with bootstrap specificity standards
            }

            .p#{$dir}-#{$break}-#{$integer}\.#{$decimal},
            .p#{$axis}-#{$break}-#{$integer}\.#{$decimal} {
              padding-#{$name}: $space !important; // using important to align with bootstrap specificity standards
            }

            .m#{$dir}-#{$break}-n#{$integer}\.#{$decimal},
            .m#{$axis}-#{$break}-n#{$integer}\.#{$decimal} {
              margin-#{$name}: -1 * $space !important;
            }

          } @else {
            @if "#{$size}" != "0" {
              .m#{$dir}-#{$break}-n#{$size},
              .m#{$axis}-#{$break}-n#{$size} {
                margin-#{$name}: -1 * $space !important;
              }
            }
          }
        }
      }
    }
  }
}
