// Used to keep child line heights the same, even if font sizes vary, like when using an icon
.flatten-line-height {
  line-height: 1rem !important;
}

.readable-line-height {
  line-height: $line-height-base;
}

.inherit-line-height {
  line-height: inherit;
}

.readable-length {
  max-width: 42rem;

  @include media-breakpoint-up(xl) {
    max-width: 70rem;
  }
}

.readable-length--xx-small {
  max-width: 10rem;
}

.readable-length--x-small {
  max-width: 18rem;
}

.readable-length--small {
  max-width: 28rem;
}

.readable-length--medium {
  max-width: 52rem;
}

.readable-length--large {
  max-width: 60rem;
}

.font-weight-semi-bold {
  font-weight: 500 !important;
}

.no-user-select {
  user-select: none;
}

.text-underline {
  text-decoration: underline !important;
}

.text-uppercase {
  letter-spacing: 0.65px;
  text-transform: uppercase !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-spaced {
  letter-spacing: 0.5px;
}

.white-space-normal {
  white-space: normal !important;
}

.scale-up {
  transform: scale(1.125);
}

.scale-down {
  transform: scale(0.875);
}

.big {
  font-size: 1.25rem !important;
}

.not-as-big {
  font-size: 1.125rem !important;
}

.base-font-size {
  font-size: 1rem !important;
}

.reduced {
  font-size: 0.9375rem !important;
}

.not-as-small {
  font-size: 0.875rem !important;
}

// Most instances of .small in our result in 13px font, but with the unnecessary sub pixels. This is a true 13px font.
.true-small {
  font-size: 0.8125rem !important; 
}

.smallest {
  font-size: 0.75rem !important;
}

.break-word {
  word-break: break-word;
  hyphens: auto;
}

.nowrap {
  white-space: nowrap !important;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow-x: hidden;
  // specify max-width in the element you're truncating
}

@for $i from 0 through 100 {
  @if $i % 10 == 0 {
    .opacity-#{$i} {
      opacity: #{$i/100};
    }
  }
}

