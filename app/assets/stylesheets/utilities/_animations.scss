.basic-transition {
  transition: $transition-base;
}

.fade-enter-active,
.fade-leave-active {
  transition: $transition-base;
  transition-property: opacity;
}

.fade-enter,
.fade-leave-to {
  opacity: 0
}

[data-overlay-reloader] {
  position: relative;

  &:after {
    animation: reloaderPulse 1.5s infinite ease-in-out;
    background-color: rgba($white, 0.65);
    bottom: 0;
    color: $color-dark;
    content: "Ensuring apps are up-to-date...";
    font-size: 1.5rem;
    font-style: italic;
    left: 0;
    letter-spacing: 1px;
    opacity: 0;
    padding-top: map-get($spacers, 6);
    pointer-events: none;
    position: absolute;
    right: 0;
    text-align: center;
    transition: $transition-base;
    top: 0;
  }

  &.is-reloading:after {
    opacity: 1;
  }
}

@keyframes reloaderPulse {
  0% {
    background-color: rgba($white, 0.65);
  }

  50% {
    background-color: rgba($white, 0.9);
  }

  100% {
    background-color: rgba($white, 0.65);
  }
}
