@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {
  $min: map-get($breakpoints, $name);
  @return if($min != 0, $min, null);
}

@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {
  $max: map-get($breakpoints, $name);
  @return if($max and calc($max > 0), $max - .02, null);
}

@mixin content-breakpoint-up($name) {
  $min: breakpoint-min($name, $container-breakpoints);
  @if $min {
    @container module-content (min-width: #{$min}) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin content-breakpoint-down($name) {
  $max: breakpoint-max($name, $container-breakpoints);
  @if $max {
    @container module-content (max-width: #{$max}) {
      @content;
    }
  } @else {
    @content;
  }
}
