// SASS variable overrides must be declared before loading up Active Admin's styles.
//
// To view the variables that Active Admin provides, take a look at
// `app/assets/stylesheets/active_admin/mixins/_variables.scss` in the
// Active Admin source.
//
// For example, to change the sidebar width:
// $sidebar-width: 242px;

@import 'formadmin/formadmin';
@import "active_admin/searchable_select";
// Overriding any non-variable SASS must be done after the fact.
// For example, to change the default status-tag color:
//
//   .status_tag { background: #6090DB; }

.active_admin_dialog { width: auto !important; }

#active_admin_content {
  padding: 10px 10px 0;
}

#active_admin_content #sidebar {
  width: 20%;
  display: inline-block;
  margin-left: 0;
}

.panel,
.sidebar_section {
  margin-left: 2px;
}
#active_admin_content.with_sidebar #main_content {
  margin-right: 0px;
}

#active_admin_content #main_content_wrapper {
  width: 80%;
  display: inline-block;
}

.stats-header {
  width: 300px;
  border: 1px solid #cccccc;
  border-width: 0 1px 1px 0;
  vertical-align: bottom;
  background-color: #f2f2f2;
  background-image: linear-gradient(to bottom, #f2f2f2, #e6e6e6);
  box-shadow: inset 0 1px 0 #ffffff;
  border-bottom: 1px solid #cccccc;
  padding: 12px 15px;
}
.paginated_collection.stats {
  margin: 35px 24px;
  width: 1000px;
}

.turbolinks-progress-bar {
  visibility: hidden;
}

.image-file-name {
  background-color: #f9f9f9 !important;
  border: 0px solid #f9f9f9 !important;
}

.sidebar_section label {
  text-transform: none;
}

textarea.handle-textarea-height {
  overflow-y: auto;
  height: auto;
  max-height: 25rem;
}
