@charset 'UTF-8';
/*!  Force output of charset by adding a unicode character. ♫ */

/*
 * Inverted Triangle CSS Specificity
 * (the lower on the triangle, the more specific a selector is)
 * ________________
 * \______________/   SETTINGS
 *  \____________/    TOOLS
 *   \__________/     GENERIC
 *    \________/      ELEMENTS
 *     \______/       VENDOR
 *      \____/        COMPONENTS
 *       \__/         VIEWS
 *        \/          UTILITIES
 */


/*
 * SETTINGS:
 * Include all variables
 */
@import "settings/lato-font";
@import "settings/nulodgicons";
@import "settings/genuicons";
@import "bootstrap-functions";
@import "bootstrap-colors";
@import "bootstrap-variables";
@import "settings/custom-variables";

@import "settings/css-variables";
@import "settings/theming-overrides";

/*
 * TOOLS:
 * Include vendor & custom mixins
 */
@import "bootstrap/mixins";
@import "tools/*";

/*
 * GENERIC:
 * Resets, normalizers, and global '*' selectors
 * (most of this is already handled by bootstrap)
 */
// @import "generic/*";

/*
 * ELEMENTS:
 * Include global declarations for raw node selectors (e.g. hr, section, article, p)
 */
@import "elements/*";

/*
 * VENDOR:
 * Include vendor css before any of our components, so that we can override it easily
 */
 @import "vendor/*";

/*
 * COMPONENTS:
 * Small, reusable items are called components (single elements), like a button, or a link with an icon
 */
@import "components/*";

/*
 * VIEWS:
 * I.e., individual pages. Most pages will have some one-time use elements that need their own styling
 * (but try to create/utilize reusable components and objects whenever possible)
 */
@import "views/*";

/*
 * UTILITIES:
 * Include helper or "utility" classes that will guarantee an element behaves a certain way
 * (Pretty much the only place it makes sense to use `!important` in a declaration)
 */
@import "utilities/*";

/*
 * RESPONSIVE:
 * Mobile responsive classes seperated out by general components and specfiic modules.
 */
@import "responsive/*";
