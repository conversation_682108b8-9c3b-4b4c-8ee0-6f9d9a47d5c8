/*
 * Resets and client-specific CSS
 */

.ExternalClass {
  width:100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

#outlook a {
  padding: 0;
}

*:not(br):not(tr):not(html) {
  font-family: Verdana, Arial, 'Helvetica Neue', Helvetica, sans-serif;
  box-sizing: border-box;
}

body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  padding: 0;
}

h1, h2, h3, h4, h5, h6 {
  color: #212529 !important;
}

img {
  border: 0 none;
  height: auto;
  line-height: 100%;
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
}

a img {
  border: 0 none;
}

table {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

table, td {
  border-collapse: collapse;
}

p,
ul,
ol,
blockquote {
  line-height: 1.5;
  text-align: left;
}

a {
  color: #0d6efd;
}

td {
  word-break: break-word;
}

#body_table {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
}

.image-fix {
  display: block;
}


/*
 * Bootstrap helpers, settings, and components
 */

$directions: (
  "l": "left",
  "r": "right",
  "b": "bottom",
  "t": "top"
);
$spacing: (
  0: 0,
  1: 4px,
  2: 8px,
  3: 16px,
  4: 24px,
  5: 48px
);
@each $letter, $direction in $directions {
  .m#{$letter}-0 { margin-#{$direction}: map-get($spacing, 0) !important; }
  .m#{$letter}-1 { margin-#{$direction}: map-get($spacing, 1) !important; }
  .m#{$letter}-2 { margin-#{$direction}: map-get($spacing, 2) !important; }
  .m#{$letter}-3 { margin-#{$direction}: map-get($spacing, 3) !important; }
  .m#{$letter}-4 { margin-#{$direction}: map-get($spacing, 4) !important; }
  .m#{$letter}-5 { margin-#{$direction}: map-get($spacing, 5) !important; }

  .p#{$letter}-0 { padding-#{$direction}: map-get($spacing, 0) !important; }
  .p#{$letter}-1 { padding-#{$direction}: map-get($spacing, 1) !important; }
  .p#{$letter}-2 { padding-#{$direction}: map-get($spacing, 2) !important; }
  .p#{$letter}-3 { padding-#{$direction}: map-get($spacing, 3) !important; }
  .p#{$letter}-4 { padding-#{$direction}: map-get($spacing, 4) !important; }
  .p#{$letter}-5 { padding-#{$direction}: map-get($spacing, 5) !important; }
}
@each $spacer, $value in $spacing {
  .my-#{$spacer} {
    margin-bottom: $value !important;
    margin-top: $value !important;
  }
  .mx-#{$spacer} {
    margin-left: $value !important;
    margin-right: $value !important;
  }
  .m-#{$spacer} {
    margin: $value !important;
  }

  .py-#{$spacer} {
    padding-bottom: $value !important;
    padding-top: $value !important;
  }
  .px-#{$spacer} {
    padding-left: $value !important;
    padding-right: $value !important;
  }
  .p-#{$spacer} {
    padding: $value !important;
  }
}

.rounded { border-radius: 4px !important; }
.rounded-circle { border-radius: 50% !important; }
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.bg-light { background-color: #e9ecef !important; }
.text-muted { color: #6c757d; }

small, .small { font-size: 80% !important; }

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  border: 0;
  padding: 6px 12px;
  border-radius: 4px;
  text-decoration: none;
}

.btn-primary {
  background-color: #0d6efd;
  color: white;
}


/*
 * Custom css
 */

hr {
  border: none;
  border-top: 1px solid #ccc;
}

p {
  font-size: 14px;
  margin: 1em 0;
}

h1 {
  font-size: 35px;
}

h2 {
  font-size: 28px;
}

h3 {
  font-size: 25px;
}

h4 {
  font-size: 21px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 14px;
}

#email_container {
  padding: 20px;
  width: 600px;
}
