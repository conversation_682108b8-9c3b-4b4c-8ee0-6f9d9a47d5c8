.type-select--telecom {
  filter: grayscale(1) opacity(0.5);

  &.data {
    background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/data-service.svg");
  }

  &.voice {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/voice-service.svg");
  }

  &.consolidated {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/voice-service.svg");

    &:after {
      background-image: url("https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/data-service.svg");
    }
  }

  &.primary {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/primary.svg");
  }

  &.secondary {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/secondary.svg");
  }

  .type-selector:hover & {
    filter: opacity(.75);
  }
}

.type-select--telecom.consolidated {
  height: $type-select-height / 1.5;
  margin-bottom: $type-select-height / 3;
  margin-right: $type-select-height / 3;
  position: relative;
  width: $type-select-height / 1.5;
  vertical-align: middle;

  &:after {
    background-size: contain;
    content: "";
    height: $type-select-height / 1.5;
    left: 100%;
    position: absolute;
    top: 100%;
    transform: translate(-50%,-50%);
    width: $type-select-height / 1.5;
  }
}
