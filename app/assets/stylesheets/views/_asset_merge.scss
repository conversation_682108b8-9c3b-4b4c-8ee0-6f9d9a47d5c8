.merge-asset-modal {
  table {
    border-collapse: collapse;
    border-spacing: 0;
    border: 1px solid $themed-very-fair;
  }

  p {
    margin-bottom: 0px;
  }
  
  .merge-asset-header {
    color: #fff;
    background-color: $themed-muted;
    border-color: $themed-muted;
  }
  
  /* Custom labels: the checkcontainer */
  .checkcontainer {
    display: block;
    position: relative;
    padding-left: 35px;
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none; /* Chrome, Opera, Safari */
    -moz-user-select: none; /* Firefox 2+ */
    -ms-user-select: none; /* IE 10+ */
    user-select: none; /* Standard syntax */
  }
  
  /* Hide the browser's default checkbox */
  .checkcontainer input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
  
  /* Create a custom radio button */
  .radiobtn {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: $themed-very-fair;
    border-radius: 50%;
  }
  
  /* On mouse-over, add a grey background color */
  .checkcontainer:hover input ~ .radiobtn {
    background-color: $gray-500;
  }
  
  /* When the radio button is checked, add a blue background */
  .checkcontainer input:checked ~ .radiobtn {
    background-color: transparentize($color-primary, .15);
  }
  
  /* When the radio button is disabled, add a grey background */
  .checkcontainer input:disabled ~ .radiobtn {
    background-color: $themed-fair;
  }
  
  /* Create the indicator (the dot/circle - hidden when not checked) */
  .radiobtn:after {
    content: "";
    position: absolute;
    display: none;
  }

  /* Show the indicator (dot/circle) when checked */
  .checkcontainer input:checked ~ .radiobtn:after {
    display: block;
    top: 7.1px;
    left: 7.1px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: white;
  }
  
  /* Show the indicator (dot/circle) when disabled */
  .checkcontainer input:disabled ~ .radiobtn:after {
    display: block;
    top: 7.1px;
    left: 7.1px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: white;
  }

  .radio-checkbox {
    display: block;
    display: flex;
    align-items: baseline;
    position: relative;
    padding-left: 35px;
    cursor: pointer;
    font-size: 16px;
    user-select: none; 

    &:hover .tickmark:after {
      display: block;
    }

    .tickmark {
      position: absolute;
      top: 0;
      left: 0;
      height: 24px;
      width: 24px;
      border-radius: 2px;
      border: 1px solid white;

      &::after {
        content: "";
        position: absolute;
        display: none;
        left: 9px;
        top: 5px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 3px 3px 0;
        transform: rotate(45deg);
      }
    }

    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;

      &:checked {
        ~ .tickmark:after {
          display: block;
        }
      }
    }
  }

  th, td {
    text-align: left;
  }
  
  label {
    margin-bottom: 0;
  }
  
  .table th.field-name,
  .table td.field-attr {
    min-width: 250px;
  }
  
  .table tr td {
    vertical-align: top !important;
  }


  /* SCSS for mouseover horizontal Scroll for table */ 
  .nulodgicon-chevron-left,
  .nulodgicon-chevron-right {
    display: none;
  }

  .scroltable-wrapper {
    position: relative;
  }

  .scroltable-nav {
    position: absolute;
    z-index: 100 !important;
    width: 5%;
    height: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75em;
    border-radius: 4px;
    color: $themed-muted;
    background: transparent;
  }

  .scroltable-nav.scroltable-nav-left {
    left: -5px;
  }

  .scroltable-nav.scroltable-nav-right {
    right: -5px;
  }

  .scroltable-nav {
    &.scroltable-nav-left:hover{
      background: 10px center, -webkit-linear-gradient(180deg, rgba(255,255,255,0), #fff);
      background: 10px center, -moz-linear-gradient(to left, rgba(255,255,255,0), #fff);
      background: 10px center, -ms-linear-gradient(to left, rgba(255,255,255,0), #fff);
      background: 10px center, -o-linear-gradient(to left, rgba(255,255,255,0), #fff);
      background: 10px center, linear-gradient(to left, rgba(255,255,255,0), #fff);
      .nulodgicon-chevron-left {
        display: block;
      }
    }
    &.scroltable-nav-right:hover {
      background: right 10px center, -webkit-linear-gradient(0, rgba(255,255,255,0), #fff);
      background: right 10px center, -moz-linear-gradient(to right, rgba(255,255,255,0), #fff);
      background: right 10px center, -ms-linear-gradient(to right, rgba(255,255,255,0), #fff);
      background: right 10px center, -o-linear-gradient(to right, rgba(255,255,255,0), #fff);
      background: right 10px center, linear-gradient(to right, rgba(255,255,255,0), #fff);
      .nulodgicon-chevron-right {
        display: block;
      }
    }
  }
}
