$footer-height: 5.25rem;
$box-footer-height: 1.25rem;

.report-data-selection {
  background-color: var(--themed-very-light);
  color: #6c757d;
  font-size: 0.8125rem;
  height: 100%;
  position: relative;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: var(--themed-light);
  }

  &.is-active {
    background-color: rgba(13,110,253,0.1); // 10 percent opacity of primary blue
    box-shadow: 0 0 0 1.5px var(--themed-link);
    color: var(--themed-maastricht-blue);
  }
}

.report-module-checkbox {
  border: 1px solid #adb5bd;
  border-radius: 2px;
  color: var(--themed-lighter);
  display: inline-block;
  font-size: .6875rem;
  height: 1rem;
  line-height: 1;
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
  transition: all .2s ease-in-out;
  transform-origin: right top;
  transform: scale(0.875);
  width: 1rem;

  .report-data-selection:hover & {
    color: var(--themed-light);
  }

  .is-active &,
  .is-active:hover & {
    background: var(--themed-link);
    border-color: var(--themed-link);
    color: white;
  }
}

.text-transparent {
  color: transparent!important;
}

.module-badge {
  background-color: #e5eef5;
  color: #041e42;
  border-radius: 0.75rem;
  padding: 0.5rem;

  img,
  svg {
    height: 24px;
    width: 24px;
  }
}

.module-badge--security {
  background-color: #fef6e2;
  color: #f7c23f;
}

.module-badge--assets {
  background-color: #efeaf8;
  color: #9575CD;
}

.module-badge--helpdesk {
  background-color: #fde5e5;
  color: #EF5350;
}

.module-badge--vendors {
  background-color: #e0f3ea;
  color: #2db273;
}

.module-badge--contracts {
  background-color: #d9f3ff;
  color: #00B0FF;
}

.bg-security-light {
  background: #fef6e2 !important;
}

.bg-assets-light {
  background-color: #efeaf8;
}

.bg-helpdesk-light {
  background-color: #fde5e5;
}

.bg-contracts-light {
  background-color: #d9f3ff;
}

.bg-vendors-light {
  background-color: #e0f3ea;
}

.btn-round.btn-dark:hover {
  background-color: var(--themed-dark-hover);
}

.box--flat-shadow,
.shadow-border-300 {
  box-shadow: 0 0 0 1px var(--themed-very-fair);
}

.module-sub-tabs {
  .sub-menu-item {
    flex: 0 0 auto;
    flex-wrap: nowrap;
  }
}

.it-state__customize-section {
  left: 2.25rem;
  height: auto;
  opacity: 0;
  position: absolute;
  top: 8.65rem;
  transform: translateX(20px);
  transition: all 0.2s ease-in;
  visibility: hidden;
  width: calc(100% - 4.5rem);

  &.position-relative {
    left: 0;
    top: 0;
    width: 100%;
  }

  &.previous-step {
    transform: translateX(-20px);
  }

  &.active-step {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    transition-delay: 0.6s;
    transition-timing-function: ease-out;
  }
}

.it-state__inner-box {
  padding-bottom: $footer-height;
}

.it-state__footer {
  bottom: 2.25rem;
  height: auto;
  left: 2.25rem;
  position: absolute;
  width: calc(100% - 4.5rem);
}

.box--with-switch {
  padding-bottom: 1.5rem;
  padding-top: 1.5rem;
  transition: all 0.2s ease-in-out;
}

.box--with-switch .slide-toggle-wrap {
  position: absolute;
  right: 0.5rem;
  top: 0.25rem;
}

.box--animated-height {
  transition: height 0.2s 0.3s linear;
}

.box--footer-action {
  padding-bottom: $box-footer-height + 1.125rem; // box-footer-height + standard box padding
  position: relative;

  &.box--with-switch {
    padding-bottom: 2.5rem;
    padding-top: 2rem;
  }
}

.box__footer-action {
  background-color: var(--themed-lighter);
  border-radius: 0 0 0.5rem 0.5rem; // 0 0 $border-radius $border-radius;
  border-top: 1px solid var(--themed-light);
  bottom: 0;
  color: var(--themed-muted);
  left: 0;
  letter-spacing: 0.5px;
  line-height: 1;
  font-size: 0.75rem;
  text-align: center;
  transition: all 0.2s ease-in-out;
  padding: 0.25rem;
  position: absolute;
  width: 100%;

  &:hover {
    background-color: var(--themed-light);
  }
}

.preview-report-wrap {
  background-color: $themed-branded-light;
  border-radius: 1rem;
  max-height: calc(100vh - 6rem);
  min-height: 100%;
  overflow-y: scroll;
  padding: 2.25rem;
  padding-top: 2.25rem;
  transform-origin: left top;
  visibility: hidden;

  &.is-changing {
    will-change: transform, visibility;
  }

  &.is-changed {
    visibility: visible;
  }
}

.preview-report {
  border-radius: 0.5rem;
  width: 816px;
}

.page {
  background: $themed-box-bg;
  padding: 3rem; // 0.5 inch page margin
  padding-left: 8rem;
  position: relative;
  width: 816px; // 8.5 inches

  &:first-child {
    margin-top: 0;
  }
}

.page-left-border {
  background-color: #041e42;
  background: linear-gradient(45deg, oklch(23.99% 0.076 257.21) 0%, oklch(33.08% 0.084 257.71) 35%, oklch(41.37% 0.126 264.66) 100%);
  color: white;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 5rem;
}

.page-left-copy {
  bottom: 0;
  left: 50%;
  letter-spacing: 1px;
  position: absolute;
  transform: rotate(-90deg) translate(16px, -50%);
  transform-origin: top left;
  width: 1056px; // equivalent to .page height
}

.page-number {
  bottom: 1rem;
  left: 50%;
  position: absolute;
  text-align: center;
  transform: translateX(-50%);
  width: 100%;
}

.preview-ctas {
  background: #343a40; // Gray 800
  bottom: 0;
  display: none;
  position: fixed;
  width: 100vw;
}

.preview-report-btn {
  position: absolute;
  right: 0.5rem;
  top: 1.5rem;
  z-index: 999;
}

.preview-report-ctas {
  background: #dee2e6; // gray-300
  bottom: 0;
  box-shadow: 0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20); // Shadow-6
  left: 0;
  position: fixed;
  width: 100vw;
  z-index: 999;
}

.preview-report-container {
  max-height: calc(100vh - 6rem);
  top: 4.5rem; // 3rem global header height + 1.5rem top margin
}

.close-report-preview {
  box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.20);
  right: 1.5rem;
  top: 1.5rem;
  z-index: 99;
}

.full-screen-preview {
  .main-page-content {
    z-index: 9999 !important;
  }

  .drawer,
  .site-header,
  .toggle-drawer,
  .intercom-lightweight-app-launcher--placeholder {
    z-index: -1 !important;
    position: absolute !important;
  }

  .preview-report-wrap {
    border-radius: 0;
    bottom: 0;
    height: calc(100vh - 4.375rem);
    left: 0;
    min-height: 0;
    right: 0;
    top: 0;
    transform: none !important;
    position: fixed !important;
    z-index: 99999;
  }

  .full-screen-preview {
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
  }

  .page {
    box-shadow: 0 0 12px -6px rgba(0, 0, 0, 0.14), 0 0 15px -6px rgba(0, 0, 0, 0.14);
  }

  .page-left-border {
    height: 1056px;
  }
}
