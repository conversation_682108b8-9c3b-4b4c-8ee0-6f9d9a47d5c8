.contracts-hierarchy {
  .hirerarchy-item {
    border-left: 4px solid $gray-500;
    max-width: 600px;
    display: block;
  
    &.active {
      border: 1px solid $color-contracts;
      border-left: 4px solid $color-contracts !important;
      background-color: $themed-light !important;
      
      .router-link {
        color: $themed-secondary;
        cursor: default;
        pointer-events: none;
      }
    }
  
    .handle {
      cursor: move !important;
      left: -1.125rem;
      top: 55%;
      letter-spacing: 0px;
    }
  }
  
  .badge-primary {
    background-color: $color-contracts;
  }
  
  .tree .node {
    list-style-type: none;
    margin: 0;
    padding: 10px 5px 0 5px;
    position: relative;

    .node-action {
      transition: all 0.2s ease;
      border-radius: 50%;
      color: $gray-500;
  
      &:hover {
        padding: 6px;
        width: 20px;
        height: 20px;
        background: $secondary;
        color: white;
      }
  
      &.active:hover {
        padding: 6px;
        width: 20px;
        height: 20px;
        background: $color-contracts;
        color: white;
      }
    }
  
    .draggable {
      .chosen .hirerarchy-item {
        background-color: $themed-light !important;
  
        &::before,
        &::after {
          display: block !important;
        }
      }
  
      &.empty-node {
        min-height: 2.25rem;
      }
  
      .drag::before,
      .drag::after {
        display: none !important;
      }
    }
  
    &::before,
    &::after {
      content: "";
      left: -20px;
      position: absolute;
      right: auto;
    }
  
    &::before {
      border-left: 1px solid $gray-500;
      bottom: 50px;
      height: 100%;
      top: 0;
      width: 1px;
    }
  
    &::after {
      border-top: 1px solid #999;
      height: 20px;
      top: 45px;
      width: 25px;
      z-index: -1;
    }
  
    &:last-child::before {
      height: 45px;
    }
  }

  & .tree > .nodes > .draggable > .node {
    margin-left: 0 !important;

    &::before,
    &::after {
      display: none;
    }

    & > .hirerarchy-item *.handle {
      display: none;
    }
  }
}

.open-nested-modal > .sweet-modal {
  width: 100%;
  max-width: 100%;
  height: 100vh;
  overflow: hidden;
  border-radius: 0;
  top: 0;
}
