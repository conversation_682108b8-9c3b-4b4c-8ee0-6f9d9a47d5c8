.drawer-subscription {
  align-items: center;
  border-radius: $border-radius;
  bottom: 1rem;
  display: flex;
  justify-content: center;
  left: 0.5rem;
  min-height: 4rem;
  padding: 0.25rem;
  position: fixed;
  transition: 
    background-color map-get($nav-transition-durations, 'base') map-get($nav-transition-functions, 'base'),
    visibility 0s linear,
    opacity map-get($nav-transition-durations, 'in') map-get($nav-transition-functions, 'in'),
    transform map-get($nav-transition-durations, 'in') map-get($nav-transition-functions, 'in');
  transition-property: opacity, visibility, transform, background-color;
  width: $drawer-width - 1rem;
  z-index: 99;

  &:hover {
    background-color: $blue-oblivion;
  }

  .menu-closed & {
    left: 0.25rem;
    opacity: 0;
    width: $drawer-collapse-width - 0.5rem;
    visibility: hidden;

    &.drawer-subscription--active {
      opacity: 1;
      visibility: visible;
    }
  }

  svg {
    border-radius: 50%;
    height: 2.5rem;
    transform: rotateZ(270deg);
    width: 2.5rem;

    circle {
      animation: (map-get($nav-transition-durations, 'in') + map-get($nav-transition-durations, 'base')) map-get($nav-transition-functions, 'base') 1 progress;
      fill: transparent;
      // stroke: $color-contract;
      stroke: $azure-blue;

      .menu-closed & {
        animation: (map-get($nav-transition-durations, 'out') + map-get($nav-transition-durations, 'base')) map-get($nav-transition-functions, 'base') 1 progress;
      }
    }
  }
}

@keyframes progress {
  0% { stroke-dasharray: 0, 100; }
  100% { stroke-dasharray: 100, 100; }
}

.drawer-subscription-menu-closed {
  align-items: center;
  display: none;
  flex-direction: column;
  font-size: 0.75rem;
  justify-content: center;
  
  .menu-closed & {
    display: flex;
  }

  svg {
    height: 1.85rem;
    width: 1.85rem;
  }
}

.drawer-subscription-menu-open {
  display: flex;
  font-size: 0.875rem;

  .menu-closed & {
    display: none;
  }
}

body:not(.menu-closed) .drawer-subscription-text-wrap--open {
  animation: (map-get($nav-transition-durations, 'in') + map-get($nav-transition-durations, 'base')) map-get($nav-transition-functions, 'base') 1 fade-in-sub-text;
}

body.menu-closed .drawer-subscription-text-wrap--closed {
  animation: (map-get($nav-transition-durations, 'out') + map-get($nav-transition-durations, 'base')) map-get($nav-transition-functions, 'base') 1 fade-in-sub-text;
}

@keyframes fade-in-sub-text {
  0% { opacity: 0; }
  50% { opacity: 0; } // Creates a "delay" that waits for nav and circle to animate
  100% { opacity: 1; }
}

.drawer-subscription-text {
  color: rgba(255,255,255,0.65);
  font-size: 0.625rem;
  display: block;

  .drawer-subscription-menu-open & {
    font-size: 0.875rem;
  }
}

.drawer-subscription-time {
  color: white;
}

@media only screen and (max-height: 46rem) {
  .drawer-subscription {
    background-color: $dark-drawer;
    transform: translate3d($drawer-width + 0.5rem, 0, 0);
    
    &:hover {
      background-color: $blue-oblivion;
    }

    .menu-closed & {
      transform: translateX($drawer-collapse-width + 0.5rem);
      transition: 
        background-color map-get($nav-transition-durations, 'base') map-get($nav-transition-functions, 'base'),
        visibility 0s linear,
        opacity map-get($nav-transition-durations, 'out') map-get($nav-transition-functions, 'out'),
        transform map-get($nav-transition-durations, 'out') map-get($nav-transition-functions, 'out');
    }
  }
}
