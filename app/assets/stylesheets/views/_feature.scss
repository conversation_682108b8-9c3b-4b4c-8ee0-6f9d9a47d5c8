.feature-comments__loading {
  font-size: 1.125rem;
  font-weight: bold;
  padding-top: 1rem;
}

.feature-comments__container {
  visibility: hidden;
}

.feature-comments__container--visible {
  visibility: visible;
}

.feature-attachment-link {
  color: $themed-muted;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 80%;
  white-space: nowrap;
  
  &:hover {
    color: $themed-dark;
  }
}

.feature-requests {
  .box__inner {
    line-height: 1.25rem;
  }

  .box--with-hover {
    padding: 1.25rem;
    
    @media($max: $medium) {
      padding: 0.75rem;
    }
  }

  .dropdown-menu {
    display: block;
    left: auto;
    right: 0;
  }

  .dropdown-toggle {
    margin-top: -0.625rem;
  }

  .delete-btn {
    color: $themed-secondary;
    font-size: 1.125rem;
    
    &:hover {
      color: $themed-dark;
    }
  }
}
