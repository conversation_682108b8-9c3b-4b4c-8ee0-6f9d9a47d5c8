.contract-list img {
  position: absolute;
  width: 1.65rem;
}

.contract-details {
  padding-left: 2.4rem;
  position: inherit;
  min-height: 55px;
}

.module-sub-tabs {
  align-items: flex-end;
  display: flex;
}

.module-users-holder {
  .avatar-holder {
    cursor: pointer;
    justify-content: center !important;
  }
}

.contract-details__meta-data {
  margin-top: -0.5rem;
  line-height: 2rem;
}

.contract-details__name {
  line-height: 1.75rem;
}

.contract-details__value {
  font-size: 0.8rem;
  color: $themed-muted;
}

.lifecycle-progress {
  background: linear-gradient(90deg, $green, $green 48%, $orange 83%, $red);
  border-radius: 0.25rem;
  height: 0.5rem;
  margin: 0 auto;
  margin-bottom: 3rem;
  width: 90%;
  position: relative;
}

.lifecycle-progress__today-marker {
  background: $themed-secondary;
  box-shadow: $shadow-base;
  height: 1rem;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
  transition: left 0.2s ease-in-out;
  width: 1rem;
}

.lifecycle-progress__today-text {
  font-weight: 500;
  left: 50%;
  min-width: 5rem;
  position: absolute;
  top: 100%;
  transform: translate(-50%, 0.25rem);
  transition: left 0.2s ease-in-out;
}

.lifecycle-progress__warning-line {
  border-left: 1px solid $themed-fair;
  height: 0.5rem;
  position: absolute;
  top: -0.5rem;
  transition: left 0.2s ease-in-out;
}

.lifecycle-progress__warning-text {
  left: 50%;
  position: absolute;
  top: -2.5rem;
  transform: translate(-50%, 0.25rem);
  width: 8rem;
  transition: left 0.2s ease-in-out;
}

.contract-attachment iframe {
  height: 100vh;
}

.contract-attachment-list-item__image {
  width: 1.75rem;
}

.contract-attachment-list-item:hover {
  cursor: pointer
}

.remove-uploader {
  width: 2rem;
  line-height: 2rem;
}

.donut-chart-info-contracts {
  padding-left: 3.75rem;

  @include media-breakpoint-up(lg) {
    &:before {
      border-top: 1px solid $gray-500;
      content: "";
      left: -1.75rem;
      position: absolute;
      top: 2.25rem;
      width: 6rem;
    }
  }
}

.contract-name {
  max-width: 12rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contract-name-clamped {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
