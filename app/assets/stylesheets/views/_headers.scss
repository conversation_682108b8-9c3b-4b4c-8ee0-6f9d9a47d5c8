/* Header & Navigation Theme */
/* (Moved the theme-ing to its own section for ease of change) */

// Centered right pin position for Switch Company dropdown
$company-select__spaces-badge-right: 0.5rem;
$company-select__spaces-badge-hover-right: 2rem;
$image-circle-company-right: 0.25rem;
$image-circle-company-top: 0.375rem;
$image-circle-company--left-top: -0.4375rem;

.drawer-overlay {
  background-color: rgba(0,0,0,0.35);
  height: 100vh;
  left: 0;
  opacity: 1;
  position: fixed;
  transform: translateZ(0);
  transition: $nav-transition-in;
  transition-property: opacity;
  top: 0;
  width: 100vw;
  z-index: map-get($zIndex, 'drawer-overlay');

  .menu-closed & {
    opacity: 0;
    transition:
      opacity map-get($nav-transition-durations, out) map-get($nav-transition-functions, out),
      z-index 0s map-get($nav-transition-durations, out) linear;
      z-index: map-get($zIndex, 'behind');
  }

  .hint-drawer-toggle & {
    will-change: opacity, z-index;
  }
}

.image-circle-company {
  background: none;
  cursor: pointer;
  display: none;
  height: 1.4375rem;
  opacity: 1;
  padding: 0.3125rem 0.3125rem;
  position: absolute;
  right: $image-circle-company-right;
  top: $image-circle-company-top;
  transition: $transition-base;  
  width: 1.4375rem;
  z-index: 1;

  &:hover {
    background: $themed-light !important;
  }

  .pinned & {
    display: grid;
  }

  &:active {
    background-color: $themed-very-fair !important;
  }

  &.image-circle-company--left {
    left: 0.3125rem;
    right: unset;
    top: $image-circle-company--left-top;

    .pin-company,
    .other-company {
      transform: none;
    }
  }
}

.select-company-box-wrapper {
  &:hover {
    &.unpinned {
      .image-circle-company {
        display: grid;
      }
    }
  }
}

.pin-company,
.other-company {
  transform: rotate(90deg);
  width: 0.8125rem;
}

.other-company {
  filter: grayscale(1) contrast(0.3);
}

h6[data-tooltip] {
  &:hover:after {
    background: rgba(var(--themed-tooltip-bg-rgb-values), .85);
    border-radius: $border-radius !important;
    color: $themed-white-black;
    content: attr(data-tooltip);
    font-size: 0.6rem;
    padding: 0.5rem 0.7rem;
    position: absolute;
    white-space: nowrap;
    bottom: 2rem;
    left: 0.5rem;
  }
}

.company-tooltip {
  background: rgba(var(--themed-tooltip-bg-rgb-values), .85);
  border-radius: $border-radius !important;
  bottom: calc(100% + 10px);
  color: $themed-white-black;
  display: none;
  font-size: 14px;
  left: -3rem;
  opacity: 0;
  padding: 8px 12px;
  position: absolute;
  transform: translateX(-50%);
  transition: opacity 0.5s, visibility 0.2s;
  white-space: nowrap;

  &.company-tooltip--right {
    left: 4rem;
  }

  &.company-tooltip--left {
    bottom: calc(100% + -1.75rem);
    left: -5rem;
    padding: 0.3125rem .75rem;

    &:before {
      content: '';
      border-color: rgba(var(--themed-tooltip-bg-rgb-values), 0.85);
      border-bottom-color: transparent !important;
      border-right-color: transparent !important;
      border-top-color: transparent !important;
      border-style: solid;
      border-width: 0.3125rem 0 0.3125rem 0.3125rem;
      height: 0;
      margin: 0.3125rem 0;
      position: absolute;
      right: -0.3125rem;
      top: calc(50% - 0.625rem);
      width: 0;
      z-index: 1;
    }
  }
}

.image-circle-company:hover .company-tooltip {
  display: block;
  opacity: 1;
}

.drawer {
  background-color: $themed-dark-drawer-alt-bg;
  left: 0;

  .menu-closed & {
    background-color: $themed-dark-drawer-bg;
  }
}

.drawer-label {
  color: $gray-500;
}

.drawer-link {
  transition: $nav-transition-base;
  transition-property: background-color;

  &:hover {
    background-color: lighten($dark-drawer-alt, 5%);
  }

  &:active {
    background-color: lighten($dark-drawer-alt, 15%);
    transition-duration: map-get($nav-transition-durations, instant);
  }
}

.drawer-link--active {
  &.drawer-link--home {
    background-color: rgba($color-home, 0.3);
  }

  &.drawer-link--assets {
    background-color: rgba($color-assets, 0.3);
  }

  &.drawer-link--contracts {
    background-color: rgba($color-contract, 0.3);
  }

  &.drawer-link--vendors {
    background-color: rgba($color-vendors, 0.3);
  }

  &.drawer-link--telecom {
    background-color: rgba($color-telecom, 0.3);
  }

  &.drawer-link--helpdesk {
    background-color: rgba($color-helpdesk, 0.3);
  }

  &.drawer-link--network {
    background-color: rgba($color-network, 0.3);
  }

  &.drawer-link--more {
    background-color: rgba($gray-500, 0.3);
  }
}

.drawer-link__text {
  color: white;
}

.box-badge--sync-at {
  bottom: 0;
  border-bottom-left-radius: 0.5rem;
}

.nav-dark-bg {
  background-color: $maastricht-blue;
}

.nav-dark-bg-text {
  background-color: $maastricht-blue;
  color: $themed-lighter;
}

.menu-dropdown-item {
  border-color: rgba(255,255,255,0.15) !important;
  color: white;
  transition: border-color map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);

  &:last-child {
    border-bottom: none;
    border-bottom-left-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
  }

  &:hover {
    color: white;
  }

  &:not(.slide-toggle-wrap):hover {
    background-color: $dark-drawer;
  }

  &.slide-toggle-wrap {
    margin: 0 1rem;
    padding: 1rem 0.125rem;
    width: calc(100% - 2rem);
  }
}

.menu-dropdown-item--light {
  background-color: $light;

  &:hover {
    background: $themed-light !important;
  }

  &:active,
  &:visited {
    color: $themed-dark !important;
  }
}

.menu-dropdown-item--small {
  font-size: 0.8125rem !important;
  padding: 0.5rem 0.75rem !important;
}

.drawer-link__small-text {
  color: rgba(255,255,255,0.7);
}


/* Header & Navigation Layout/Functionality */
.site-header {
  background-color: $themed-header-bg;
  box-shadow: $shadow-button;
  height: $header-height;
  left: 0;
  position: fixed;
  padding-left: $drawer-collapse-width;
  transition: background-color map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);
  top: 0;
  z-index: map-get($zIndex, 'header');
}

.closing-survey {
  & .site-header {
    display: none;
  }

  & .site-header--survey {
    background: transparent;
    box-shadow: none;
    display: block !important;
    position: absolute;
    width: 100%;
  }
}
  
.homepage .site-header {
  box-shadow: $shadow-button;
}

.site-header--container {
  background-color: $themed-lighter;
  box-shadow: $shadow-button;
  height: $header-height;
  left: 0;
  position: absolute;
  top: 0;
  transform: translateX(#{$drawer-width});
  transition: $nav-transition-in;
  transition-property: transform;
}

$toggle-icon-width: 1.5rem;

.toggle-drawer {
  backface-visibility: hidden;
  border-radius: 50%;
  cursor: pointer;
  display: inline-block;
  height: $header-height - 0.5rem;
  left: 0;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
  opacity: 0.85;
  position: fixed;
  top: 0;
  transform: translateX(#{$toggle-icon-width * (2/3)});
  transition:
    transform map-get($nav-transition-durations, in) map-get($nav-transition-functions, base),
    opacity map-get($nav-transition-durations, instant) ease-in-out,
    background-color map-get($nav-transition-durations, linger) ease-in-out;
  width: $header-height - 0.5rem;
  z-index: map-get($zIndex, 'drawer-toggle');

  &:hover {
    background-color: rgba(var(--themed-dropdown-hover-bg-rgb-values), 0.1);
    opacity: 1;
    transition:
      transform #{map-get($nav-transition-durations, in)} map-get($nav-transition-functions, base),
      opacity map-get($nav-transition-durations, linger) ease-in-out,
      background-color map-get($nav-transition-durations, linger) ease-in-out;
  }

  &:active {
    background-color: rgba(0,0,0,0.2);
  }

  body:not(.menu-closed) & {
    transform: translateX(#{$drawer-width - 0.25rem - (2 * $toggle-icon-width)}); // -0.25rem accomodates the desired margin from the right of the menu
  }

  .hint-drawer-toggle & {
    will-change: transform, opacity, background-color;
  }
}

.toggle-drawer-icon,
.toggle-drawer-icon:before,
.toggle-drawer-icon:after {
  background-color: $themed-base;
  border-radius: 1.5px;
  height: 3px;
}

.toggle-drawer-icon {
  display: block;
  left: 50%;
  position: relative;
  top: 50%;
  transform: translate(-50%,-50%);
  transition: all map-get($nav-transition-durations, in) map-get($nav-transition-durations, in) map-get($nav-transition-functions, base);
  transition-property: transform, background-color;
  width: $toggle-icon-width;

  &:before,
  &:after {
    content: "";
    position: absolute;
    transform: translateX(0);
    transform-origin: center;
    transition:
      transform map-get($nav-transition-durations, in) map-get($nav-transition-durations, in) map-get($nav-transition-functions, base),
      background-color map-get($nav-transition-durations, in) 0s map-get($nav-transition-functions, base);
    width: #{$toggle-icon-width / 1.2}
  }

  &:before {
    top: -6px;
  }

  &:after {
    bottom: -6px;
  }

  body:not(.menu-closed) & {
    background-color: transparent;
    transform: translate(-50%, -50%);

    &:before,
    &:after {
      background-color: lighten($dark-drawer-alt, 35%);
    }

    &:before {
      transform: rotate(225deg) translate(-7px,-3px);
    }

    &:after {
      transform: rotate(-225deg) translate(-5.5px,2px);
    }
  }

  .hint-drawer-toggle &,
  .hint-drawer-toggle &:before,
  .hint-drawer-toggle &:after {
    will-change: transform, background-color;
  }
}

.icon-toggle {
  background: $color-secondary;
  border-radius: 1.125rem;
  padding: 0.375rem 1.5rem 0.375rem 0;

  i {
    background: $gray-700;
    border-radius: 1.25rem;
    margin-right: 0.5rem;
    padding: 0.5rem 0.625rem;

    &.genuicon-expand:before,
    &.genuicon-sample-company:before {
      position: relative;
      top: 1px;
    }
  }

  &.active {
    background: $blue-oblivion;
    
    i {
      background: $royal-blue;
    }
  }
  
  &.sample-company-toggle,
  &.vertical-theme-toggle {
    i {
      border: 1px solid shift-color($red, -20%);
      top: -1px; // adjust postion to visually accomodate border
    }

    &.active {
      i {
        background: $red;
        border: none;
        top: 0;
      }
    }
  }

  &.vertical-theme-toggle {
    i:before {
      right: 0.0625rem;
      top: 0.1875rem;
    }
  }
}

.nulodgicon-navigate {
  font-size: 0.990rem;
  line-height: 1.3rem;
}

.settings-notification-indicator,
.notification-indicator {
  animation: pulse 2s ease-in-out infinite;
  background-color: $red;
  border-radius: $border-radius;
  height: 0.5rem;
  position: absolute;
  top: .625rem;
  width: 0.5rem;
}

.notification-indicator {
  top: -0.1875rem;
  right: 0.4375rem;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.65;
  }
  50% {
    transform: scale(1.125);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.65;
  }
}

.icon-toggle--slide {
  padding-left: 2.5rem;
  position: relative;
  transition: $transition-smooth;
  transition-duration: 0.4s;

  i {
    left: -0.25rem;
    padding: 0.375rem 0.5625rem;
    position: absolute;
    top: 0;
    transition: $transition-smooth;
  }

  &.active {
    padding-left: 1rem;
    padding-right: 3rem;

    i {
      left: calc(100% - 1.75rem);
    }
  }
}

.drawer {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  transform: translateX(0px);
  transition:
    transform map-get($nav-transition-durations, in) map-get($nav-transition-functions, in),
    background-color map-get($nav-transition-durations, in) map-get($nav-transition-functions, in);
  width: $drawer-width;
  z-index: map-get($zIndex, 'drawer-open');

  .menu-closed & {
    transform: translateX($drawer-collapse-width - $drawer-width);
    transition:
      transform map-get($nav-transition-durations, out) map-get($nav-transition-functions, out),
      background-color map-get($nav-transition-durations, out) map-get($nav-transition-functions, out),
      z-index 0s map-get($nav-transition-durations, out) linear;
      z-index: map-get($zIndex, 'drawer-closed');
  }

  .hint-drawer-toggle & {
    will-change: transform, z-index, background-color;
  }
}

.drawer-wrap {
  height: calc(100vh);
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  transform: translate(0, 2.75rem);
  transition: $nav-transition-in;
  transition-property: transform;
  width: $drawer-width;

  .menu-closed & {
    height: calc(100vh - 3rem);
    transform: translate(#{$drawer-width - $drawer-collapse-width}, 3rem);
    transition:
      transform map-get($nav-transition-durations, out) map-get($nav-transition-functions, out),
      width 0s map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);
    width: $drawer-collapse-width;
  }

  .hint-drawer-toggle & {
    will-change: height, transform, width;
  }
}

.drawer-content {
  height: 100%;
  min-height: 39.25rem;
  overflow-y: hidden;
  transform: translateX(0) scale(1);
  transform-origin: left top;
  transition: $nav-transition-in;
  transition-property: transform;
  width: $drawer-width;

  .marketplace-link & {
    bottom: 0;
    position: absolute;
  }

  .menu-closed & {
    height: 27.5rem;
    min-height: 0;
    transform: translate(-4px) scale(1.5);
    transition:
      transform map-get($nav-transition-durations, out) map-get($nav-transition-functions, out),
      height 0s map-get($nav-transition-durations, out) linear;
  }

  .hint-drawer-toggle & {
    will-change: height, min-height, transform;
  }
}

.drawer-label {
  font-size: 0.875rem;
  margin: 0.5rem 0;
  padding: 0 0.5rem;
}

.drawer-link {
  border-radius: $border-radius;
  display: block;
  font-size: 0.875rem;
  margin: 0.5rem 0;
  padding: 0.25rem 0.175rem;
  position: relative;
  white-space: nowrap;

  .menu-closed & {
    max-width: $drawer-link-collapse-width;
  }

  .hint-drawer-toggle & {
    will-change: max-width;
  }
}

.drawer-link--toggle {
  box-shadow: map-get($shadows, 6);
  border-radius: $border-radius;
  left: ($drawer-collapse-width + 0.25rem);
  opacity: 0;
  overflow: hidden;
  position: fixed;
  transition: $transition-base;
  transition-property: opacity;
  width: 14rem;
  z-index: map-get($zIndex, 'drawer-more');;

  &.closed-menubar {
    .menu-closed & {
      display: block;
    }
  }

  &.is-visible {
    opacity: 1;
  }
}

.drawer-link--toggle-inner {
  background: $dark-drawer-alt;
  box-shadow: map-get($shadows, '4');
  transition: $transition-base;
  width: 100%;
}

.drawer-link--secondary {
  max-width: 100% !important;
  margin: 0.5rem 0.125rem;


  > .drawer-link__icon {
    color: white;
    line-height: 1.3125rem;
    margin-right: 0.5rem;
    opacity: 0.5;
    padding-left: 0.125rem;
  }

  > .drawer-link__image {
    line-height: 1.3125rem;
    margin-left:  0.125rem;
    margin-right: 0.75rem;
    padding: 0.125rem 0 0.125rem 0.125rem;

  }
}

.closed-menubar {
  display: none;

  .menu-closed & {
    display: block;
  }
}

.opened-menubar {
  display: block;

  .menu-closed & {
    display:none;
  }
}

.drawer-link--more {
  display: none;
  cursor: pointer;

  .menu-closed & {
    display: block;
  }
}

.drawer-link__icon {
  height: 1.625rem;
  margin-right: 0.75rem;
  padding: 0.25rem;
  transition: $nav-transition-in;
  transition-property: transform;
  transform: scale(1.125);
  width: ($drawer-link-collapse-width - 0.325rem);

  .menu-closed & {
    transition: $nav-transition-out;
    transition-property: transform;
    transform: scale(1);
  }

  .hint-drawer-toggle & {
    will-change: transform;
  }
}

.drawer-link__icon--assets {
  padding: 0.3125rem;
}

.drawer-link__icon--helpdesk {
  .menu-closed & {
    transform: scale(1.1);
  }
}

.drawer-link__icon,
.drawer-link__text {
  vertical-align: middle;
}

@each $module in $modules {
  .drawer-link--#{$module},
  .drawer-link--#{$module}:hover {
    color: map-get($module-colors, $module);
  }
}

.drawer-link__small-text {
  bottom: 4px;
  font-size: 7px;
  left: 50%;
  opacity: 0;
  position: absolute;
  transform: translateX(-50%);
  transition: $nav-transition-out;
  transition-duration: map-get($nav-transition-durations, instant);
  transition-property: opacity;

  .menu-closed & {
    opacity: 1;
    transition: $nav-transition-in;
    transition-property: opacity;
  }

  .hint-drawer-toggle & {
    will-change: opacity;
  }
}

.company-drawer.right {
  .menu-closed & {
    transform: scale(0.875) translate(10.5rem, 0rem);
    transition: transform map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);
  }
}

.company-drawer,
.workspace-drawer {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0.375rem 0;
  vertical-align: middle;
  width: auto;
}

.workspace-drawer {
  display: none;

  .module--helpdesk &,
  .module--reports & {
    display: inline-block !important;
  }
}

.company-drawer__name,
.workspace-drawer__name {
  color: $themed-base;
}

.company-drawer__logo {
  background-color: white;
  background-position: center;
  background-size: cover;
  border-radius: 50%;
  display: inline-block;
  height: 2.25rem;
  vertical-align: middle;
  width: 2.25rem;
}

.company-drawer__info,
.workspace-drawer__info {
  display: inline-block;
  line-height: 1.25rem;
  vertical-align: middle;
}

.company-switch-logo {
  background-color: white;
  border-radius: 50%;
  height: 2.25rem;
  margin-bottom: -1rem;
  margin-top: -1rem;
  position: relative;
  top: -3px;
  width: 2.25rem;
}

.company-switch-logo--icon {
  border-radius: 0;
  padding: 6px;
}

.managing-company-logo--icon {
  background-color: white;
  border-radius: 50%;
}

.managing-company-container {
  height: 1.8rem;
  width: 1.8rem;
  border-radius: 50%;
  border: 2px solid $themed-branded-light;
  box-shadow: 0px 9px 15px -9px rgba(0, 0, 0, 0.5);
  background: var(--themed-very-light);

  &:hover {
    z-index: 20 !important;
  }

  &.selected-company {
    border-color: #0c66e4;
  }
}

.management-drawer {
  position: relative;
  transition: $nav-transition-in;
  transition-property: transform;
  z-index: map-get($zIndex, 'behind');

  .menu-closed & {
    transform: translateY(-3.375rem);
    transition: $nav-transition-out;
    transition-property: transform;
  }

  .hint-drawer-toggle & {
    will-change: transform;
  }
}

// These elements are readable with a screen reader as long as display and visibility aren't hidden
.hide-on-menu-closed {
  opacity: 1;
  transition: $nav-transition-in;
  transition-property: opacity;

  .menu-closed & {
    opacity: 0;
    pointer-events: none;
    transition: $nav-transition-out;
    transition-duration: map-get($nav-transition-durations, instant);
    transition-property: opacity;
  }

  .hint-drawer-toggle & {
    will-change: opacity;
  }
}

.main-menu-toggle {
  color: $themed-dark;
  cursor: pointer;
  font-weight: 400;
  height: $header-height;
  line-height: $header-height;
  padding: 0 1rem;
  position: relative;
  transition: $transition-base;
  width: auto;

  &:hover {
    color: $themed-base;
  }
}

.site-header-link {
  border-radius: 50%;
  color: $themed-header-link;
  cursor: pointer;
  line-height: $header-height - 0.5rem;
  margin-right: 1rem;
  margin-top: 0.25rem;
  height: $header-height - 0.5rem;
  opacity: 0.85;
  padding: 0;
  text-align: center;
  width: $header-height - 0.5rem;

  &:hover {
    background-color: rgba(var(--themed-dropdown-hover-bg-rgb-values), 0.1);
    color: $themed-base;
    opacity: 1;
  }

  &:active {
    background-color: rgba(var(--themed-dropdown-hover-bg-rgb-values), 0.2);
  }
}

.logo-img {
  height: 3rem;
  padding: 0;
  padding-left: 10px;
  width: auto;
}

.company-menu__logo-image {
  height: 2rem;
}

.company__name {
  line-height: $header-height;
  padding: 0 1rem;
  width: auto;
}

.user__img-avatar,
.user__text-avatar {
  border-radius: 50%;
  color: rgba(255,255,255,0.65);
  display: block;
  font-weight: bold;
  height: 2.25rem;
  line-height: 2.25rem;
  text-align: center;
  width: 2.25rem;
}

.user__text-avatar--admin {
  font-size: 0.625rem;
  margin-top: -0.25rem;
  vertical-align: middle;
}

.user__img-avatar {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

/* menu toggle */
.header-menu {
  background-color: $biscay-blue;
  border-radius: $border-radius;
  box-shadow: map-get($shadows, 6);
  font-size: 0.875rem;
  opacity: 0;
  position: fixed;
  top: $header-height;
  transition: 
    opacity 0.2s ease-in-out,
    background-color map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);
  transform: scale3d(0,0,0);
  transform-origin: left top;
  visibility: hidden;

  &.show {
    opacity: 1;
    transform: scale3d(1,1,1);
    visibility: visible;
    z-index: 100;
  }
}

.select-container {
  &.show {
    display: inherit;
    opacity: 1;
    transform: scale(1);
    transition: opacity 0.2s ease-in-out;
    z-index: 100;
  }
}

.menu-toggle {
  color: $themed-dark;
  cursor: pointer;
  font-weight: 400;
  height: $header-height - 0.5rem;
  line-height: $header-height - 0.5rem;
  margin-top: 0.25rem;
  position: relative;
  transition: $transition-base;
  width: auto;
}

#settings_menu {
  right: 4rem;
  left: inherit;
  width: 16rem;
}

#main_menu {
  right: 1rem;
  left: inherit;
  min-width: 14rem;
}

#app_toggle_menu {
  right: 8rem;
  left: inherit;
  max-width: 12rem;
  min-width: 12rem;
}

#company_menu {
  right: inherit;
  left: 7rem;
  width: 16rem;
}

.menu-dropdown-item {
  align-items: center;
  display: inline-flex;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  width: 100%;

  > i {
    font-size: 1.25rem;
    line-height: 1.3125rem; // 1.5 line-height of 0.875rem parent font size
    opacity: 0.5;
  }
}

.menu-dropdown-item--indented {
  padding-left: 2rem;
}

.menu-disabled {
  opacity: 0.3;
}

.grecaptcha-badge {
  z-index: 9;
}

.select-links {
  max-height: 250px;
  overflow-y: scroll;
  height: 15rem;
}

.company-select, 
.workspace-link {
  position: relative;

  &:hover {
    background-color: $themed-lighter;
    color: $themed-base;

    .make-defult-link {
      background-color:  #e4e4e4;
    }

    .image-circle-company {
      background-color: $themed-box-bg;
      border-radius: 50% !important;
      box-shadow: map-get($shadows, 1);
      display: grid;
    }

    .company-select__spaces-badge {
      right: $company-select__spaces-badge-hover-right;
    }
  }
}

.workspace-link {
  padding: 0 0 0 0.5rem;

  &:hover {
    background-color: $themed-lighter;
    color: $themed-base;
    .make-defult-link {
      background-color:  #e4e4e4;
      &.default {
        background-color: $gray-500;
      } 
    }
  }
}

.company-select__spaces-badge {
  background-color: $themed-light;
  border-radius: 1rem;
  color: $secondary;
  display: none;
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  position: absolute;
  right: $company-select__spaces-badge-right;
  top: 50%;
  transform: translateY(-50%);
  transition: $transition-base;

  &:after {
    content: attr(data-workspaces-count);
    display: inline-block;
  }

  .pinned & {
    right: $company-select__spaces-badge-hover-right;
  }
}

.module--helpdesk,
.module--reports {
  .company-select__spaces-badge {
    display: block;
  }
}

$company-select-top-offset: $header-height + 0.25rem;
$company-select-bottom-offset: 1.5rem;
$company-select-max-height: 40rem;
$company-select-static-content-height: 11.625rem;
$company-menu-responsive-left-offset-md: 2.5rem;
$company-menu-responsive-left-offset-sm: 1.25rem;
$company-menu-responsive-width: 90%;

.company-select-menu,
.workspace-select-menu,
.managing-company-select-menu,
.workspace-settings-menu {
  background: $themed-box-bg;
  box-shadow: map-get($shadows, 6);
  display: block;
  height: auto;
  left: $drawer-collapse-width + 1.5rem;
  max-height: calc(100vh - #{$company-select-top-offset + $company-select-bottom-offset }); // Include 1.5rem space on bottom
  overflow-y: scroll;
  top: $company-select-top-offset;
  width: 30rem;
  z-index: 100;

  @media($max: map-get($grid-breakpoints, md)) {
    left: $company-menu-responsive-left-offset-md; // Add minimal offset when window is small
    width: calc(#{$company-menu-responsive-width} - #{$company-menu-responsive-left-offset-md});
  }

  @media($max: map-get($grid-breakpoints, sm)) {
    left: $company-menu-responsive-left-offset-sm;
    width: calc(#{$company-menu-responsive-width} - #{$company-menu-responsive-left-offset-sm});
  }
}

.managing-company-select-menu {
  left: $drawer-collapse-width + 1.5rem + 6rem;
}

.workspace-select-menu {
  left: $drawer-collapse-width + 1.5rem + 8rem; // Add 8rem to differentiate from the company selection dropdown's location
  
  @media($max: map-get($grid-breakpoints, md)) {
    left: $company-menu-responsive-left-offset-md * 2; // Add minimal extra for WS menu offset when window is small
    width: calc(#{$company-menu-responsive-width} - #{$company-menu-responsive-left-offset-md * 2});
  }

  @media($max: map-get($grid-breakpoints, sm)) {
    left: $company-menu-responsive-left-offset-sm;
    width: calc(#{$company-menu-responsive-width} - #{$company-menu-responsive-left-offset-sm});
  }
}

.workspace-settings-menu {
  border-radius: $border-radius-sm;
  left: unset;
  position: absolute;
  right: 0.375rem;
  top: 1.75rem;
  width: fit-content;

  .menu-dropdown-item {
    display: block;
  }
}

.company-select__icon,
.workspace-select__icon {
  filter: var(--themed-light-icon-filter);
  opacity: 0.35;
}

.form-select__icon {
  filter: grayscale(100%);
  opacity: 0.65;
}

.selected-company-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.company-selection-wrap,
.workspace-selection-wrap {
  max-height: calc(100vh - #{$company-select-top-offset + $company-select-bottom-offset + $company-select-static-content-height});
  overflow-x: hidden;
  overflow-y: auto;
}

.company-selection-header,
.workspace-selection-header,
.form-selection-header {
  letter-spacing: 0.5px;
  border-bottom: 1px solid $themed-fair;
  padding-bottom: 0.25rem;
}

.company-select--selected,
.workspace-link--selected {
  background-color: $themed-light;
  position: relative;
}

.company-select--active,
.workspace-link--active.workspace-link--selected  {
  &:before {
    color: $success;
    content: "\e031";
    font-family: "genuicon";
    position: absolute;
    left: 2.5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }
}

.workspace-link--active.workspace-link--selected  {
  &:before {
    left: .625rem;
  }
}

.workspace-link--active {
  &:after {
    color: $themed-dark;
    content: "Default";
    position: absolute;
    right: 0.9375rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    background: $gray-200;
    padding: 0.1875rem 0.3125rem;
    border-radius: 0.25rem;
    font-size: 0.8125rem;
  }

  &:hover {
    .make-defult-link.active {
      background-color: $gray-500;
    }
  }
}

.workspace-link--active.workspace-link--selected {
  &:after {
    display: none;
  }
}

.company-select--active .company-select__name {
  padding-left: 1.25rem;
}

.workspace-link--active.workspace-link--selected  {
  padding-left: 1.75rem;

  &:before {
    left: 0.5rem;
  }
}

.workspace-dropdown {
  margin-bottom: 5px;
  background-color: $themed-lighter;
  box-shadow: map-get($shadows, 2);
  margin-top: -0.25rem;
  z-index: 200;
}

.company-select--add,
.workspace-link--add {
  letter-spacing: 0.5px;
  opacity: 0.65;
  transition: $transition-base;

  &:hover {
    opacity: 1;
  }
}

.workspaces-info-modal-wrap {
  align-items: center;
  background-color: rgba(0,0,0,0.35);
  bottom: 0;
  content: "";
  display: flex;
  justify-content: center;
  left: 0;
  overflow: scroll;
  position: fixed;
  right: 0;
  top: 0;
  z-index: map-get($zIndex, 'modal');
}

.workspaces-info-modal {
  border-radius: $border-radius-lg;
  box-shadow: $shadow-top;
  margin: auto;
  position: relative;
}

.close-workspace-info-modal {
  color: $themed-base;
  cursor: pointer;
  font-size: 1.5rem;
  font-weight: bold;
  opacity: 0.5;
  position: absolute;
  right: 1rem;
  top: 0.5rem;

  &:hover {
    opacity: 1;
  }
}

.workspace-settings-link {
  color: $gray-500;
  display: none;
  padding: 0.5rem;

  &.active,
  &:hover {
    color: $themed-muted;
  }

  &.active,
  .workspace-link:hover & {
    display: block;
  }
}

.workspace-link-name {
  padding: 0.5rem 0;
}

.workspace-default-label  {
  background-color: $themed-light;
  border-radius: $border-radius-lg;
  color: $themed-secondary;
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  transition: $transition-base;

  .workspace-link:hover & {
    background-color: $themed-very-fair;
    margin-right: 1rem;
  }

  .workspace-link--active & {
    background-color: $themed-very-fair;
  }
}

.workspace-link:has(.workspace-settings-link.active) {
  .workspace-default-label {
    margin-right: 1rem;
  }
}

.workspace-name-icon-bg {
  border-radius: 8px !important;
  content: " ";
  height: calc(100% + 4px);
  left: 2px;
  position: absolute;
  opacity: .15;
  top: -2px;
  width: calc(100% + 4px);
}

div#company_select_menu,
div#managing_company_select_menu {
  position: fixed;
  overflow-y: auto;
  max-height: 100vh;
}

@media only screen and (min-height: #{$company-select-top-offset + $company-select-max-height + $company-select-bottom-offset}) {
  .company-select-menu,
  .managing-company-select-menu,
  .workspace-select-menu {
    max-height: $company-select-max-height;
  }

  .company-selection-wrap,
  .workspace-selection-wrap {
    max-height: $company-select-max-height - $company-select-top-offset - $company-select-bottom-offset - $company-select-static-content-height;
  }
}

.make-defult-link {
  position: absolute;
  right: 0;
  top: 0;
  padding: 0.5rem;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  width: 6rem;
  height: 2.4rem;
  text-align: center;
  &.active,
  &:hover {
    background-color: $gray-500;
    &:hover {
      background-color: $gray-500;
    }
  }
}

@-moz-document url-prefix() {
  .vue-tooltip-theme {
    transition: none !important;
  }
}

.management-toggle_box {
  border-radius: 1rem;
  border: 1px solid $themed-very-fair;
  min-width: 13.75rem;
  
  @media only screen and (max-width: 600px){
    display: none !important;
  }
}

.view-toggle-btn {
  height: 23px;
  border-radius: 20rem;
  padding: 2px 5px;
  cursor: pointer;
  color: gray;
}

.active-view {
  background-color: $maastricht-blue;
  background: linear-gradient(-45deg, $de-france-blue, $blue-oblivion 55%, $maastricht-blue);
  color: white;
}

.setting-alert {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  border-left: 0.3rem solid $color-caution;
  background: $themed-light;
}
.info-icon {
  color: $color-caution;
  margin-right: 0.3rem;
}

.company-name-tooltip {
  position: absolute;
  background: rgba(var(--themed-tooltip-bg-rgb-values), .85);
  color: $themed-white-black;
  padding: 0.5rem 0.75rem;
  border-radius: $border-radius !important;
  font-size: 0.8rem;
  max-width: 23.5rem;
  left: 21.25rem;
  white-space: normal;
  word-wrap: break-word;
  opacity: 0;
  transform: translateX(-50%);
  transition: opacity 0.5s, visibility 0.2s;
  pointer-events: none;
  z-index: 122;
}
