.rating-style {
  font-size: x-large;
  color: lightgrey;
  cursor: pointer;
  line-height: 1;

  &:hover {
    color: lightgrey;
  }
}

.selected-rating {
  color: gold;
}

.short-text {
  max-height: 5rem;
  min-height: 5rem;
}

.choice-text {
  font-weight: 400;
  font-size: large;
}

.selected-choice {
  background-color: #b1e2cb;
  border: 0.063rem solid;
  border-color: #75b798;
}

.cover-image {
  height: 100%;
  left: 0;
  mix-blend-mode: luminosity;
  object-fit: contain;
  position: absolute;
  top: 0;
  width: 100%;
}

.cover-image--waves {
  height: 100%;
  width: 100%;
  mix-blend-mode: overlay;
  top: 1.5rem;
}

.response-preview {
  position: absolute;
  width: 61rem;
  height: 32rem;
  left: 28%;
  top: 19%;
  transform: translate(-8%, 0%);
  z-index: 10;
}

.emoji-size {
  font-size: 1.3rem;
}

.choice-box {
  padding: .75rem 1rem !important;

  .inline-survey-questions & {
   padding: .5rem .75rem !important
  }

  &:hover {
    background-color: $green-light;
  }
}

.custom-survey-wrapper {
  margin-top: -8rem;
}
