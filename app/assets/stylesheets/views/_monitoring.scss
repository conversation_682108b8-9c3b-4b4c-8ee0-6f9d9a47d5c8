select.selectpicker {
  display: block;
  margin: 0 auto;
  padding-left: 20px;
}

.history-graph-btn{
  line-height: 0.7rem;
  font-size: small;
}

.sensor-detail-list {
  width: stretch;
}

.sensor-status {
  font-size: 0.8rem;
}
.sensor-detail {
  width: 100%; /* Configure it fit in your design  */
  margin: 0 auto; /* Centering Stuff */
  background-color: $themed-box-bg; /* Default background */
  border: 1px solid $themed-light;
  border-left-width: 5px;
  border-radius: 3px;
  margin: 0 auto;
  font-size: 1rem;

  &:hover {
    cursor: pointer;
    box-shadow: 0 2px 5px -2px rgba(0, 0, 0, 0.25);
    border-left: 5px solid $orange;
    background: $themed-box-bg;
  }
}

.sensor-detail-color {
  border-left-color: $color-accent; /* Left side border color */
  background-color: rgba($color-accent, 1); /* Same color as the left border with reduced alpha to 0.1 */
}

.sensor-detail-color strong {
  color: rgba(0, 0, 0, 0.5);
}

.monitoring-heading-text{
  position: inherit;
  margin-left: 2rem;
  color: rgba(0, 0, 0, 0.5);
  .v-spinner {
    position: absolute;
    right: 1.5rem;
    top: 0;
    margin: 0;
  }
}

.item-text {
  font-size: 1.15rem;
}

.sensor-details-section {
  .item-holder {
    font-size: 0.9rem;
  }
}

.sensor-detail-list {
  .sensor-detail{
    border-radius: 0;
  }
}

.truncate-holder {
  width: 91%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

.data-graphs-holder {
  #line-chart {
    height: 350px !important;
  }
}
