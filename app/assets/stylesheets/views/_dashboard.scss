.bus-stat {
  background-position: 0.5rem 0.5rem;
  background-repeat: no-repeat;
  background-size: 3rem;
  display: block;
  margin: 1.5rem 0.75rem;
  transition: $transition-base;
  padding-left: 5rem;

  &:hover {
    text-decoration: none;
  }

  &:hover > div {
    text-decoration: underline;
  }

  @include media-breakpoint-down(md) {
    background-size: 3rem;
    background-position: left center;
  }
}

.bus-stat--contracts {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-contracts.svg");
}

.bus-stat--staff {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-staff.svg");
}

.bus-stat--assets {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-assets.svg");
}

.bus-stat--help {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-helpdesk.svg");
}

.gamification-box {
  min-height: 192px;
  padding-left: calc(3.5rem + 192px);
}

.gamification-box__img {
  left: 1.5rem;
  min-width: 192px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.box.bg-help:hover {
  background: $color-help !important; // Override bootstrap default in this rare case
}

.warranty-dot {
  border-radius: 50%;
  display: inline-block;
  height: 0.5rem;
  margin-right: 0.5rem;
  width: 0.5rem;
}

.warranty-dot--safe {
  background-color: $color-safe;
}

.warranty-dot--expiring {
  background-color: $color-expiring;
}

.warranty-dot--expired {
  background-color: $color-expired;
}

.spending-loading-bg {
  background-image: url('https://nulodgic-static-assets.s3.amazonaws.com/images/services_summary_bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
