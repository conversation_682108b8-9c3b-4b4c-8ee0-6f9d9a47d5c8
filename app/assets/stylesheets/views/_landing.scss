$session-image-height: 20rem;

.sessions-row {
  min-height: calc(100vh - 3rem);
}

.sessions-bg-wrap {
  background-color: $maastricht-blue;
  height: $session-image-height;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}

.sessions-bg {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  transition: $transition-base;
  top: 0;
  width: 100%;

  &.is-loaded {
    opacity: 1;
  }
}

.sessions-fg-wrap {
  height: $session-image-height;
}

.sessions-fg {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
  opacity: 0;
  transition: $transition-base;

  &.is-loaded {
    opacity: 1;
  }
}

.sessions-content {
  margin: 0 auto;
  max-width: 32rem;
}

@include media-breakpoint-up(lg) {
  .sessions-bg-wrap {
    left: 26rem;
    height: 100%;
    min-height: calc(100vh - 3rem);
    width: calc(100% - 26rem);
  }

  .sessions-fg-wrap {
    height: calc(100vh - 3rem);
    order: 2;
  }

  .sessions-fg {
    background-size: contain;
  }

  .sessions-content {
    max-width: 26rem;
    order: 1;
  }
}

@include media-breakpoint-up(xl) {
  .sessions-bg-wrap {
    left: 32rem;
    width: calc(100% - 32rem);
  }

  .sessions-content {
    max-width: 32rem;
  }
}

.sessions-help-text {
  bottom: 0;
  left: 0;
  padding: 0 3rem;
  position: absolute;
  width: 100%;
}

.show-sign-in {
  background-color: white;

  .register & {
    background-color: $themed-light;
  }
}

.show-register {
  background-color: $themed-light;

  .register & {
    background-color: white;
  }
}

.show-password {
  color: $themed-secondary;
  cursor: pointer;
  display: none;
  padding: 0.25rem 0.5rem;
  position: absolute;
  right: 3px;
  top: 50%;
  transform: translateY(-50%);
  transition: $transition-base;
  z-index: 99;

  &:hover {
    color: $themed-base;
  }

  &.js-visible {
    display: block;
  }
}
