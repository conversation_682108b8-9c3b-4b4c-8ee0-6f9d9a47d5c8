.password-modal {
  box-sizing: border-box;
  display: none;
  position: fixed;
  z-index: 1;
  padding-top: 100px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;

  &::before {
    content: "";
    position: fixed;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: map-get($shadows, 'top');
    transform: scale(1);
    transition: all 0.3s ease-in-out;
  }
  
  .modal-content {
    background-color: $themed-lighter;
    margin: auto;
    padding: 20px;
    border-radius: $border-radius-lg;
    box-shadow: map-get($shadows, 'top');
    width: 35%;
    text-align: center;

    .session-timeout-clock, .network-error-icon {
      font-size: 100px;
      color: $color-danger;
    }

    #password-modal-closeButton {
      display: inline-block;
      cursor: pointer;
      color: #222C38;
      text-align: center;
      width: 42px;
      height: 42px;
      line-height: 42px;
      border-radius: 50%;
    }

    #password-modal-closeButton:hover {
      background: #038bcf;
      color: #fff;
    }
  }

  .box {
    background-color: $themed-lighter;
    box-shadow: none;
    margin: 0;
    padding: 0 5px 0;
  }

  .title {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    height: 35px;
    line-height: 50px;
    padding-left: 10px;
  }
  
  .title > h2 {
    float: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin: 0;
    padding: 0;
    font-weight: 500;
    font-size: 22px;
  }

  .description {
    padding-left: 10px;
    color: #6c757d !important;
    text-align: left;
    margin: 0;
    margin-bottom: 0;
    font-size: 12px;
  }

  .form-text {
    text-align: left;
  }

  label {
    float: left;
  }

  input.btn.btn-primary {
    float: right;
  }

  input.btn.btn-primary:hover {
    color: #fff;
    background-color: #025ce2;
    border-color: #0257d5;
  }
}
