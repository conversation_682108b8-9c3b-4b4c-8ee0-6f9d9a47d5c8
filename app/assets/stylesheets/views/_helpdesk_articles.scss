.article-title {
  max-width: 37.5rem;
}

.truncate-body {
  max-height: 2.5rem;
  overflow: hidden;
}

.tags--border {
  border-radius: 0.25rem;
}

.article-actions-menu {
  top: 1.25rem;
  left: -6.25rem;
}

.article-date {
  bottom: 0.375rem;
  left: unset;
  padding-right: 0.625rem;
  right: 0;
}

.category-text {
  line-height: normal;
}

.article-description {
  img {
    height: auto;
    width: 100%;
  }
  
  /deep/ {
    ul {
      list-style-type: disc;
      padding-left: 0.938rem;
    }
  }
}

.tags--border {
  border-radius: 0.25rem;
}

.faq-preview {
  img {
    max-height: 10rem;
    width: auto;
  }

  .attachment__name,
  .attachment__size {
    display: none;
  }
}
