.center-align {
  display: flex;
  align-items: center;
}

.un-assign__item {
  color: $themed-muted;
  display: inline-block;
  position: absolute;
  top: 4px;
  right: 8px;
  font-size: 0.75rem;
  font-style: normal;
  &:hover {
    color: $red;
  }
}

.transaction-list-item,
.sync-account-item,
.vendor-list-item,
.vendor-notification-item {
  transition: $transition-base;

  &:hover {
    background-color: $themed-lighter;
  }
}
.short-name {
  max-width: 10rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-badge {
  color: white;
  display: inline-block;
  font-size: 11px;
  font-weight: 700;
  height: 16px;
  line-height: 8px;
  padding: 4px 6px;
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-left-radius: 5px;

  &.success {
    background: $pastel-green-100;
  }

  &.danger {
    background: $color-danger;
  }
}

.licensed-apps,
.metered-apps {
  padding-left: 1.5rem;
  position: relative;

  &:before,
  &:after {
    content: "";
    left: 0;
    position: absolute;
  }

  &:before {
    border-radius: 0.5rem 0 0 0.5rem;
    background-color: rgba($green, 0.25);
    height: 100%;
    width: 1.5rem;
    top: 0;
  }

  &:after {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/licensed-app.svg");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    filter: var(--themed-badge-icon-filter);
    height: 1.5rem;
    top: 50%;
    transform: rotate(-90deg) translateX(50%); // Translating the X for height centering because of the rotation
    width: 1.5rem;
  }

  th:first-child,
  td:first-child {
    padding-left: 1rem;
  }
}

.metered-apps {
  &:before {
    background-color: rgba($cyan, 0.25);
  }

  &:after {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/metered-app.svg");
  }
}

.module--vendors {
  .module-sub-tabs .sub-menu-item .sub-menu-item__icon {
    display: none;
  }

  @media screen and (min-width: 1150px) {
    .module-sub-tabs .sub-menu-item .sub-menu-item__icon {
      display: inline-block;
    }
  }
}
