.avatar-holder {
  display: flex;
  justify-content: center;
  position: relative; // Give z-indexes a standalone context

  .avatar-img {
    margin-bottom: 0;
    margin-top: -3px;

    &:nth-child(1){
      z-index: 3;
    }

    &:nth-child(2){
      z-index: 2;
      margin-left: -14px;
    }

    &:nth-child(3){
      z-index: 1;
      margin-left: -14px;
    }

    .vue-avatar--wrapper {
      box-shadow: $shadow-base;
      border: 2px solid #fff;
      font-family: $font-family-sans-serif !important;
    }
  }

  .icon-holder {
    margin: 0 auto;
    border-radius: 50%;
    box-shadow: $shadow-base;
    border: 4px solid $themed-light;
    background: $themed-light;
  }
}

.user-avatar-holder {
  .vue-avatar--wrapper,
  .avatar-image,
  img {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 50%;
    box-shadow: $shadow-base;
    border: 3px solid #fff;
  }
}

.reward-header {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/reward_background.jpg");
}
