.mfa-outer {
  max-width: 477px;
  border: 1px solid $themed-very-fair;
  margin: 35px auto 0;
  text-align: center;
  padding: 2.5rem 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

  .mfa-back {
    margin-top: -17px;
    display: block;
    text-align: left;

    &-modal {
      margin-left: 24px;
    }
  }

  .form-group {
    max-width: 380px;
    margin: auto;
    position: relative;

    .label-align-left {
      width: 100%;
      text-align: left;
      font-size: 14px;
      margin-bottom: 5px;
    }

    .change-device {
      position: absolute;
      right: 4px;
      top: 4px;
    }

    .btn {
      width: 100%;
      padding: 10px;
    }
  }

  .mfa-block {
    display: inline-block;
    margin: 0 auto;
    padding: 3px;

    svg {
      width: 60px;
      height: 60px;
      color: $color-success;
    }
  }

  .sub-text {
    font-size: 14px;
    line-height: 18px;
    max-width: 378px;
    margin: 0 auto;
  }

  .verification-text {
    display: flex;
    align-items: center;
    justify-content: center;

    a {
      display: inline-flex;
      align-items: center;
      margin: 0 5px;

      .genuicon-refresh {
        margin-right: 2px;
        margin-top: 3px;
      }
    }
  }

  .vue-phone-number-input {
    .field-label {
      top: -18px !important;
      left: 10px !important;
      color: $themed-muted !important;
      font-family: $font-family-sans-serif;
      font-weight: normal;
    }
    
    input {
      padding-top: 0;
    }

    .field-country-flag {
      top: 15px !important;
    }

    .select-country-container {
      .country-selector-arrow {
        right: 5px;
        top: 10px;
      }
      input {
        border-right: 0 !important;
      }
    }
  }

  .verification-input-section {
    margin:5px 0 10px;
    position: relative;

    &::before {
      content: '-';
      position: absolute;
      display: block;
      top: 0px;
      right: 0;
      font-size: 35px;
      width: 27px;
      height: 45px;
      left: 0;
      margin: 0 auto;
      color: transparentize($gray-600, .34);
    }
  }

  .nulodgicon-email {
    &:before {
      font-size: 20px;
      top: 0.3em;
    }
  }

  .resend-code {
    display: block;

    &.or-style::before,
    &.or-style::after {
      display: inline-block;
      content: "";
      border-top: .1rem solid $themed-fair;
      width: 3.5rem;
      margin: 0 1rem;
    }

    &.bottom {
      margin-top: -3px;
    }
  }
}


// Revamped Design for MFA — Security tab
.heading-section {
  padding: 2rem 0rem;
}

.outer {
  max-width: 715px;

  .section-text-list {
    .section-heading {
      margin-bottom: 0px;
      line-height: 14px;
    }
    .sub-text {
      font-size: 13px;
      text-align: left;
    }
  }

  .radio-holder {
    .radio-box {
      margin-right: 10px;
      display: flex;
      align-items: center;

      input {
        margin-right: 0.25rem;
      }

      label {
        margin: 0px;
        font-size: 14px;
      }
    }
  }

  .btn.btn-primary {
    min-width: 80px;
  }

  .time-input {
    width: 35px;
    padding: 3px 1px 1px;
    border-width: 1px;
    border: 1px solid $gray-500;

    &:focus {
      outline: none;
      border: 1px solid $themed-secondary;
    }
  }
  
  .toggle-margin {
    margin-right: 10rem !important;
  }
}
