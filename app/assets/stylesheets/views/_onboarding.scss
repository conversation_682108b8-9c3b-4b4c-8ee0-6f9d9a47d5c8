.onboard-dots {
  bottom: 1.5rem;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}

.onboard-dot {
  background-color: $gray-500;
  border-radius: 50%;
  cursor: pointer;
  float: left;
  height: 7px;
  margin-right: 5px;
  transition: background-color 0.1s ease-in-out;
  width: 7px;
}

.onboard-active-dot {
  background-color: $primary;
  border-radius: 50%;
  height: 7px;
  position: absolute;
  top: 0;
  transition: all 0.1s ease-in-out;
  width: 7px;

  @for $i from 1 through 5 {
    &[data-active-slide="#{$i}"] {
      transform: translateX(#{($i - 1) * 12}px);
    }
  }
}

.onboard-btn {
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  bottom: 1rem;
  position: absolute;
}

.onboard-btn--prev {
  left: 1rem;
}

.onboard-btn--next {
  right: 1rem;
}

.onboard-slides {
  border-radius: $border-radius-lg;
  height: 100vh;
  margin-left: auto;
  margin-right: auto;
  max-height: 34rem;
  overflow: hidden;
  position: relative;
}

.onboard-slide {
  left: 0;
  position: absolute;
  top: 0;
  transform: translateX(100%);
  transition: all 0.2s ease-in-out;
  will-change: transform;
  width: 100%;

  &.is-prev {
    transform: translateX(-100%);
  }

  &.is-next {
    transform: translateX(100%);
  }

  &.is-active {
    transform: translateX(0);
  }

  &[data-penultimate-slide].is-prev {
    opacity: 0;
    transform: translateX(0);
  }

  &[data-last-slide] {
    opacity: 0;
    transition-property: opacity;
    transition-delay: 0.4s;

    &.is-active {
      opacity: 1;
    }
  }
}

.onboard-text {
  max-width: 25rem;
  opacity: 0;
  transition: all 0.2s 0.2s ease-in-out;

  .is-active & {
    opacity: 1;
  }
}

.onboard-text--full-width {
  max-width: none;
}

.banner-background {
  background: $maastricht-blue;
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/onboarding/onboarding-background.png");
  background-position: left center;
  background-repeat: no-repeat;
  background-size: cover;
}

.dot,
.onboard-dot--gray {
  cursor: pointer;
  height: 0.5rem;
  width: 0.5rem;
  margin: 0 0.5rem 2px;
  background-color: white;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.6s ease;

  &.active,
  &:hover {
    background-color: $primary;
  }
}

.onboard-dot--gray {
  background-color: $gray-500;
}

$swap-timing: 0.4s;
$swap-timing-fast: 0.1s;
$swap-delays: (
  1: 0.5s,
  2: 1s,
  3: 0.35s
);

$swap-timing--delayed: 0.88s;

$swap-motion: cubic-bezier(0.4, 0.2, 0.2,1);
$swap-in-motion: cubic-bezier(0.0, 0.3, 0.2, 1);
$swap-out-motion: cubic-bezier(0.65, 0.3, 0.2,1);

.main-section--one,
.main-section--two {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100vh;
  left: 0;
  padding-bottom: 2rem;
  position: absolute;
  top: 0;
  width: 60%;
}

.main-section--two {
  left: auto;
  right: 0;
}

.main-section--one,
.main-section--two,
.secondary-section--one,
.secondary-section--two
.secondary-section-wrap {
  // Improved transition performance
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);
}

.secondary-section-wrap {
  background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/platform-curve.svg");
  background-color: #041e42 !important;
  background-position: 0 2px;
  background-size: cover;
  height: 100%;
  display: flex;
  min-height: 100vh;
  position: absolute;
  right: 0;
  top: 0;
  transition: transform $swap-timing map-get($swap-delays, 1) ease-in-out;
  width: 40%;
  will-change: transform;

  &.is-step-two {
    transform: translateX(-150%);
    transform: translate3d(-150%,0,0);
  }
}

.secondary-section--one,
.secondary-section--two {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  min-width: 100%;
  margin-top: -38%;
  width: 100%;
}

.secondary-section--one {
  flex-direction: column;
  margin-top: -28%;
}

.swap-fade-enter-active {
  transition: opacity $swap-timing map-get($swap-delays, 2) $swap-in-motion;
  will-change: opacity;
}

.swap-fade-leave-active {
  transition: opacity $swap-timing $swap-out-motion;
  will-change: opacity;
}

.swap-fade-enter,
.swap-fade-leave-to,
.swap-fast-fade-enter,
.swap-fast-fade-leave-to {
  opacity: 0;
}

.swap-fast-fade-enter-active {
  transition: opacity $swap-timing-fast map-get($swap-delays, 3) $swap-in-motion;
  will-change: opacity;
}

.swap-fast-fade-leave-active {
  transition: opacity $swap-timing-fast $swap-out-motion;
  will-change: opacity;
}

.simple-fade-enter,
.simple-fade-leave-to {
  opacity: 0;
}

.simple-fade-enter-active {
  transition: opacity $swap-timing map-get($swap-delays, 1) $swap-in-motion;
  will-change: opacity;
}

.simple-fade-leave-active {
  transition: opacity $swap-timing $swap-out-motion;
  will-change: opacity;
}

@media only screen and (max-width: 1040px) {
  .secondary-section-wrap,
  .secondary-section--one,
  .secondary-section--two,
  .bottom-line {
    display: none !important;
  }

  .main-section--one,
  .main-section--two {
    width: 100%;
    will-change: opacity, transform;

    &.swap-fade-enter-active {
      transition: all $swap-timing map-get($swap-delays, 1) $swap-in-motion;
      transition-property: opacity, transform;
    }

    &.swap-fade-leave-active {
      transition: all $swap-timing $swap-out-motion;
      transition-property: opacity, transform;
    }

    &.swap-fade-leave-to {
      opacity: 0;
      transform: translateX(-5%);
    }
  }

  .main-section--two .single-column-form-wrap {
    padding-bottom: 6rem !important;
  }
}

@media only screen and (max-width: 448px) {
  .onboarding-wrap {
    min-height: 52rem;
  }
}

.center-it {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
}

@mixin line-styling {
  bottom: 0;
  height: 6px;
  position: fixed;
  width: 60%;
  z-index: 41;

  &:before {
    background-color: transparent;
    bottom: 6px;
    content: "";
    position: absolute;
    height: 3rem;
    width: 1.5rem;
  }
}

.bottom-line {
  @include line-styling;
  background-color: #0b2b57;
  will-change: opacity, transform, background-color;

  &.swap-fade-enter-active {
    transition:$swap-timing map-get($swap-delays, 1) ease-in-out;
    transition-property: opacity, transform;
  }

  &.swap-fade-leave-active {
    transition: all $swap-timing--delayed $swap-out-motion;
    transition-property: opacity, transform;
  }

  &.reverse {
    right: 0;
    background-color: #092851;
    width: calc(60% + 1px);
  }
}

.bottom-line:before {
  right: 0;
  will-change: opacity, transform;
  box-shadow: 0 1.5rem 0 0 #0b2b57;
  border-bottom-right-radius: 1rem;
}

.bottom-line.reverse:before {
  left: 0;
  box-shadow: 0 1.5rem 0 0 #092851;
  border-bottom-right-radius: 1rem;
  transform: scaleX(-1);
}

