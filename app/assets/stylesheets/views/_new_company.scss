.slide-in-top {
  animation: slide-in-top 1.2s forwards;
  animation-iteration-count: 1;
  padding-right: 0;
  position: relative;
  top: -6.25rem;
}

@keyframes slide-in-top {
  40% { top: 0; left: 0; padding-right: 0; }
  50% { left: -.125rem; }
  60% { left: .125rem; }
  80% { left: -.0625rem; padding-right: .125rem; }
  90% { left: .0625rem; padding-right: -.0625rem; }
  100% { left: 0; top: 0; padding-right: 0; }
}

.slide-in-top-delayed {
  animation: slide-in-top-delayed  1.2s forwards;
  animation-iteration-count: 1;
  padding-right: 0;
  position: relative;
  top: -6.25rem;
}

@keyframes slide-in-top-delayed  {
  30% { top: -6.25rem; }
  70% { top: 0.125rem; }
  80% { top: -0.125rem; }
  90% { top: 0rem; }
  100% { top: 0; }
}

.slide-in-right {
  animation: slide-in-right 2.0s forwards;
  animation-iteration-count: 1;
  margin-right: 0.125rem;
  position: absolute;
  left: 4.688rem;
}

@keyframes slide-in-right {
  50% { left: 4.688rem; }
  100% { left: 0; }
}

.fade-in {
  animation: fade-in 1.0s forwards;
  animation-iteration-count: 1;
  opacity: 0.0;
}

@keyframes fade-in {
  100% { opacity: 1.0; }
}

.connector-animation {
  fill: none;
  height: 3rem;
  position: absolute;
  stroke: $themed-very-fair;
  stroke-width: 2;
  top: -1rem;
  width: 3rem;
}

.connector-animation__path {
  stroke-dasharray: 40;
  stroke-dashoffset: 40;
  animation: dash 2.4s linear forwards;
}

@keyframes dash {
  0% { stroke-dashoffset: 40; }
  80% { stroke-dashoffset: 40; }
  100% { stroke-dashoffset: 0; }
}
