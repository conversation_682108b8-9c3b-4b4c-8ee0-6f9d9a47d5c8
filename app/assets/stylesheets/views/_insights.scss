.custom-widget {
  min-height: 393px;

  .box-size {
    background-color: transparent;
    box-shadow: none;
    padding: 0;
  }
}

.scrollable-table--modal {
  max-width: 1280px;
  overflow: hidden;
  width: 100%;

  .scrollable-table-outer-wrap {
    height: 100%!important;
    max-height: 600px!important;

    table {
      th,td {
        border-top: 0;
      }
      .header-data--sticky,
      .table-data--sticky {
        left: 80px!important;

        &:first-child {
          left: 0!important;
          min-width: 80px;
        }
      }
    }
  }
}

.insight-card {
  position: relative;
  transition: all 200ms ease-out;

  .hover-block {
    background: #041e42;
    border-radius: 0.5rem;
    bottom: 0;
    display: none;
    height: 85%;
    left: 0;
    opacity: 0.65;
    position: absolute;
    right: 0;
    width: 100%;
    z-index: 9;
  }

  &:hover {
    .hover-block {
      display: flex;
    }
  }
}

.insight-scroll {
  .box__inner {
    overflow-x: hidden;

    .charts {
      margin-right: 10px;
      min-height: 125px;
    }

    &::-webkit-scrollbar {
      transition: opacity 0s linear;
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(108, 117, 125, 0.5);
      border-radius: 4px;

      &:hover {
        background: rgba(52, 58, 64, 0.5);
      }
    }
  }
}

.charts {
  .chart-list {
    max-height: 7rem;
    overflow-y: auto;
    transition: $transition-base;
  }

  .toggle-expand:after {
    content: '+ Show More';
  }

  &.expanded {
    .chart-list {
      max-height: 360px;
      transition: $transition-base;
    }

    .toggle-expand:after {
      content: '- Show Less';
    }
  }
}

.modal-box-holder {
  height: 100%;

  .box-size {
    height: 100%;
    padding-left: 10px;
    margin-bottom: 15px;
  }
}

.modal-box-holder {
  .box-size {
    .box__inner {
      height: 100%;
      max-height: 8.8rem;
      overflow-y: auto;
    }
  }
}

.date-color {
  color: #1E949B;
}

.insights-summary {
  height: 215px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-top: 5px;
  padding-right: 20px;
  
  &::-webkit-scrollbar {
    transition: opacity 0s linear;
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(108, 117, 125, 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(52, 58, 64, 0.5);
    }
  }
}
