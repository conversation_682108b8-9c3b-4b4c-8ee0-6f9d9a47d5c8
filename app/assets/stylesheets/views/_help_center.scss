// Treating these hc-variable classes like extensions of the standard helper classes (thus !important is used)
.hc-text-header { 
  color: var(--hc-header-text, white) !important; 

  &.hc-text-header--hover:hover {
    color: var(--hc-header-text, white) !important;
  }
}

.hc-text-links { 
  color: var(--hc-body-links, $primary) !important;

  &:hover {
    color: var(--hc-body-links, $primary) !important;
  }
}

.hc-bg-button { 
  background-color: var(--hc-button-bg, $primary) !important;
  border-color: var(--hc-button-bg, $primary) !important;
  color: var(--hc-button-text, white) !important;

  &.hc-bg-button--hover:hover {
    background-color: var(--hc-button-hover-bg, shade-color($primary, 16%)) !important;
    border-color: var(--hc-button-hover-bg, shade-color($primary, 16%)) !important;
    color: var(--hc-button-text, white) !important;
  }
}

.help-center-custom-pill {
  border-radius: 50rem;
  max-width: 50%;
}

.help-center-title {
  font-size: 4rem;
}

.help-center-custom-pills-container {
  width: 35%;
}

.help-center-image {
  width: 100%;
  transform: scaleX(-1);
}

.help-center-main-content {
  position: relative;
  z-index: 1;
}

$after-effect-width: 0.25rem;
$after-effect-offset: 2.25rem;
$open-icon-font-size: 1.25rem;

.open-article-item {
  position: relative;

  .open-article-icon {
    font-size: $open-icon-font-size;
    position: absolute;
    right: -1.5rem;
    top: calc(50% - (#{$open-icon-font-size}/2));
  }

  &:after {
    background-color: $color-info;
    border-radius: 0.125rem;
    bottom: 0;
    content: "";
    left: -$after-effect-offset;
    height: 100%;
    position: absolute;
    transition: $transition-base;
    transform: scaleY(0);
    transform-origin: center;
    width: $after-effect-width;
  }

  &:hover { 
    &:after {
      transform: scaleY(1);
    }

    .open-article-icon {
      display: block !important;
    }
  }
}
