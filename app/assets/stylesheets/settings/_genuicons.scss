/* Don't update these lines */

@font-face {
  font-family: "genuicon";
  src: asset-url("genuicon.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

[data-icon]:before,
[class^="genuicon-"]:before,
[class*=" genuicon-"]:before {
  font-family: "genuicon" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: -0.125em;
}

[data-icon]:before {
  content: attr(data-icon);
}


/* Some icons feel poorly sized. These fix that. */

.genuicon-bell:before,
.genuicon-automated-tasks:before {
  display: inline-block;
  transform: scale(0.875);
  transform-origin: center bottom;
}

.nulodgicon-stats-bars:before {
  display: inline-block;
  transform: scale(1.125);
  transform-origin: center bottom;
}

.nulodgicon-cog:before {
  display: inline-block;
  transform: scale(1.0625);
  transform-origin: center center;
}

.genuicon-list-no-split,
.genuicon-list-with-split {
  display: inline-block;
  transform: scale(1.125);
  transform-origin: center center;
}

/* Paste updated icons below */

.genuicon-paper-plane:before {
  content: "\e000";
}
.genuicon-help-circled:before {
  content: "\e001";
}
.genuicon-person-add:before {
  content: "\e002";
}
.genuicon-ban:before {
  content: "\e003";
}
.genuicon-ban:before {
  content: "\e003";
}
.genuicon-split:before {
  content: "\e004";
}
.genuicon-pricetags:before {
  content: "\e005";
}
.genuicon-barcode:before {
  content: "\e006";
}
.genuicon-list-ul:before {
  content: "\e007";
}
.genuicon-ios-keypad:before {
  content: "\e009";
}
.genuicon-square-o:before {
  content: "\e008";
}
.genuicon-check-square-o:before {
  content: "\e00a";
}
.genuicon-cash:before {
  content: "\e00b";
}
.genuicon-money:before {
  content: "\e00c";
}
.genuicon-magic:before {
  content: "\e00d";
}
.genuicon-star:before {
  content: "\e00e";
}
.genuicon-documents:before {
  content: "\e00f";
}
.genuicon-paper-clip:before {
  content: "\e010";
}
.genuicon-clock-o:before {
  content: "\e011";
}
.genuicon-android-time:before {
  content: "\e012";
}
.genuicon-ios-person-outline:before {
  content: "\e013";
}
.genuicon-assets:before {
  content: "\e014";
}
.genuicon-calendar-check-o:before {
  content: "\e015";
}
.genuicon-calendar-o:before {
  content: "\e016";
}
.genuicon-laptop:before {
  content: "\e017";
}
.genuicon-comment-o:before {
  content: "\e018";
}
.genuicon-exclamation:before {
  content: "\e019";
}
.genuicon-envelope-o:before {
  content: "\e01a";
}
.genuicon-person-stalker:before {
  content: "\e01b";
}
.genuicon-ticket:before {
  content: "\e01c";
}
.genuicon-align-justify:before {
  content: "\e01d";
}
.genuicon-clipboard-old:before {
  content: "\e01e";
}
.genuicon-flag-o:before {
  content: "\e01f";
}
.genuicon-exclamation-circle:before {
  content: "\e020";
}
.genuicon-android-funnel:before {
  content: "\e023";
}
.genuicon-keyboard:before {
  content: "\e024";
}
.genuicon-refresh:before {
  content: "\e025";
}
.genuicon-minus-circle:before {
  content: "\e026";
}
.genuicon-thumbnails-large:before {
  content: "\e027";
}
.genuicon-automate:before {
  content: "\e029";
}
.genuicon-circle-line:before {
  content: "\e02a";
}
.genuicon-alerts:before {
  content: "\e021";
}
.genuicon-permissions:before {
  content: "\e022";
}
.genuicon-arrows-v:before {
  content: "\e028";
}
.genuicon-sort-up:before {
  content: "\e02d";
}
.genuicon-funnel:before {
  content: "\e02c";
}
.genuicon-sort-down:before {
  content: "\e02f";
}
.genuicon-automation-bot:before {
  content: "\e02e";
}
.genuicon-checkmark:before {
  content: "\e031";
}
.genuicon-exclamation-triangle:before {
  content: "\e032";
}
.genuicon-mobile:before {
  content: "\e033";
}
.genuicon-arrows:before {
  content: "\e034";
}
.genuicon-ellipsis-v:before {
  content: "\e035";
}
.genuicon-automated-tasks:before {
  content: "\e037";
}
.genuicon-expand:before {
  content: "\e038";
}
.genuicon-resize-down:before {
  content: "\e039";
}
.genuicon-loop-alt3:before {
  content: "\e03a";
}
.genuicon-hourglass-end:before {
  content: "\e03b";
}
.genuicon-check-clipboard-1:before {
  content: "\e03c";
}
.genuicon-clipboard-1:before {
  content: "\e03d";
}
.genuicon-source-plaid:before {
  content: "\e03f";
}
.genuicon-source-contract:before {
  content: "\e040";
}
.genuicon-source-quickbooks:before {
  content: "\e041";
}
.genuicon-source-xero:before {
  content: "\e042";
}
.genuicon-module-assets-old:before {
  content: "\e043";
}
.genuicon-module-contracts:before {
  content: "\e044";
}
.genuicon-module-helpdesk:before {
  content: "\e045";
}
.genuicon-module-network:before {
  content: "\e046";
}
.genuicon-module-telecom:before {
  content: "\e047";
}
.genuicon-files-o:before {
  content: "\e049";
}
.genuicon-dislike:before {
  content: "\e04a";
}
.genuicon-like-filled:before {
  content: "\e04b";
}
.genuicon-slack:before {
  content: "\e04c";
}
.genuicon-vendor-link:before {
  content: "\e04d";
}
.genuicon-module-vendors:before {
  content: "\e048";
}
.genuicon-contract-link:before {
  content: "\e04e";
}
.genuicon-logo:before {
  content: "\e050";
}
.genuicon-pencil-square-o:before {
  content: "\e051";
}
.genuicon-apps:before {
  content: "\e052";
}
.genuicon-flag:before {
  content: "\e053";
}
.genuicon-stopwatch:before {
  content: "\e054";
}
.genuicon-duplicate-contract:before {
  content: "\e056";
}
.genuicon-plus-minus:before {
  content: "\e055";
}
.genuicon-arrow-swap:before {
  content: "\e057";
}
.genuicon-merge-icon:before {
  content: "\e058";
}
.genuicon-invite-user:before {
  content: "\e05a";
}
.genuicon-bell-slash-o:before {
  content: "\e05b";
}
.genuicon-stack:before {
  content: "\e05c";
}
.genuicon-rocket:before {
  content: "\e064";
}
.genuicon-qr-code:before {
  content: "\e066";
}
.genuicon-upgrade:before {
  content: "\e067";
}
.genuicon-company:before {
  content: "\e069";
}
.genuicon-printer:before {
  content: "\e036";
}
.genuicon-sample-company:before {
  content: "\e06a";
}
.genuicon-reports:before {
  content: "\e06c";
}
.genuicon-parent-company:before {
  content: "\e068";
}
.genuicon-image:before {
  content: "\e06d";
}
.genuicon-clipboard-with-check:before {
  content: "\e03e";
}
.genuicon-nav-dashboard:before {
  content: "\e05e";
}
.genuicon-nav-incoming:before {
  content: "\e05f";
}
.genuicon-nav-insights:before {
  content: "\e060";
}
.genuicon-nav-logs:before {
  content: "\e061";
}
.genuicon-nav-resources:before {
  content: "\e062";
}
.genuicon-nav-tickets:before {
  content: "\e063";
}
.genuicon-nav-connectors:before {
  content: "\e06e";
}
.genuicon-nav-transactions:before {
  content: "\e072";
}
.genuicon-nav-saas-usage:before {
  content: "\e074";
}
.genuicon-nav-vendor-spend:before {
  content: "\e06f";
}
.genuicon-check-mark-circle-two:before {
  content: "\e02b";
}
.genuicon-ios-close:before {
  content: "\e070";
}
.genuicon-checkmark-circled:before {
  content: "\e071";
}
.genuicon-brush:before {
  content: "\e073";
}
.genuicon-cost:before {
  content: "\e075";
}
.genuicon-hardware:before {
  content: "\e076";
}
.genuicon-history:before {
  content: "\e077";
}
.genuicon-location:before {
  content: "\e078";
}
.genuicon-notes:before {
  content: "\e079";
}
.genuicon-software:before {
  content: "\e07a";
}
.genuicon-sources:before {
  content: "\e07b";
}
.genuicon-summary:before {
  content: "\e07c";
}
.genuicon-warranty:before {
  content: "\e07d";
}
.genuicon-cloud:before {
  content: "\e07e";
}
.genuicon-align-left:before {
  content: "\e07f";
}
.genuicon-align-center:before {
  content: "\e081";
}
.genuicon-align-right:before {
  content: "\e082";
}
.genuicon-canned-action:before {
  content: "\e088";
}
.genuicon-knowledge-base:before {
  content: "\e089";
}
.genuicon-save:before {
  content: "\e083";
}
.genuicon-draggable:before {
  content: "\e08b";
}
.genuicon-state-of-it:before {
  content: "\e08d";
}
.genuicon-download:before {
  content: "\e08a";
}
.genuicon-nav-discovered:before {
  content: "\e087";
}
.genuicon-nav-reporting:before {
  content: "\e08c";
}
.genuicon-repeat:before {
  content: "\e086";
}
.genuicon-repeat-once:before {
  content: "\e08e";
}
.genuicon-nav-task-scheduler:before {
  content: "\e08f";
}
.genuicon-feed:before {
  content: "\e090";
}
.genuicon-workspace:before {
  content: "\e091";
}
.genuicon-accounting:before {
  content: "\e092";
}
.genuicon-city:before {
  content: "\e093";
}
.genuicon-classroom:before {
  content: "\e094";
}
.genuicon-computer:before {
  content: "\e095";
}
.genuicon-customer-service:before {
  content: "\e096";
}
.genuicon-data:before {
  content: "\e097";
}
.genuicon-delivery:before {
  content: "\e098";
}
.genuicon-factory:before {
  content: "\e099";
}
.genuicon-farm:before {
  content: "\e09a";
}
.genuicon-hr:before {
  content: "\e09b";
}
.genuicon-it:before {
  content: "\e09c";
}
.genuicon-location-thin:before {
  content: "\e09d";
}
.genuicon-office:before {
  content: "\e09e";
}
.genuicon-paperwork:before {
  content: "\e09f";
}
.genuicon-remote-office:before {
  content: "\e0a0";
}
.genuicon-docs:before {
  content: "\e065";
}
.genuicon-build-asset-types:before {
  content: "\e0a1";
}
.genuicon-build-blocked-keywords:before {
  content: "\e0a2";
}
.genuicon-build-categories:before {
  content: "\e0a3";
}
.genuicon-build-custom-forms:before {
  content: "\e0a4";
}
.genuicon-build-documents:before {
  content: "\e0a5";
}
.genuicon-build-faqs:before {
  content: "\e0a6";
}
.genuicon-build-responses:before {
  content: "\e0a7";
}
.genuicon-saved-build:before {
  content: "\e0a9";
}
.genuicon-apply-build-once:before {
  content: "\e0aa";
}
.genuicon-apply-build:before {
  content: "\e0ab";
}
.genuicon-add-saved-build:before {
  content: "\e0ac";
}
.genuicon-build-elements:before {
  content: "\e0a8";
}
.genuicon-edit-copy:before {
  content: "\e0ad";
}
.genuicon-copy:before {
  content: "\e0ae";
}
.genuicon-add-copy:before {
  content: "\e0af";
}
.genuicon-build-automated-tasks:before {
  content: "\e080";
}
.genuicon-build-email-templates:before {
  content: "\e0b0";
}
.genuicon-nav-automation:before {
  content: "\e0b1";
}
.genuicon-module-assets:before {
  content: "\e05d";
}
.genuicon-data-encryption:before {
  content: "\e04f";
}
.genuicon-patch-management:before {
  content: "\e0b2";
}
.genuicon-antivirus:before {
  content: "\e0b3";
}
.genuicon-firewall:before {
  content: "\e0b4";
}
.genuicon-device-health:before {
  content: "\e0b6";
}
.genuicon-random-tabs:before {
  content: "\e0b7";
}
.genuicon-user-avatar:before {
  content: "\e0c1";
}
.genuicon-department:before {
  content: "\e0c3";
}
.genuicon-phone:before {
  content: "\e0c4";
}
.genuicon-email:before {
  content: "\e0c5";
}
.genuicon-country:before {
  content: "\e0c6";
}
.genuicon-manager:before {
  content: "\e0c7";
}
.genuicon-teammate-job-title:before {
  content: "\e0c8";
}
.genuicon-sso-login:before {
  content: "\e0c9";
}
.genuicon-street-address:before {
  content: "\e0ca";
}
.genuicon-status:before {
  content: "\e0b9";
}
.genuicon-secondary-question:before {
  content: "\e0ba";
}
.genuicon-ooo:before {
  content: "\e0bc";
}
.genuicon-repeat-alt:before {
  content: "\e0bb";
}
.genuicon-grid:before {
  content: "\e059";
}
.genuicon-grid-full:before {
  content: "\e06b";
}
.genuicon-list-no-split:before {
  content: "\e085";
}
.genuicon-list-with-split:before {
  content: "\e0bd";
}
.genuicon-split-pane:before {
  content: "\e0be";
}
.genuicon-level-down:before {
  content: "\e0b";
}
.genuicon-up-down-arrow:before {
  content: "\e030";
}
.genuicon-reply-arrow:before {
  content: "\e0b5";
}
.genuicon-backups:before {
  content: "\e0bf";
}
.genuicon-info-circled:before {
  content: "\e0c0";
}
.genuicon-sla-old:before {
  content: "\e0b8";
}
.genuicon-clipboard:before {
  content: "\e0cb";
}
.genuicon-smart-filter:before {
  content: "\e0cc";
}
.genuicon-kanban:before {
  content: "\e084";
}
.genuicon-sla:before {
  content: "\e0c2";
}
.genuicon-priority-generic:before {
  content: "\e0cd";
}
.genuicon-priority-high:before {
  content: "\e0ce";
}
.genuicon-priority-low:before {
  content: "\e0cf";
}
.genuicon-priority-medium:before {
  content: "\e0d0";
}
