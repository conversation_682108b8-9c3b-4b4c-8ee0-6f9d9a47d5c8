@mixin light-mode-vars {
  @include _generate-theme-css-variables($themed-colors-map, "light");

  @include _generate-themed-module-color-variables($module-colors-light, "light");
  @include _generate-themed-module-color-variables($module-colors-dark, "dark");
}

@mixin dark-mode-vars {
  @include _generate-theme-css-variables($themed-colors-map, "dark");

  @include _generate-themed-module-color-variables($module-colors-dark, "light");
  @include _generate-themed-module-color-variables($module-colors-light, "dark");
}

@mixin dark-mode-low-vars {
  @include _generate-theme-css-variables($themed-colors-map, "dark-low");
  
  @include _generate-themed-module-color-variables($module-colors-dark, "light");
  @include _generate-themed-module-color-variables($module-colors-light, "dark");
}

@mixin theme-description-vars {
  @include _generate-theme-css-variables($themed-colors-map, "theme-description");
}

:root {
  @include light-mode-vars;
}

:root.dark-theme {
  @include dark-mode-vars;
}

:root.dark-low-theme {
  @include dark-mode-low-vars;
}

// Store and access devtools documentation within $themed-colors-map
:root.theme-description {
  @include theme-description-vars;
}
