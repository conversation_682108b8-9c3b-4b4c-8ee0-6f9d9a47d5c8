// _theming-overrides.scss
// First Generate utility classes from our $themed-colors-map amd $themed-overrides-map
// Then manually override some bootstrap color resets, defaults, third party library styling to specifically utilize our themed colors


// Semantic colors: generate utility classes in a bootstrap safe manner
@include _generate-basic-utility-classes-with-css-vars($themed-colors-map);

// Themed CSS Variable Overrides of useful ulitity classes and manually override currently implemented sass variables.
// Example: The themed-overrides-map is currently used in application.css with the 
// call to @include _override-basic-utility-classes-with-css-vars($themed-overrides-map); and the example output of
// .text-dark {
//   color: var(--themed-dark) !important;
// }
@include _override-basic-utility-classes-with-css-vars($themed-overrides-map);

body {
  background-color: $themed-main-bg;
  color: $themed-base;
}

a {
  color: var(--themed-link);

  &:hover {
    color: var(--themed-link-hover);
  }
}

.btn-outline-primary {
  color: $themed-link;
  border-color: $themed-link;
}

.table {
  th, td {
    border-top: 1px solid var(--themed-very-fair);
  }

  .table {
    background-color: $themed-box-bg;
  }
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(var(--themed-base-rgb-values), 0.05);
}

.sticky-columns .table-striped tbody tr:nth-of-type(odd) .table-data--sticky {
  background-color: $themed-very-fair;
}

hr {
  border-color: rgba(var(--themed-base-rgb-values), 0.1);
}

.dropdown-menu {
  background-color: $themed-box-bg;
  border-color: $themed-very-fair;
}

.dropdown-item {
  color: $themed-base;

  &:hover,
  &.dropdown-item:focus {
    background-color: $themed-light;
    color: $themed-base;
  }
}

.btn-light {
  background-color: $themed-light;
  border-color: $themed-light;
  color: $themed-base;

  &:hover {
    background-color: var(--themed-light-hover-bg);
    color: $themed-base;
  }

  &.disabled {
    background-color: $themed-very-light;
    border-color: $themed-very-light;
  }
}

.btn-dark {
  background-color: $themed-dark;
  border-color: $themed-dark;
  color: $themed-box-bg;

  &:hover {
    background-color: var(--themed-dark-hover);
    color: $themed-box-bg;
  }
}

// Workaround for some of our buttons that are styled inline with 'btn-link bg-dark'
a.text-secondary:hover,
a.text-secondary:focus {
  color: var(--themed-secondary-hover) !important;
}

a.text-dark:hover,
a.text-dark:focus {
  color: var(--themed-dark-hover) !important;
}

.alert-light,
.alert-basic-light {
  background-color: $themed-very-light !important;
  border: $themed-lighter !important;
  color: $themed-muted !important;
}

.nav-link.active {
  background-color: $themed-box-bg !important;
  color: $themed-secondary !important;
}

.form-control:disabled,
form-control[readonly] {
  background-color: $themed-light !important;
}

.vti__dropdown.disabled,
.vti__dropdown.open,
.vti__dropdown:hover  {
  background-color: $themed-light !important;
}

.vti__dropdown-arrow,
.country-selector-arrow {
  color: $themed-muted !important;
}

.vti__dropdown-list {
  background-color: $themed-box-bg !important;

  .vti__dropdown-item.highlighted {
    background-color: $themed-light;
    color: $themed-base;
  }
}

.country-selector {
  .field-input {
    color: $themed-base;
  }

  .country-list {
    background-color: $themed-box-bg !important;
    color: $themed-base;

    .country-list-item.keyboard-selected,
    .country-list-item:hover {
      background-color: $themed-light !important;
    }
  }

  .field-label,
  .country-list-item.selected {
    color: $themed-base !important;
  }
}

.mx-datepicker-popup {
  background-color: $themed-box-bg !important;

  .mx-calendar-content .cell:hover {
    background-color: $themed-light;
  }

  .mx-panel-date td.last-month,
  .mx-panel-date td.next-month {
    color: $themed-very-fair;
  }
}

.multiselect {
  .multiselect__option--selected {
    background-color: $themed-lighter;
    color: $themed-dark;
  }
}

.vue__time-picker-dropdown,
.vue__time-picker .dropdown {
  background-color: $themed-box-bg !important;
  border: 1px solid var(--themed-very-fair);
  border-top: none;
  border-radius: 0 0 $border-radius $border-radius;
  margin-top: 0.0625rem;

  ul li {
    color: $themed-base !important;
  }
}
