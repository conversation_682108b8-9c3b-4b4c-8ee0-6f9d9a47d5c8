/* Don't update these lines */

@font-face {
  font-family: "nulodgicon";
  src: asset-url("nulodgicon.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

[data-icon]:before,
[class^="nulodgicon-"]:before,
[class*=" nulodgicon-"]:before {
  font-family: "nulodgicon" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: -0.125em;
}

[data-icon]:before {
  content: attr(data-icon); 
}

// TODO: These are legacy and accidental color definitions that should be removed when a QA can be done.
.nulodgicon-android-checkmark-circle {
  color: #198754;
}

.nulodgicon-android-close {
  color: #e14144;
}


/* Replace from here to below with updated icons */

.nulodgicon-plus-circled:before {
  content: "\e000";
}
.nulodgicon-ios-cloud-download-outline:before {
  content: "\e001";
}
.nulodgicon-android-call:before {
  content: "\e002";
}
.nulodgicon-alert-circled:before {
  content: "\e003";
}
.nulodgicon-arrow-left-c:before {
  content: "\e004";
}
.nulodgicon-edit:before {
  content: "\e005";
}
.nulodgicon-ios-box:before {
  content: "\e006";
}
.nulodgicon-trash-b:before {
  content: "\e008";
}
.nulodgicon-paper-airplane:before {
  content: "\e009";
}
.nulodgicon-information-circled:before {
  content: "\e00a";
}
.nulodgicon-android-checkmark-circle:before {
  content: "\e00b";
}
.nulodgicon-checkmark:before {
  content: "\e00c";
}
.nulodgicon-eye-disabled:before {
  content: "\e00d";
}
.nulodgicon-eye:before {
  content: "\e00e";
}
.nulodgicon-ios-home-outline:before {
  content: "\e00f";
}
.nulodgicon-chevron-down:before {
  content: "\e010";
}
.nulodgicon-chevron-left:before {
  content: "\e011";
}
.nulodgicon-chevron-right:before {
  content: "\e012";
}
.nulodgicon-chevron-up:before {
  content: "\e013";
}
.nulodgicon-speedometer:before {
  content: "\e014";
}
.nulodgicon-ios-list-outline:before {
  content: "\e015";
}
.nulodgicon-person-stalker:before {
  content: "\e016";
}
.nulodgicon-ios-email-outline:before {
  content: "\e017";
}
.nulodgicon-android-add-circle:before {
  content: "\e018";
}
.nulodgicon-log-out:before {
  content: "\e019";
}
.nulodgicon-ios-copy-outline:before {
  content: "\e01a";
}
.nulodgicon-ios-cart-outline:before {
  content: "\e01b";
}
.nulodgicon-ios-telephone-outline:before {
  content: "\e01c";
}
.nulodgicon-ios-box-outline:before {
  content: "\e01d";
}
.nulodgicon-ios-help-outline:before {
  content: "\e01e";
}
.nulodgicon-ios-pulse:before {
  content: "\e01f";
}
.nulodgicon-unlink:before {
  content: "\e020";
}
.nulodgicon-link:before {
  content: "\e021";
}
.nulodgicon-nulodgicon:before {
  content: "\e022";
}
.nulodgicon-alert:before {
  content: "\e023";
}
.nulodgicon-link-1:before {
  content: "\e024";
}
.nulodgicon-upload:before {
  content: "\e025";
}
.nulodgicon-laptop:before {
  content: "\e026";
}
.nulodgicon-person:before {
  content: "\e027";
}
.nulodgicon-email:before {
  content: "\e028";
}
.nulodgicon-ios-undo:before {
  content: "\e029";
}
.nulodgicon-location:before {
  content: "\e02a";
}
.nulodgicon-android-download:before {
  content: "\e02b";
}
.nulodgicon-wrench:before {
  content: "\e02c";
}
.nulodgicon-arrow-down-b:before {
  content: "\e02d";
}
.nulodgicon-arrow-left-b:before {
  content: "\e02e";
}
.nulodgicon-android-checkbox-outline:before {
  content: "\e02f";
}
.nulodgicon-android-checkbox-outline-blank:before {
  content: "\e030";
}
.nulodgicon-navigate:before {
  content: "\e031";
}
.nulodgicon-ios-navigate:before {
  content: "\e032";
}
.nulodgicon-planet:before {
  content: "\e033";
}
.nulodgicon-ios-cog:before {
  content: "\e034";
}
.nulodgicon-ios-plus:before {
  content: "\e035";
}
.nulodgicon-ios-close-outline:before {
  content: "\e036";
}
.nulodgicon-ios-unlocked:before {
  content: "\e037";
}
.nulodgicon-locked:before {
  content: "\e038";
}
.nulodgicon-ios-world:before {
  content: "\e039";
}
.nulodgicon-android-locate:before {
  content: "\e03a";
}
.nulodgicon-ios-search-strong:before {
  content: "\e03b";
}
.nulodgicon-social-windows:before {
  content: "\e03c";
}
.nulodgicon-search:before {
  content: "\e03d";
}
.nulodgicon-log-in:before {
  content: "\e03e";
}
.nulodgicon-android-desktop:before {
  content: "\e03f";
}
.nulodgicon-social-whatsapp:before {
  content: "\e040";
}
.nulodgicon-load-b:before {
  content: "\e041";
}
.nulodgicon-connection-bars:before {
  content: "\e042";
}
.nulodgicon-chatbubbles:before {
  content: "\e043";
}
.nulodgicon-plus-round:before {
  content: "\e044";
}
.nulodgicon-android-clipboard:before {
  content: "\e045";
}
.nulodgicon-soup-can-outline:before {
  content: "\e046";
}
.nulodgicon-bug:before {
  content: "\e047";
}
.nulodgicon-hammer:before {
  content: "\e048";
}
.nulodgicon-file-o:before {
  content: "\e049";
}
.nulodgicon-help-circled:before {
  content: "\e04a";
}
.nulodgicon-ios-monitor-outline:before {
  content: "\e04b";
}
.nulodgicon-stats-bars:before {
  content: "\e04c";
}
.nulodgicon-cloud-download:before {
  content: "\e04d";
}
.nulodgicon-cloud-upload:before {
  content: "\e04e";
}
.nulodgicon-android-bulb:before {
  content: "\e04f";
}
.nulodgicon-arrow-right-c:before {
  content: "\e050";
}
.nulodgicon-trophy:before {
  content: "\e051";
}
.nulodgicon-card:before {
  content: "\e052";
}
.nulodgicon-diamond:before {
  content: "\e053";
}
.nulodgicon-dollar:before {
  content: "\e054";
}
.nulodgicon-pricetag:before {
  content: "\e055";
}
.nulodgicon-university:before {
  content: "\e056";
}
.nulodgicon-paperclip:before {
  content: "\e058";
}
.nulodgicon-arrow-up-b:before {
  content: "\e059";
}
.nulodgicon-arrow-right-b:before {
  content: "\e05a";
}
.nulodgicon-android-close:before {
  content: "\e007";
}
.nulodgicon-external-link:before {
  content: "\e05b";
}
.nulodgicon-binoculars:before {
  content: "\e05c";
}
.nulodgicon-vendors:before {
  content: "\e05d";
}
.nulodgicon-sun-o:before {
  content: "\e05e";
}
.nulodgicon-moon-o:before {
  content: "\e05f";
}
.nulodgicon-bell-o:before {
  content: "\e060";
}
.nulodgicon-mobile:before {
  content: "\e061";
}
.nulodgicon-bars:before {
  content: "\e062";
}
.nulodgicon-align-center:before {
  content: "\e057";
}
.nulodgicon-cog:before {
  content: "\e063";
}
.nulodgicon-unarchive:before {
  content: "\e065";
}
.nulodgicon-archive:before {
  content: "\e064";
}
.nulodgicon-switch:before {
  content: "\e066";
}
.nulodgicon-dot-3:before {
  content: "\e067";
}
.nulodgicon-thumbs-o-up:before {
  content: "\e068";
}
.nulodgicon-game-controller:before {
  content: "\e069";
}
