// Js code to download warranty csv file 
function processSerialNumber(serialNumber, index) {
  if (index > 0) {
  let input = document.getElementById(`inputtextpfinder${index}`)
	input.value = serialNumber;
	input.dispatchEvent(new Event("input"));
    if (serialNumbers.length != index + 1) {
      document.getElementById("AddItem").click();
    }
  } else {
    const input = document.getElementById("inputtextpfinder")
	input.value = serialNumber;
	input.dispatchEvent(new Event("input"));
  }
}
// Replace the actual serial Number arry below
const serialNumbers = ["CND5032725", "5CD0037MCQ", "5CD024K378", "CND5032731"]
	
function processNextSerialNumber() {
  serialNumbers.forEach((number, index) => {
    processSerialNumber(number, index)
  })
}

processNextSerialNumber();
