function titleTooltips(tooltipDataTitle = null) {

  function createTip(title, placement = 'bottom') {
    const tooltipNode = document.createElement('div');
    tooltipNode.className = "tooltip position-fixed";
    tooltipNode.style.left = 0;
    tooltipNode.style.top = 0;
    tooltipNode.setAttribute('aria-hidden', false);
    
    const innerTip = document.createElement('div');
    innerTip.className = "tooltip-inner"
    innerTip.innerHTML = title;
    tooltipNode.appendChild(innerTip);

    return tooltipNode;
  }

  function placeTip(el, tooltip) {
    const placement = el.dataset.titleTooltip;
    const { left, top, right, bottom, height, width } = el.getBoundingClientRect();
    const tipWidth = tooltip.getBoundingClientRect().width;
    const tipHeight = tooltip.getBoundingClientRect().height;
    const extraPadding = (parseInt(el.dataset.tooltipSpacing, 10) || 16);
    if (placement === 'right') {
      tooltip.style.transform = `translate3d(${parseInt(right + extraPadding, 10)}px, ${parseInt(top + (height / 2) - (tipHeight / 2), 10)}px, 0)`;
    } else if (placement === 'left') {
      tooltip.style.transform = `translate3d(${parseInt(left - tipWidth - extraPadding, 10)}px, ${parseInt(top + (height / 2) - (tipHeight / 2), 10)}px, 0)`;
    } else if (placement === 'top') {
      tooltip.style.transform = `translate3d(${parseInt(left + (width / 2) - (tipWidth / 2), 10)}px, ${parseInt(top - tipHeight - extraPadding, 10)}px, 0)`;
    } else {
      tooltip.style.transform = `translate3d(${parseInt(left + (width / 2) - (tipWidth / 2), 10)}px, ${parseInt(bottom + extraPadding, 10)}px, 0)`;
    }
    
    requestAnimationFrame(() => tooltip.setAttribute('aria-hidden', false));
  }

  function observeConditionalHide(el, tooltip) {
    const observedNode = document.querySelector(el.dataset.hideTooltipSelector);
    let reappearTimeout;
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'class') {
          if (observedNode.classList.contains(el.dataset.hideTooltipClass)) {
            tooltip.classList.add('force-hidden');
          } else {
            clearTimeout(reappearTimeout);
            reappearTimeout = setTimeout(() => {
              tooltip.classList.remove('force-hidden');
            }, 200);
          }
        }
      });
    });
    observer.observe(observedNode, { attributes: true, childList: false, subtree: false });
  }

  const tipableElements = tooltipDataTitle
    ? document.querySelectorAll(`[${tooltipDataTitle}]`)
    : document.querySelectorAll('[data-title-tooltip]');

  tipableElements.forEach((el, index) => {
    el.addEventListener('mouseover', () => {
      let tooltip = tooltipDataTitle
        ? document.querySelector(`[${tooltipDataTitle}-id="${index}"]`)
        : document.querySelector(`[data-tooltip-id="${index}"]`);

      if (!tooltip) {
        tooltip = createTip(el.title);
        el.removeAttribute('title');
        tooltipDataTitle
          ? tooltip.setAttribute(`${tooltipDataTitle}-id`, index)
          : tooltip.setAttribute("data-tooltip-id", index);

        document.body.appendChild(tooltip);
        tooltip.style.setProperty("display", "block", "important");

        const conditionalHide = el.dataset.hideTooltipSelector && el.dataset.hideTooltipClass;
        if (conditionalHide) {
          observeConditionalHide(el, tooltip);
        }
      }

      placeTip(el, tooltip);
    });

    el.addEventListener('mouseout', () => {
      const tooltip = tooltipDataTitle
        ? document.querySelector(`[${tooltipDataTitle}-id="${index}"]`)
        : document.querySelector(`[data-tooltip-id="${index}"]`);

      if (tooltip) {
        requestAnimationFrame(() => tooltip.setAttribute('aria-hidden', true));
      }
    });
  });

}
