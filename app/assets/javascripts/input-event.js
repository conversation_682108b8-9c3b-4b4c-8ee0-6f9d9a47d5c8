(function () {

  var postInvalidInputEvent = new CustomEvent("postInvalidInput");
  var timeout;

  var onInvalidInput = debounce(function (e) {
    var el = e.target;
    if (el.classList.contains('is-invalid')) {
      el.dispatchEvent(postInvalidInputEvent);
    }
  }, 250);

  document.addEventListener('input', onInvalidInput, false);

  function debounce(func, wait) {
    return function executed() {
      var context = this;
      var args = arguments;
      var later = function () {
        timeout = null;
        func.apply(context, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    }
  }

}());

