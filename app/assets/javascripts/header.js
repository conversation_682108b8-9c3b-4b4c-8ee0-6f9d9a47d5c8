 /* eslint-disable */
 document.addEventListener('DOMContentLoaded', function () {
  var commonMixpanelProps = {
    company: Vue.prototype.$companyName ? Vue.prototype.$companyName : ''
  }

  /* Nav Drawer */
  var isAnimatingDrawer = false;
  var $drawerToggle = document.querySelector('.toggle-drawer');
  var $themeToggle = document.querySelector('.drawer-theme-toggle');
  var $wideContentToggle = document.querySelector('.wide-theme-toggle');
  var $verticalContentToggle = document.querySelector('.vertical-theme-toggle');
  var $drawerOverlay = document.querySelector('.drawer-overlay');
  var $settingsToggle = document.querySelector('#settings_toggle');
  var transitionEndName = whichTransitionEvent();

  if ($settingsToggle) {
    $settingsToggle.addEventListener('click', function () {
      var navTheme = document.cookie.split('; ').find(row => row.startsWith('nav-theme='));
  
      if (navTheme && navTheme.split('=')[1] === 'vertical' && $verticalContentToggle) {
        $verticalContentToggle.classList.add('active');
      } else if ($verticalContentToggle) {
        $verticalContentToggle.classList.remove('active');
      }
    });
  }
  
  const getBasicHeaders = () => {
    return {
      "X-Key-Inflection": "camel",
      "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').attributes['content'].value,
    };
  };

  const getWorkspaceHeaders = (company = window.$company, workspace = window.$workspace) => {
    return {
      ...getBasicHeaders(),
      "X-Genuity-Company-Id": (company ? company.id : ""),
      "X-Genuity-Workspace-Id": (workspace ? workspace.id : ""),
    };
  };

  if (Vue.prototype.$companyChatSupport == "false") {
    document.body.classList.add('show-chat-button');
  }

  if ($drawerToggle) {
    $drawerToggle.addEventListener('click', function (e) {
      e.preventDefault();
      requestAnimationFrame(function () {
        isAnimatingDrawer = true;

        if (document.body.classList.contains('menu-closed')) {
          document.body.classList.remove('menu-closed');
        } else {
          document.body.classList.add('menu-closed');
        }
      });
    }, false);

    $drawerToggle.addEventListener('mouseenter', function (e) {
      requestAnimationFrame(function () { document.body.classList.add('hint-drawer-toggle') });
    }, false)

    $drawerToggle.addEventListener('mouseleave', function (e) {
      if (!isAnimatingDrawer) {
        document.body.classList.remove('hint-drawer-toggle');
      }
    }, false);

    $drawerToggle.addEventListener(transitionEndName, function (e) {
      requestAnimationFrame(function () {
        if (e.propertyName == "transform" || e.propertyName == "webkitTransform") {
          isAnimatingDrawer = false;
          document.body.classList.remove('hint-drawer-toggle');
        }
      })
    });

    $drawerOverlay.addEventListener('click', function (e) {
      document.body.classList.add('menu-closed');
    }, false);
  }

  if ($themeToggle) {
    $themeToggle.addEventListener('click', function (e) {
      requestAnimationFrame(function () {
        const root = document.documentElement;
        const iframes = document.querySelectorAll('iframe');
        const siteThemeCookieExists = document.cookie.split(";").some((item) => item.trim().startsWith("site_theme="));
        const siteThemeCookieIsLight = document.cookie.split(";").some((item) => item.includes("site_theme=light"));
        const isSameOrigin = (iframe) => new URL(iframe.src).origin === window.location.origin;
        const isOverviewPage = window.location.pathname === '/company/overview';
        const isDarkMode = siteThemeCookieIsLight || !siteThemeCookieExists;

        const applyTheme = (element, addClass, removeClass) => {
          element.classList.remove(removeClass);
          element.classList.add(addClass);
        };

        const updateIframeTheme = (themeToAdd, themeToRemove) => {
          for (const iframe of iframes) {
            if (isOverviewPage) {
              if (isSameOrigin(iframe) && iframe.contentWindow && iframe.contentWindow.document) {
                applyTheme(iframe.contentWindow.document.querySelector('html'), themeToAdd, themeToRemove);
              }
            } else {
              applyTheme(iframe.contentWindow.document.querySelector('html'), themeToAdd, themeToRemove);
            }
          }
        };

        if (isDarkMode) {
          applyTheme(root, 'dark-theme', 'light-theme');
          setCookie('site_theme', 'dark', true);
          $themeToggle.classList.add('active');
          updateIframeTheme('dark-theme', 'light-theme');
        } else {
          applyTheme(root, 'light-theme', 'dark-theme');
          setCookie('site_theme', 'light', true);
          $themeToggle.classList.remove('active');
          updateIframeTheme('light-theme', 'dark-theme');
        }
      });
    }, false);
  }

  if ($wideContentToggle) {
    $wideContentToggle.addEventListener('click', function (e) {
      requestAnimationFrame(function () {
        if (document.body.classList.contains('content-wide-theme')) {
          document.body.classList.remove('content-wide-theme');
          setCookie('content_width_theme', 'normal', true);
          setCookie('user_first_time', true, true);
          $wideContentToggle.classList.remove('active');
        } else {
          document.body.classList.add('content-wide-theme');
          setCookie('content_width_theme', 'wide', true);
          setCookie('user_first_time', true, true);
          $wideContentToggle.classList.add('active');
        }
      });
    }, false);
  }

  if ($verticalContentToggle) {
    let hoverTimer;
    const hoverDuration = 1500; 
    $verticalContentToggle.addEventListener('mouseover', () => {
      const verticalThemeCookie = document.cookie.split(";").some((item) => item.includes("nav-theme=vertical"));
      hoverTimer = setTimeout(() => {
        if (!hasSeenThisModuleWalkthrough() && !verticalThemeCookie) {
          updateVerticalWalkThrough();
        }
      }, hoverDuration);

    });
    $verticalContentToggle.addEventListener('mouseout', () => {
      clearTimeout(hoverTimer);
    });

    $verticalContentToggle.addEventListener('click', function (e) {
      const verticalThemeExists = document.cookie.split(";").some((item) => item.trim().startsWith("nav-theme="));
      const cookieWideContent = document.cookie.split('; ').some((item) => item.includes("content_width_theme=wide"));
      const cookieDataUser = document.cookie.split('; ').some((item) => item.includes(`user_first_time=true`));
      const verticalNavClass = document.getElementsByClassName('vertical-nav');
      const horizontalNavClass = document.getElementsByClassName('horizontal-nav');
      const verticalThemeCookie = document.cookie.split(";").some((item) => item.includes("nav-theme=vertical"));
      const horizontalImportClass = document.getElementsByClassName('horizontal-nav-import');
      const leftArrowClass = document.getElementsByClassName('left-arrow');
      const swapToHorizontalNav = verticalThemeExists && verticalThemeCookie;

      if (!hasSeenThisModuleWalkthrough() && !verticalThemeCookie) {
        updateVerticalWalkThrough();
      }

  
      if (swapToHorizontalNav) {
        setCookie("nav-theme", 'horizontal', true);
        toggleImportElements(horizontalImportClass, true);
        toggleContentClasses();
        document.body.classList.remove('module-vertical-nav');
        $verticalContentToggle.classList.remove('active');
      } else {
        setCookie("nav-theme", 'vertical', true);
        toggleImportElements(horizontalImportClass, false);
        toggleContentClasses();
        document.body.classList.add('module-vertical-nav');
        $verticalContentToggle.classList.add('active');
        if (!cookieWideContent && $wideContentToggle) {
          document.body.classList.add('content-wide-theme');
          $wideContentToggle.classList.add('active');
          setCookie('content_width_theme', 'wide', true);
        }
      }
      document.dispatchEvent(new CustomEvent('toggleVerticalNav',  {detail: !swapToHorizontalNav}));
    })
  }

  function toggleImportElements(elements, horizontal) {
    for (const element of elements) {
      if (element && element.classList) {
        element.classList.toggle('d-none', !horizontal);
      }
    }
  }

  function toggleContentClasses() {
    const contentModuleElement = document.getElementById('contentSlot');
    contentModuleElement.classList.toggle('module-content--vertical-nav');
    contentModuleElement.classList.toggle('module-content');
  }

  const updateVerticalWalkThrough = async () => {
    const company = getCompanyFromStorage();
    const url = `/check_out_my_module_onboarding.json?module_name=${'vertical_view'}`;
    await fetch(encodeURI(url), { headers: getWorkspaceHeaders(company), method: 'PUT' });
    removePulseClass();
    Vue.prototype.$hasSeenModuleWalkthrough['vertical_view'] = true;
  };

  function removePulseClass() {
    const settingToggleClass = document.getElementsByClassName('notification-indicator');
    const settingsClass = document.getElementsByClassName('settings-notification-indicator');
    settingToggleClass[0].classList.remove('notification-indicator');
    settingsClass[0].classList.remove('settings-notification-indicator');
  };


  function hasSeenThisModuleWalkthrough() {
    return Vue.prototype.$hasSeenModuleWalkthrough && Vue.prototype.$hasSeenModuleWalkthrough['vertical_view'];
  }

  function setUserWideContent() {
    if ($wideContentToggle) {
      const cookieWideContent = document.cookie.split('; ').some((item) => item.includes("content_width_theme=wide"));
      if (cookieWideContent || (userFirstTime() && window.innerWidth > 1800)) {
        if (!document.body.classList.contains('content-wide-theme')) {
          document.body.classList.add('content-wide-theme');
          $wideContentToggle.classList.add('active');
        }
      }
    }
  }

  function userFirstTime() {
    const cookieDataUser = document.cookie.split('; ').find(row => row.startsWith(`user_first_time`));
    const isHelpTicketPath = document.location.pathname.split('/').includes('help_tickets');
    return !cookieDataUser && isHelpTicketPath && !pathMatches(); 
  }

  function pathMatches() {
    const pathPatterns = { show: /\/help_tickets\/(\d+)/, create: /\/help_tickets\/new/ };
    const curretPathName = document.location.pathname;
    return pathPatterns['show'].test(curretPathName) || pathPatterns['create'].test(curretPathName);
  }
  setUserWideContent();
  document.addEventListener('click', function() {
    setUserWideContent();
  });
  
  /* Hides intercom chat button when notification occurs */
  setTimeout(() => {
    const vuetifyElement = document.querySelector('.vue-notification-group span');

    if(vuetifyElement) {
      var observer = new MutationObserver(mutations => {
        for (const m of mutations) {
          if (m.target.classList[0] == 'vue-notification-wrapper') {
            const notificationWrapper = document.querySelector('.vue-notification-wrapper');
            
            if (document.body.contains(notificationWrapper)) {
              console.log('Chat button is hidden, due to notification display');
              document.body.classList.add('hide-chat-button');
            } 
          } else if (!document.querySelector('.vue-notification-wrapper')){
            document.body.classList.remove('hide-chat-button');
          }
        }
      });
  
      observer.observe(vuetifyElement, { attributes: true, childList: true, subtree: true });
    }
  },2000);

  /* User menu dropdown */
  var menuToggles = document.querySelectorAll('.company-menu-toggle, .workspace-menu-toggle, .managing-menu-toggle, .app-toggle, .settings-toggle, .profile-menu-toggle');
  menuToggles.forEach((t) => {
    var menuId = t.dataset.dropdownMenu;
    var menu = document.querySelector(`#${menuId}`);
    var menuLinks = null;

    if (menu) {
      menuLinks = menu.querySelectorAll('a');
      menu.addEventListener('click', function (e) {
        e.stopPropagation();
      }, false);
    }

    if (menuLinks) {
      for (var i = 0, len = menuLinks.length; i < len; i++) {
        menuLinks[i].addEventListener('click', function (e) {
          document.body.classList.remove('show');
        }, false);
      }
    }

    t.addEventListener('click', function(e) {
      e.stopPropagation();
      var id = e.currentTarget.dataset.dropdownMenu;
      var element = document.querySelector(`#${id}`);
      if (!element) {
        return;
      }
      if (!element.classList.contains("show")) {
        document.querySelectorAll(".header-menu").forEach((e) => {
          e.classList.remove("show");
        });
        element.classList.add('show');
      } else {
        document.querySelectorAll(".header-menu").forEach((e) => {
          e.classList.remove("show");
        });
      }
    }, false);

    document.addEventListener('click', function() {
      if (document.body.classList.contains('show')) {
        document.body.classList.remove('show');
      }
    }, false);
  });


  var mainMenuLogout = document.querySelector('#main_menu_logout');
  if (mainMenuLogout) {
    mainMenuLogout.addEventListener('click', function (e) {
      mixpanel.track('Log Out Clicked');
      mixpanel.reset();
    }, false);
  }

  /* Sidebar more button toggle */
  var moreButton = document.querySelector('.drawer-link--more');
  var showSettings = document.querySelector('.drawer-link--toggle');
  if (moreButton && showSettings) {
    moreButton.addEventListener('click', function (e) {
      e.stopPropagation();
      var moreBounds = moreButton.getBoundingClientRect();

      moreButton.classList.toggle('drawer-link--active');
      if (showSettings.classList.contains('is-visible')) {
        showSettings.classList.remove('is-visible');
        setTimeout(function () {
          showSettings.classList.add('d-none');
          showSettings.style.transform = "";
        }, 300); // Assumes <= 300ms animation
        return;
      }
      
      // Initially display the popup, albeit while visually hidden
      showSettings.style.top = moreBounds.top + 'px';
      showSettings.classList.remove('d-none');
      
      // Check if popup is out of bounds
      var settingsBounds = showSettings.getBoundingClientRect();
      if (settingsBounds.height + settingsBounds.top > window.innerHeight) {
        showSettings.style.transform = `translateY(calc(-100% + ${moreBounds.height}px))`;
      }

      // Visually show the popup
      showSettings.classList.add('is-visible');
    }, false);

    document.body.addEventListener('click', function (e) {
      if (showSettings.classList.contains('is-visible')) {
        showSettings.classList.add('d-none');
        showSettings.classList.remove('is-visible');
        moreButton.classList.remove('drawer-link--active');
      }
    }, false);
  }

  var marketplaceLinks = document.querySelectorAll('.sso-user-closed-menubar, .sso-user-opened-menubar, .sso-user-dropdown-menu');
  if (marketplaceLinks.length > 0) {
    marketplaceLinks.forEach(button => {
      button.addEventListener('click', e => {
        var modal = document.getElementById('userPasswordModal');
        modal.style.display = 'block';
      }, false);
    });
  }

  var closeModalButton = document.querySelector('#password-modal-closeButton');
  if (closeModalButton) {
    closeModalButton.addEventListener('click', function (e) {
      var modal = document.getElementById("userPasswordModal");
      modal.style.display = "none";
      var passwordInput = document.getElementById("marketplace-password");
      passwordInput.value = "";
      var errorSpan = document.getElementById("marketplace-password-error");
      errorSpan.innerText = "";
    }, false);
  }
    
  var $marketplaceButton = document.querySelector('.drawer-link--marketplace');
  if ($marketplaceButton) {
    document.querySelectorAll('#main_menu_marketplace, .drawer-link--marketplace').forEach(button => {
      button.addEventListener('click', e => {
        e.stopPropagation();
        mixpanel.track('Marketplace Clicked', commonMixpanelProps);
      });
    });
  }

  /* App menu dropdown */
  var appToggle = document.querySelector('#app_toggle');
  if (appToggle) {
    var appToggleMenu = document.querySelector('#app_toggle_menu');
    if (appToggleMenu) {
      var appToggleLinks = appToggleMenu.querySelectorAll('a');

      appToggleMenu.addEventListener('click', function (e) {
        e.stopPropagation();
      }, false);
    }

    appToggle.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      document.body.classList.toggle('js-app-toggle-menu-open');
      document.body.classList.remove('js-company-menu-open');
      document.body.classList.remove('js-main-menu-open');
    }, false);

    if (appToggleLinks) {
      for (var i = 0, len = appToggleLinks.length; i < len; i++) {
        appToggleLinks[i].addEventListener('click', function (e) {
          document.body.classList.remove('js-app-toggle-menu-open');
        }, false);
      }
    }

    document.addEventListener('click', function () {
      document.body.classList.remove('js-app-toggle-menu-open');
    }, false);
  }

  /* Sample Data Toggle */
  var sampleToggle = document.querySelector('.close-sample-data');
  if (sampleToggle) {
    sampleToggle.addEventListener('click', function () {
      var id = Vue.prototype.$defaultCompanyId;
      var l = window.location;
      window.location = l.protocol + "//" + l.host + "/user_accesses/new?company_id=" + id;
    }, false);
  }

  /* Subscribe Button */
  var subscribeButton = document.querySelector("#subscribe_button");
  var resubscribeButton = document.querySelector("#resubscribe_button");

  if (subscribeButton) {
    subscribeButton.addEventListener('click', function (e) {
      e.stopPropagation();
      mixpanel.track('Subscribe Clicked', commonMixpanelProps);
    }, false);
  }

  if (resubscribeButton) {
    resubscribeButton.addEventListener('click', function (e) {
      e.stopPropagation();
      mixpanel.track('Resubscribe Clicked', commonMixpanelProps);
    }, false);
  }

  // TODO: Might be remove no_access page & all its methdos
  var backToSignInLinks = document.querySelectorAll('.back-to-sign-in-link, .back-to-sign-in-btn');
  if (backToSignInLinks.length > 0) {
    backToSignInLinks.forEach(button => {
      button.addEventListener('click', e => {
        setStateParamsToStorage();
      }, false);
    });
  }

  /* Dashboard view buttons */

  // DOM elements
  var $managementViewToggle = document.querySelector('#management_view');
  var $myCompanyViewToggle = document.querySelector('#my_company_view');
  var $managingCompaniesView = document.querySelector('.managing_container');
  var $workspaceMenuToggle = document.querySelector('#workspace_menu_toggle');

  // Constants for view types
  var DASHBOARD_VIEWS = {
    MANAGEMENT: 'management',
    COMPANY: 'company'
  };

  // Function to set view type in sessionStorage
  function setDashboardViewToStorage(viewType) {
    localStorage.setItem('dashboardView', viewType);
  }

  // Function to get view type from sessionStorage
  function getDashboardViewFromStorage() {
    return localStorage.getItem('dashboardView');
  }

  // Function to set the active view
  function setActiveView(viewToggle) {
    $managementViewToggle.classList.remove('active-view');
    $myCompanyViewToggle.classList.remove('active-view');
    viewToggle.classList.add('active-view');
  }

  // Function to handle view toggle click events
  function handleViewToggleClick(viewType, viewToggle) {
    setDashboardViewToStorage(viewType);
    setActiveView(viewToggle);
    document.dispatchEvent(new Event('dashboardViewTypeUpdated'));
  }

  // Function to hide the workspace menu toggle
  function hideWorkspaceMenuToggle() {
    if ($workspaceMenuToggle) {
      $workspaceMenuToggle.classList.remove('workspace-drawer');
      $workspaceMenuToggle.classList.add('hidden');
    }
  }

  // Function to show the workspace menu toggle
  function showWorkspaceMenuToggle() {
    if ($workspaceMenuToggle) {
      $workspaceMenuToggle.classList.remove('hidden');
      $workspaceMenuToggle.classList.add('workspace-drawer');
    }
  }

  // Check if DOM elements exist before proceeding
  if ($managementViewToggle && $myCompanyViewToggle) {
    // Get the current view type from sessionStorage
    var currentView = getDashboardViewFromStorage();

    // If no view is set, default to MANAGEMENT and store in sessionStorage
    if (!currentView) {
      currentView = DASHBOARD_VIEWS.MANAGEMENT;
      setDashboardViewToStorage(currentView);
    }

    // Set the active view based on the current view type
    if (currentView === DASHBOARD_VIEWS.MANAGEMENT) {
      setActiveView($managementViewToggle);
    } else {
      setActiveView($myCompanyViewToggle);
    }

    // Add event listeners for view toggles
    $managementViewToggle.addEventListener('click', () => {
      handleViewToggleClick(DASHBOARD_VIEWS.MANAGEMENT, $managementViewToggle);
      if ($managingCompaniesView) { $managingCompaniesView.classList.remove("hidden") };
      hideWorkspaceMenuToggle();
    });

    $myCompanyViewToggle.addEventListener('click', () => {
      handleViewToggleClick(DASHBOARD_VIEWS.COMPANY, $myCompanyViewToggle);
      if ($managingCompaniesView) { $managingCompaniesView.classList.add("hidden") };
      showWorkspaceMenuToggle();
    });
  };

  // Handle the managing companies view visibility based on the current view type
  const dashboardView = getDashboardViewFromStorage();
  if ($managingCompaniesView) {
    if (dashboardView === DASHBOARD_VIEWS.MANAGEMENT) {
      $managingCompaniesView.classList.remove("hidden");
      hideWorkspaceMenuToggle();
    } else {
      $managingCompaniesView.classList.add("hidden");
      showWorkspaceMenuToggle();
    }
  }

  setMicrosoftSSOSubdomainCookie();
});

function setCookie(name, val, useRootDomain) {
  // Setting the cookie for all subdomains is not reliable on localhost – defaulting to no value for that case
  const shouldSetRootDomainWithSubdomains = useRootDomain && Vue.prototype.$rootDomain !== "localhost";
  const rootDomain = shouldSetRootDomainWithSubdomains ? "; domain=." + Vue.prototype.$rootDomain : '';
  
  var expirationDate = new Date();
  expirationDate.setFullYear(expirationDate.getFullYear() + 1);

  var toSet = name + "=" + val + rootDomain + "; expires=" + expirationDate.toUTCString() + "; path=/";
  document.cookie = toSet;
}

function setMicrosoftSSOSubdomainCookie() {
  const companySubdomain = Vue.prototype.$currentCompanySubdomain;
  const rootDomain = Vue.prototype.$rootDomain;
  if (companySubdomain) {
    const cookieToSet = `ms_sso_company_subdomain=${companySubdomain};domain=.${rootDomain};path=/`;
    document.cookie = cookieToSet;
  }
}

function setStateParamsToStorage() {
  if (window.location.search) {
    const queryString = window.location.search;
    const params = new URLSearchParams(queryString);
    const redirectionPath = params.get('redirection_path');
    const companySubdomain = params.get('sub');
    sessionStorage.setItem('redirection_state', `${redirectionPath},${companySubdomain}`);
  }
}

function whichTransitionEvent() {
  var el = document.createElement('fake');
  var transitions = {
    'WebkitTransition': 'webkitTransitionEnd',
    'MozTransition': 'transitionend',
    'transition': 'transitionend'
  };

  for (var t in transitions) {
    if (el.style[t] !== undefined) {
      return transitions[t];
    }
  }
}

/**
 * Element.closest() polyfill
 * https://developer.mozilla.org/en-US/docs/Web/API/Element/closest#Polyfill
 */
if (!Element.prototype.closest) {
  if (!Element.prototype.matches) {
    Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
  }
  Element.prototype.closest = function (s) {
    var el = this;
    var ancestor = this;
    if (!document.documentElement.contains(el)) return null;
    do {
      if (ancestor.matches(s)) return ancestor;
      ancestor = ancestor.parentElement;
    } while (ancestor !== null);
    return null;
  };
}

/* Ignore JavaScript Errors */

// ResizeObserver loop limit error is raised by only Chrome browser on resizing screen.
// It is safe to ignore this error.
const errorsToIgnore = ['ResizeObserver loop limit exceeded'];

window.onerror = function (message) {
  if (errorsToIgnore.includes(message)) {
    return true;
  }
  return false;
}

function validatePassword() {
  var passwordInput = document.getElementById("marketplace-password");
  var password = passwordInput.value;

  if (password.length < 8) {
    var errorSpan = document.getElementById("marketplace-password-error");
    errorSpan.innerText = "Password must be at least 8 characters long.";
    return false;
  }

  if (password.indexOf(' ') >= 0) {
    var errorSpan = document.getElementById("marketplace-password-error");
    errorSpan.innerText = "Password cannot contain spaces.";
    return false;
  }

  if (!password.match(/^(?=.*[A-Za-z])(?=.*\d)[\w.,!@#$%^&*()-+=]{8,}$/)) {
    var errorSpan = document.getElementById("marketplace-password-error");
    errorSpan.innerText = "Password must contain at least one letter and one number.";
    return false;
  }

  return true;
}
