// TODO: Some of these variables should probably live under a global $workspace object
/* eslint-disable */
window.$company = null;
window.$workspace = null;
var $selectedCompany = null;
var $permissions = null;
var companyOptions = null;
var $workspaceOptions = null;
var $userCompanies = null;
var totalCompaniesCount = null;
var headerCompanies = null;
var remainingCompanies = null;
var workspacesLoaded = false;
var $workspacesUpdatedBind = null;
var companyTrigger = false;
var workspaceTrigger = false;
var loadingPermissions = false;
var loadingCompany = false;
var $workspaceSelectedFromDropdown = false;

const getBasicHeaders = () => {
  return {
    "X-Key-Inflection": "camel",
    "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').attributes['content'].value,
  };
};

const getWorkspaceHeaders = (company = window.$company, workspace = window.$workspace) => {
  return {
    ...getBasicHeaders(),
    "X-Genuity-Company-Id": (company ? company.id : ""),
    "X-Genuity-Workspace-Id": (workspace ? workspace.id : ""),
  };
};

const companyModule = () => {
  const path = document.location.pathname;
  if (path.startsWith('/help_tickets') || isMenuUrl(path)) {
    return 'HelpTicket';
  } else if (path.startsWith('/contracts')) {
    return 'Contract';
  } else if (path.startsWith('/telecom')) {
    return 'TelecomService';
  } else if (path.startsWith('/vendor')) {
    return 'Vendor';
  } else if (path.startsWith('/managed_asset')) {
    return 'ManagedAsset';
  } else if (path.startsWith('/reports')) {
    const search = document.location.search;
    if (search.includes('help_tickets')) {
      return 'HelpTicket';
    } else if (search.includes('contracts')) {
      return 'Contract';
    } else if (search.includes('vendors')) {
      return 'Vendor';
    }
    return 'CompanyUser';
  } else {
    return 'CompanyUser';
  }
};

const isMenuUrl = (path) => {
  return (path.startsWith('/profile') || path.startsWith('/gamification') ||
    path.startsWith('/help') || path.startsWith('/guides'));
}

const updatePermissions = async () => {
  const company = getCompanyFromStorage();
  const isHelpCenter = location.pathname.startsWith('/help_center')
  const isPublicFaqs = location.pathname.includes('/faqs') && !location.pathname.includes('/help_tickets/faqs');
  const isPublicArticles = location.pathname.includes('/knowledge_base');
  if (isHelpCenter || isPublicFaqs || isPublicArticles) {
    return;
  }
  $permissions = null;
  if (!company) { return }

  if (Vue.prototype.$superAdminUser) {
    $permissions = {
      companyUser: { root: ['write'] },
      contract: { root: ['write'] },
      helpTicket: company.workspaceIds.reduce((a, v) => ({ ...a, [v]: ['write']}), {}),
      managedAsset: { root: ['write'] },
      monitoring: { root: ['write'] },
      telecomService: { root: ['write'] },
      vendor: { root: ['write'] },
      isSuperAdmin: true
    };
  } else {
    loadingPermissions = true;
    const fetchPermissions = await fetch("/expanded_permissions.json", { headers: getWorkspaceHeaders(company) });
    const pathname = document.location.pathname;
    let req_btn = document.getElementById('req-permission-btn')
    const contentType = fetchPermissions.headers.get("content-type") || "";
    if (contentType.includes("application/json")) {
      $permissions = await fetchPermissions.json();
    } else {
      $permissions = await fetchPermissions;
    }
    loadingPermissions = false;

    if (!pathname.endsWith("confirm_new_email") && !pathname.endsWith("pricing") && !pathname.endsWith("denied") && ($permissions && !$permissions[permissionIdx]) && !req_btn) {
      const idx = pathname.substring(1).indexOf("/") + 2;
      document.location.href = pathname.substring(0, idx) + "denied";
    }
  }
  emitCompanyChange();
};

const permissionIdx = (() => {
  let idx = companyModule();
  idx = idx[0].toLowerCase() + idx.substring(1);
  return idx;
})();

const setUpDropdowns = () => {
  $selectedCompany = window.$company;
  setCompanyLinksLoading();
  if (isCompanySelectOpen()) {
    fetchCompanyOptions();
  } else if (isWorkspaceSelectOpen()) {
    fetchWorkspaceOptions();
  }
};

const onCompanyDropdownClick = () => {
  setUpDropdowns();
};

const onWorkspaceDropdownClick = () => {
  setUpDropdowns();
};

const onMangingCompaniesDropdownClick = () => {
  toggleManagingCompaniesMenu();
};

document.addEventListener('DOMContentLoaded', () => {
  const companyMenuToggle = document.getElementById("company_menu_toggle");
  const managingCompanyMenuToggle = document.getElementById("managing_company_menu_toggle");
  if (!companyMenuToggle) { return; }

  if (Vue.prototype.$currentUserId) {
    emitCompanyChange();
  }

  loadCompanyFromStorage();
  enableManagingCompanySelection();
  companyTrigger = true;
  companyMenuToggle.addEventListener('click', onCompanyDropdownClick);
  if (managingCompanyMenuToggle) {
    managingCompanyMenuToggle.addEventListener('click', onMangingCompaniesDropdownClick);
  }

  const companySearch = document.querySelector("#search_company");
  if (companySearch) {
    const handleCompanySearch = debounce(updateCompanySearch, 500);
    companySearch.value = null;
    companySearch.addEventListener("input", handleCompanySearch);
  }

  const managingCompanySearch = document.querySelector("#search_managing_company");
  if (managingCompanySearch) {
    const handleManagingCompanySearch = debounce(updateManagingCompanySearch, 500);
    managingCompanySearch.value = null;
    managingCompanySearch.addEventListener("input", handleManagingCompanySearch);
  }

  const workspaceMenuToggle = document.getElementById('workspace_menu_toggle');
  if (workspaceMenuToggle) {
    workspaceMenuToggle.addEventListener('click', onWorkspaceDropdownClick);
  }

  const closeCompany = document.getElementById("close_company_select");
  if (closeCompany) {
    closeCompany.addEventListener('click', closeAllMenus);
  }

  const closeManagingCompany = document.getElementById("close_managing_company_select");
  if (closeManagingCompany) {
    closeManagingCompany.addEventListener('click', closeAllMenus);
  }

  const closeWorkspace = document.getElementById('close_workspace_select');
  if (closeWorkspace) {
    closeWorkspace.addEventListener('click', closeAllMenus);
  }

  fetchUserCompanies();
  preventClickEventForManagingDropdown();
  enableClearAllBtnClick();
  enableSelectAllBtnClick();

  document.addEventListener('click', closeAllMenus);

  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      //if esc key was not pressed in combination with ctrl or alt or shift
      const isNotCombinedKey = !(event.ctrlKey || event.altKey || event.shiftKey);
      if (isNotCombinedKey) {
        closeAllMenus();
      }
    }
  });
});

const isDropdownOpen = (dropdownId) => {
  const dropdownMenu = document.getElementById(dropdownId);
  return (dropdownMenu && dropdownMenu.classList.contains("show"));
};

const isCompanySelectOpen = () => {
  return isDropdownOpen("company_select_menu");
};

const isWorkspaceSelectOpen = () => {
  return isDropdownOpen("workspace_select_menu");
};

const hideManagingCompaniesMenu = () => {
  const dropdownMenu = document.getElementById("managing_company_select_menu");
  if (dropdownMenu) {
    dropdownMenu.classList.remove("show");
  }
};

const toggleManagingCompaniesMenu = () => {
  const dropdownMenu = document.getElementById("managing_company_select_menu");
  if (dropdownMenu) {
    dropdownMenu.classList.toggle("show");
  }
};

const hideCompanyModal = () => {
  const companyMenu = document.getElementById("company_select_menu");
  if (companyMenu) {
    companyMenu.classList.remove('show');
  }
};

const hideWorkspaceModal = () => {
  const workspaceMenu = document.getElementById('workspace_select_menu');
  if (workspaceMenu) {
    workspaceMenu.classList.remove('show');
  }
};

const hideWorkspaceSettingsMenus = () => {
  document.querySelectorAll(".workspace-settings-menu").forEach((e) => {
    const settingsLinkElement = e.parentElement.querySelector('.workspace-settings-link');

    if (e.classList.contains("show")) {
      e.classList.remove("show");
    }

    if (settingsLinkElement.classList.contains("active")) {
      settingsLinkElement.classList.remove("active");
    }
  });
};

const closeAllMenus = () => {
  hideCompanyModal();
  hideWorkspaceModal();
  hideWorkspaceSettingsMenus();
  hideManagingCompaniesMenu();

  // Just in case, close all other header menus
  const appToggleMenu = document.getElementById("app_toggle_menu");
  if (appToggleMenu) {
    appToggleMenu.classList.remove("show");
  }
  const mainMenu = document.getElementById("main_menu");
  if (mainMenu) {
    mainMenu.classList.remove("show");
  }
  const settingsMenu = document.getElementById("settings_menu");
  if (settingsMenu) {
    settingsMenu.classList.remove("show");
  }
};

const setCompanyLinksLoading = () => {
  const pinnedLinksDiv = document.querySelector("#company_select_pinned_menu--links");
  const otherLinksDiv = document.querySelector("#company_select_other_menu--links");
  pinnedLinksDiv.innerHTML = "<i>Loading&hellip;</i>";
  otherLinksDiv.innerHTML = "<i>Loading&hellip;</i>";
}

const clearCompanyLinks = () => {
  const pinnedLinksDiv = document.querySelector("#company_select_pinned_menu--links");
  const otherLinksDiv = document.querySelector("#company_select_other_menu--links");
  pinnedLinksDiv.innerHTML = null;
  otherLinksDiv.innerHTML = null;
};

const companyAccess = async (companyData, currentModule) => {
  const url = `/user_accesses.json?company_id=${companyData.id}&redirect_route=${currentModule}&switch_company=true`;
  const fetchUserAccess = await fetch(encodeURI(url), { headers: getBasicHeaders() });
  return await fetchUserAccess.json();
};

const redirectToCompanySubdomain = async (companyData) => {
  const currentPath = window.location.pathname;
  const pathSegments = currentPath.split('/');
  const index = pathSegments.findIndex(segment => /^\d+$/.test(segment));  
  const pathToUse = index !== -1 ? pathSegments.slice(0, index ).join('/') : currentPath;
  const accessData = await companyAccess(companyData, pathToUse);
  if (accessData.url) {
    location.href = accessData.url;
  }
};

const onCompanySelection = (e) => {
  e.preventDefault();
  let link = e.currentTarget;
  if (link.classList.contains("company-select--selected")) {
    return;
  }
  redirectToCompanySubdomain(JSON.parse(link.dataset.company));
  closeAllMenus();
};

const onPinCompany = async (companyId, switchCompany = false) => {
  const url = new URL('/update_pinned_company_status.json', window.location.origin);
  url.searchParams.append('company_id', companyId);

  try {
    const fetchPinnedIds = await fetch(url, { headers: getBasicHeaders(), method: 'POST' });
    const pinnedIds = await fetchPinnedIds.json();

    const options = {
      companyOptions: companyOptions,
      pinnedCompanyIds: pinnedIds.pinnedCompanyIds,
    };
    switchCompany ? window.location.reload() : setCompanyOptions(options);
    fetchUserCompanies();

  } catch (error) {
    console.error('An error occurred during pinning company:', error);
  }
};

const addCompanyLink = async (company, pinnedIds) => {
  const element = document.createElement("a");
  const spaceCount = company.workspaceIds.length;
  const isPinned = pinnedIds.includes(company.id);
  const companySeparatorElement = document.getElementById('company_separator');

  companySeparatorElement.classList.toggle("d-block", pinnedIds.length !== 0);

  const linksDiv = isPinned
    ? document.querySelector("#company_select_pinned_menu--links")
    : document.querySelector("#company_select_other_menu--links");

  element.setAttribute("data-company", JSON.stringify(company));
  element.setAttribute("data-company-id", company.id);
  element.setAttribute("data-company-name", company.name);
  element.setAttribute("data-workspaces-count", `${spaceCount} ${spaceCount === 1 ? 'workspace' : 'workspaces'}`);
  element.classList.add("btn", "d-block", "btn-sm","btn-link", "text-secondary", "text-left", "py-2", "mb-2", "rounded", "company-select");
  element.classList.add(isPinned ? "pinned" : "unpinned");

  const span = document.createElement("span");
  span.classList.add("mr-2");

  let img = null;
  img = document.createElement("img");
  img.classList.add("company-switch-logo", "company-img", "logo-outline", "no-shadow");
  img.setAttribute("src", company.url);
  img.setAttribute("style", "height: 1.5rem; width: 1.5rem;");

  span.appendChild(img);
  element.appendChild(span);
  element.addEventListener('click', onCompanySelection);

  renderCompanyName(company.name, element);

  if (company.reseller) {
    let parentCompanyIcon = document.createElement("i");
    parentCompanyIcon.classList.add("icon", "genuicon-parent-company", "ml-3");
    element.appendChild(parentCompanyIcon);
  }

  let spacesBadge = document.createElement("div");
  spacesBadge.setAttribute("data-workspaces-count", `${spaceCount} ${spaceCount === 1 ? 'workspace' : 'workspaces'}`);
  spacesBadge.classList.add("company-select__spaces-badge");
  element.appendChild(spacesBadge);
  
  const pinButton = document.createElement("button");
  pinButton.classList.add("image-circle-company", "border-0", "rounded-circle");

  pinButton.onclick = (event) => {
    event.stopPropagation();
    event.preventDefault();

    onPinCompany(company.id); 
  };

  const pinImage = document.createElement("img");
  const pinImageSrc = document.querySelector('.company-selection-wrap').getAttribute('data-pin-image-src');
  pinImage.classList.add(isPinned ? "pin-company" : "other-company");
  pinImage.setAttribute("src", pinImageSrc);

  const pinTooltipSpan = document.createElement("span");
  pinTooltipSpan.classList.add("company-tooltip", "company-tooltip--left");
  pinTooltipSpan.textContent = isPinned ? "Unpin this company" : "Pin this company";
  
  pinButton.appendChild(pinImage);
  pinButton.appendChild(pinTooltipSpan);

  element.appendChild(pinButton);
  linksDiv.appendChild(element);
};

const clearSelectedCompanies = () => {
  const selected = document.querySelectorAll(".company-select--selected");
  selected.forEach((i) => {
    i.classList.remove("company-select--selected");
  });
};

const onWorkspaceSelection = (e) => {
  e.preventDefault();
  $workspaceSelectedFromDropdown = true
  const json = e.currentTarget.dataset.workspace;
  workspacesLoaded = false;
  window.sessionStorage.removeItem("companyFilter");
  window.sessionStorage.removeItem("workspaceFilter");
  setCompanyToStorage($selectedCompany);
  setWorkspaceToStorage(json);
  closeAllMenus();
};

const addWorkspaceLink = (workspaceLink) => {
  const company = getCompanyFromStorage();
  const linksDiv = document.querySelector("#company_select_menu--results");
  const workspaceLinkElement = document.createElement("div");
  const workspaceLabel = document.createElement("span");
  const rightWrapper = document.createElement("span");
  const settingsIcon = document.createElement("span");
  const rigthDropdownWrapper = document.createElement("div");
  const gotoSettingsMenuItem = document.createElement("a");
  const setDefaultMenuItem = document.createElement("a");

  // construct workspaceLinkElement
  workspaceLinkElement.setAttribute("data-workspace", JSON.stringify(workspaceLink));
  workspaceLinkElement.setAttribute("data-workspace-name", workspaceLink.name);
  workspaceLinkElement.classList.add("btn", "btn-sm", "btn-link", "text-secondary", "text-left", "mb-2", "rounded", "workspace-link", "d-flex", "justify-content-between")
  workspaceLabel.classList.add("workspace-link-name")
  workspaceLabel.innerHTML = workspaceLink.name;

  const defaultLabel = document.createElement("span");
  defaultLabel.innerHTML = "Default";
  defaultLabel.classList.add("workspace-default-label");


  // show active check if on active workspace
  if (window.$workspace && workspaceLink.id === window.$workspace.id || !window.$workspace && workspaceLink.default) {
    workspaceLinkElement.classList.add("workspace-link--active");
    workspaceLinkElement.classList.add("workspace-link--selected", "workspace-link--active");
  }

  // construct settingsIcon and rigthDropdownWrapper content
  if(hasWriteAllPermission(workspaceLink.id)) {
    settingsIcon.classList.add("nulodgicon-cog", "workspace-settings-link");
    settingsIcon.setAttribute("data-tc-workspace-setting-icon", "");
    setDefaultMenuItem.classList.add("make-default-item", "menu-dropdown-item", "menu-dropdown-item--small", "menu-dropdown-item--light");
    setDefaultMenuItem.innerHTML = 'Make Default';
    setDefaultMenuItem.setAttribute("data-tc-make-default", "");
    gotoSettingsMenuItem.classList.add("goto-settings-item", "menu-dropdown-item", "menu-dropdown-item--small", "menu-dropdown-item--light");
    gotoSettingsMenuItem.innerHTML = 'Workspace Settings';
    gotoSettingsMenuItem.setAttribute("data-tc-workspace-setting", "");
    rigthDropdownWrapper.classList.add("workspace-settings-menu", "header-menu");
  }

  // show default label if on default workspace
  if (window.$workspace && workspaceLink.default && hasWriteAllPermission(workspaceLink.id)) {
    rightWrapper.appendChild(defaultLabel);
    setDefaultMenuItem.innerHTML = 'Remove As Default';
  }

  // insert child elements to the workspaceLinkElement
  rigthDropdownWrapper.appendChild(setDefaultMenuItem);
  rigthDropdownWrapper.appendChild(gotoSettingsMenuItem);
  rightWrapper.appendChild(rigthDropdownWrapper);
  if(hasWriteAllPermission(workspaceLink.id)) {
    rightWrapper.appendChild(settingsIcon);
  }
  workspaceLinkElement.appendChild(workspaceLabel);
  workspaceLinkElement.appendChild(rightWrapper);

  // Setup our click handlers
  workspaceLinkElement.addEventListener('click', onWorkspaceSelection);
  if(hasWriteAllPermission(workspaceLink.id)) {
    settingsIcon.addEventListener('click', workspaceSettingsMenuClick);
  }
  setDefaultMenuItem.addEventListener('click', setUnsetDefaultWorkspaceClick);
  gotoSettingsMenuItem.addEventListener('click', gotoWorkspaceSettingsClick);

  // Finally, add workspaceLinkElement to the list
  if (linksDiv) {
    linksDiv.appendChild(workspaceLinkElement);
  }
};

const workspaceSettingsMenuClick = (e) => {
  e.stopPropagation();

  const settingsMenu = e.target.parentElement.querySelector('.workspace-settings-menu');
  const settingsMenuWasOpen = settingsMenu.classList.contains("show");
  hideWorkspaceSettingsMenus();

  if (!settingsMenu.classList.contains("show") && !settingsMenuWasOpen) {
    settingsMenu.classList.add("show");
  }

  if (!e.target.classList.contains("active") && !settingsMenuWasOpen) {
    e.target.classList.add("active");
  }
};

const setUnsetDefaultWorkspaceClick = (e) => {
  const company = getCompanyFromStorage();
  const workspaceLinkElement = e.target.parentElement.parentElement.parentElement;
  const workspace = workspaceLinkElement.getAttribute('data-workspace');
  const workspaceObj = JSON.parse(workspace);
  const url = workspaceObj.default ? '/unset_default_workspace.json' : `/set_default_workspace.json?workspace=${workspace}`;
  e.target.setAttribute("href",  fetch(encodeURI(url), { headers: getWorkspaceHeaders(company), method: 'PUT' }));

  workspaceLinkElement.click();
};

const gotoWorkspaceSettingsClick = (e) => {
  e.stopPropagation();

  const workspaceLinkElement = e.target.parentElement.parentElement.parentElement;
  const workspaceObj = JSON.parse(workspaceLinkElement.getAttribute('data-workspace'));
  if (workspaceObj) {
    e.target.href= `/help_tickets/workspaces?edit=${workspaceObj.id}`;
  }
};

const clearWorkspaceDropdowns = () => {
  const workspaceNav = document.querySelector('#company_select_menu--results');
  workspaceNav.innerHTML = "";
};

const selectCompanyOption = (id) => {
  clearSelectedCompanies();
  let selected;
  if (!id || id === 'all') {
    $selectedCompany = null;
    selected = document.querySelector(`.company-select[data-company-id="all"]`);
    selected.classList.add("company-select--selected");
    document.getElementById(`selected_company_name`).innerText = '';
    setCompanyToStorage(null);
  } else {
    selected = document.querySelector(`.company-select[data-company-id="${id}"]`);
    if (selected) {
      $selectedCompany = JSON.parse(selected.dataset.company)
      document.getElementById(`selected_company_name`).innerText = `${$selectedCompany.name}`;
    }
  }
  if (window.$company && window.$company.id == id && selected) {
    selected.classList.add('company-select--active', 'company-select--selected');
  }
};

const setCompanyOptions = (options) => {
  const pinnedCompanyIds = options['pinnedCompanyIds'] || [];
  companyOptions = options.companyOptions;

  clearCompanyLinks();
  if (companyOptions) {
    companyOptions.forEach((o) => {
      addCompanyLink(o, pinnedCompanyIds);
    });
    if ($selectedCompany) {
      selectCompanyOption($selectedCompany.id);
    }
  }
};

const enableClearAllBtnClick = () => {
  const clearAllBtn = document.getElementById('clear_all_btn');
  if (clearAllBtn) {
    clearAllBtn.onclick = (event) => {
      event.stopPropagation();
      event.preventDefault();
      clearAllSelectedCompanies();
      updateCompanyFilterStatus();
      dispatchCompanySelectionEvent();
    };
  }
};

const enableSelectAllBtnClick = () => {
  const selectAllBtn = document.getElementById('select_all_btn');
  if (selectAllBtn) {
    selectAllBtn.onclick = (event) => {
      event.stopPropagation();
      event.preventDefault();
      selectAllCompanies();
      updateCompanyFilterStatus();
      dispatchCompanySelectionEvent();
    };
  }
};

const clearAllSelectedCompanies = () => {
  clearManagingSearch();
  updateSelectedCompanies([]);

  userCompaniesIds().forEach(companyId => {
    const dropdownCompany = document.querySelector(`.managing-company-dropdown-link[data-company-id="${companyId}"]`);
    const headerCompany = document.querySelector(`.managing-company-container[data-company-id="${companyId}"]`);
    if (dropdownCompany) {
      dropdownCompany.classList.remove('company-select--selected');
      dropdownCompany.classList.remove('company-select--active');
    };
    if (headerCompany) { headerCompany.classList.remove("selected-company") };
  });

  const allBtn = document.getElementById('all-button');
  if (allBtn) { allBtn.classList.remove("selected-company") };
};

const selectAllCompanies = () => {
  updateSelectedCompanies(userCompaniesIds());
  loadSelectedCompanies();
};

const userCompaniesIds = () => {
  return $userCompanies.map(c => c.id);
};

const dispatchCompanySelectionEvent = () => {
  document.dispatchEvent(new Event('companiesSelectionChanged'));
};

const updateCompanyFilterStatus = () => {
  const companyFilterContainer = document.getElementById('company-filtering-status');
  const selectedCompanies = getSelectedCompanies().length || totalCompaniesCount;
  if (companyFilterContainer) {
    companyFilterContainer.innerHTML = `Filtering ${selectedCompanies} of ${totalCompaniesCount}`;
  }
};

const preventClickEventForManagingDropdown = () => {
  const menu = document.getElementById('managing_company_select_menu');
  if (menu) {
    menu.addEventListener('click', function (e) {
      e.stopPropagation();
    }, false);
  }
};

const setUserCompanyOptions = (options) => {
  const pinnedCompanyIds = options['pinnedCompanyIds'] || [];
  const userAllcompanies = options.companyOptions.filter(company => company.subdomain !== 'sample');
  const pinnedCompanies = userAllcompanies.filter((c) => pinnedCompanyIds.includes(c.id));
  const unPinnedCompanies = userAllcompanies.filter((c) => !pinnedCompanyIds.includes(c.id));
  $userCompanies = pinnedCompanies.concat(unPinnedCompanies);

  if (!totalCompaniesCount) {
    totalCompaniesCount = $userCompanies.length;
    updateCompanyFilterStatus();
  };

  if ($userCompanies) {
    const companiesContainer = document.querySelector("#companies_container");
    const dropdownMenuLinks = document.querySelector("#managing_company_select_menu--links");
    const pinnedCompaniesContainer = document.querySelector("#managing_company_select_pinned_menu--links");
    const companySeparatorElement = document.getElementById('managing_company_separator');
    const searchValue = document.querySelector("#search_managing_company").value;
    if (!companiesContainer || !dropdownMenuLinks) { return; }

    // Clear existing content
    companiesContainer.innerHTML = '';

    if (!headerCompanies) {
      headerCompanies = $userCompanies.slice(0, 3);
    }

    if (searchValue == '') {
      remainingCompanies = $userCompanies.slice(3);
    }

    headerCompanies.forEach((company, index) => {
      const zIndex = 10 - index;
      addCompanyOption(company, companiesContainer, false, zIndex);
    });

    pinnedCompaniesContainer.innerHTML = null;
    dropdownMenuLinks.innerHTML = null;

    if (remainingCompanies.length > 0) {
      addAllCompaniesBtn(remainingCompanies.length);
    };

    if (searchValue == '' && pinnedCompanies.length > 0) {
      pinnedCompanies.forEach(company => {
        addCompanyOption(company, pinnedCompaniesContainer, true);
      });

      companySeparatorElement.classList.add("d-block");
    } else {
      companySeparatorElement.classList.remove("d-block");
    }

    const allCompanies = searchValue == '' ? unPinnedCompanies : $userCompanies;

    allCompanies.forEach(company => {
      addCompanyOption(company, dropdownMenuLinks, true);
    });

    titleTooltips('managing-data-title-tooltip');
    loadSelectedCompanies();
  }
};

const deactiveRemainingBadge = () => {
  const allBtn = document.getElementById('all-button');
  const isElementActive = remainingCompanies.some(function(company) {
    const element = document.querySelector(`.managing-company-dropdown-link[data-company-id="${company.id}"]`);
    if (element) {
      return !element.classList.contains("company-select--selected");
    }
  });
  if (isElementActive && allBtn) {
    allBtn.classList.remove("selected-company");
  }
};

const onCompanyClick = (companyId, dropdown) => {
  const allBtn = document.getElementById('all-button');
  const dropdownCompany = document.querySelector(`.managing-company-dropdown-link[data-company-id="${companyId}"]`);
  const headerCompany = document.querySelector(`.managing-company-container[data-company-id="${companyId}"]`);

  if (dropdown) {
    if (headerCompany) {
      headerCompany.classList.toggle("selected-company");
    }

    if (dropdownCompany) {
      dropdownCompany.classList.toggle('company-select--selected');
      dropdownCompany.classList.toggle('company-select--active');
      if (!headerCompany && allBtn) {
        allBtn.classList.add("selected-company");
      }
    };

    // Check if at least one element has the active classes
    deactiveRemainingBadge(companyId);
  } else {
    if (dropdownCompany) {
      dropdownCompany.classList.toggle('company-select--selected');
      dropdownCompany.classList.toggle('company-select--active');
    }
    if (headerCompany) {
      headerCompany.classList.toggle("selected-company");
    }
  }

  // Toggle the selection status
  const isSelected = getSelectedCompanies().includes(companyId);
  if (isSelected) {
    removeSelectedCompany(companyId);
  } else {
    addSelectedCompany(companyId);
  }

  updateCompanyFilterStatus();

  // Trigger an event
  dispatchCompanySelectionEvent();
};

const addSelectedCompany = (companyId) => {
  // Add the company to the selected companies list in session storage
  const selectedCompanies = getSelectedCompanies();
  selectedCompanies.push(companyId);
  sessionStorage.setItem('selectedCompanies', JSON.stringify(selectedCompanies));
};

const removeSelectedCompany = (companyId) => {
  // Remove the company from the selected companies list in session storage
  const selectedCompanies = getSelectedCompanies().filter(id => id != companyId);
  sessionStorage.setItem('selectedCompanies', JSON.stringify(selectedCompanies));
};

const getSelectedCompanies = () => {
  // Get the selected companies from session storage
  return JSON.parse(sessionStorage.getItem('selectedCompanies')) || [];
};

const updateSelectedCompanies = (companies = []) => {
  // update the selected companies in session storage
  sessionStorage.setItem('selectedCompanies', JSON.stringify(companies));
}

const loadSelectedCompanies = () => {
  // Load selected companies from session storage
  const selectedCompanies = getSelectedCompanies();
  let isDropdownCompanySelected = false;
  selectedCompanies.forEach(companyId => {
    // Apply the selected-company class to the corresponding element
    const dropdownCompany = document.querySelector(`.managing-company-dropdown-link[data-company-id="${companyId}"]`);
    const headerCompany = document.querySelector(`.managing-company-container[data-company-id="${companyId}"]`);
    if (dropdownCompany) {
      isDropdownCompanySelected = true;
    }
    if (dropdownCompany) {
      dropdownCompany.classList.add('company-select--selected');
      dropdownCompany.classList.add('company-select--active');
    }
    if (headerCompany) {
      headerCompany.classList.add("selected-company");
    }
  });

  if (isDropdownCompanySelected) {
    const allBtn = document.getElementById('all-button');
    if (allBtn) {
      allBtn.classList.add("selected-company");
    }
  }
  deactiveRemainingBadge();
};

const addAllCompaniesBtn = (count) => {
  const linksDiv = document.querySelector("#companies_container");

  const element = document.createElement("a");
  const span = document.createElement("span");
  element.setAttribute("style", `z-index: 3;`);
  element.classList.add("mx-n1", "managing-company-container", "text-center");
  element.setAttribute("id", "all-button");
  element.setAttribute("title", "Filter by Company");
  element.setAttribute("managing-data-title-tooltip", "bottom");
  element.setAttribute("data-hide-tooltip-class", "show");
  element.setAttribute("data-tooltip-spacing", "24");
  element.setAttribute("data-hide-tooltip-selector","#managing_company_select_menu");
  span.classList.add("smallest");
  // Set the inner text
  span.innerText = `+${count}`;

  // Append the span to the element
  element.appendChild(span);

  linksDiv.appendChild(element);
};

const addCompanyOption = (company, container, dropdown, zIndex = null) => {
  const element = document.createElement("a");
  element.setAttribute("data-company-id", company.id);
  element.classList.add("mx-n1", "managing-company-container")

  const img = document.createElement("img");
  img.classList.add("managing-company-logo--icon", "company-img", "logo-outline", "no-shadow");
  img.setAttribute("src", company.url);
  img.setAttribute("style", "height: 1.5rem; width: 1.5rem;");

  if (dropdown) {
    const companyElement = document.createElement("a");
    const imgSpan = document.createElement("span");
    imgSpan.classList.add("mr-2");
    img.setAttribute("style", "height: 1.5rem; width: 1.5rem; position: relative; top: -3px;");
    imgSpan.appendChild(img);
    companyElement.appendChild(imgSpan);

    companyElement.classList.add("managing-company-dropdown-link")
    companyElement.setAttribute("data-company-id", company.id);
    companyElement.setAttribute("data-company-name", company.name);
    companyElement.classList.add("btn", "d-block", "btn-sm","btn-link", "text-secondary", "text-left", "py-2", "mb-2", "rounded", "company-select");

    renderCompanyName(company.name, companyElement);

    companyElement.onclick = (event) => {
      event.stopPropagation();
      event.preventDefault();
      onCompanyClick(company.id, true);
    };

    container.appendChild(companyElement);
  } else {
    element.setAttribute("style", `z-index: ${zIndex};`);
    element.setAttribute("title", `Filter by ${company.name}`);
    element.setAttribute("managing-data-title-tooltip", "bottom");
    element.setAttribute("data-hide-tooltip-class", "show");
    element.setAttribute("data-tooltip-spacing", "24");
    const span = document.createElement("span");
    span.classList.add("d-flex");
    span.appendChild(img);
    element.appendChild(span);

    element.onclick = (event) => {
      event.stopPropagation();
      event.preventDefault();
      onCompanyClick(company.id, false, false);
    };

    container.appendChild(element);
  }
};

const fetchCompaniesInfo = async (searchValue, setOptions) => {
  let url = '/company_options.json?switch_company=true&';
  if (searchValue && searchValue.length > 0) {
    url = `${url}search=${searchValue}`;
  }

  const fetchOptions = await fetch(encodeURI(url), { headers: getBasicHeaders() });
  const options = await fetchOptions.json();
  setOptions(options);
};

const fetchUserCompanies = async () => {
  const searchValue = document.querySelector("#search_managing_company").value;
  await fetchCompaniesInfo(searchValue, setUserCompanyOptions);
};

const fetchCompanyOptions = async () => {
  const searchValue = document.querySelector("#search_company").value;
  await fetchCompaniesInfo(searchValue, setCompanyOptions);
};

const loadCompanyFromStorage = () => {
  const xhr = new XMLHttpRequest();
  workspacesLoaded = false
  window.$company = null;
  window.$workspace = null;
  companyOptions = null;
  $workspaceOptions = null;

  let company = getCompanyFromStorage();

  if (company) {
    setCompanyToStorage(company);
    emitCompanyChange();
    setCompanyAndWorkspaceLabel();
  }
  else if(!loadingCompany) {
    loadingCompany = true;
    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        let currentCompany = xhr.responseText;
        if (currentCompany) {
          company = currentCompany;
        }
        if (company) {
          setCompanyToStorage(company);
          emitCompanyChange();
        }
        if (!company) {
          company = loadCompanyFromCookie();
        }
        setCompanyAndWorkspaceLabel();
        loadingCompany = false;
      }
    };

    const { protocol, host } = document.location;
    const url = `${protocol}//${host}/company/current_company.json`;
    xhr.open('GET', encodeURI(url), true);
    xhr.setRequestHeader('X-CSRF-Token', document.querySelector('meta[name="csrf-token"]').attributes['content'].value);
    xhr.setRequestHeader('X-Key-Inflection', 'camel');
    xhr.send();
  }
}

const translateJson = (obj) => {
  if (typeof obj === "string") {
    return obj;
  } else {
    return JSON.stringify(obj);
  } 
}

const parseJsonString = (string) => {
  try {
    return JSON.parse(string);
  } catch (error) {
    return null;
  }
}

const enableCompanySelection = () => {
  const toggle = document.querySelector("#company_menu_toggle");
  if (toggle) {
    toggle.dataset.dropdownMenu = "company_select_menu";
  }
};

const enableManagingCompanySelection = () => {
  const toggle = document.querySelector("#managing_company_toggle");
  if (toggle) {
    toggle.dataset.dropdownMenu = "managing_company_select_menu";
  }
};

const setCompanyToStorage = (obj) => {
  const json = translateJson(obj);
  const storage = window.sessionStorage;
  const previousCompany = getCompanyFromStorage();

  if (!json) {
    storage.removeItem("company");
    // We also need to remove the workspace, since workspace can only be selected for a given company
    storage.removeItem("workspace");
    window.$company = null;
    window.$workspace = null;
    emitCompanyChange();
    hideCompanyModal();
  } else {
    storage.setItem("company", json);
    if (!$workspacesUpdatedBind) {
      const pusherInit = new CustomEvent('pusher-init', { detail: {} });
      document.dispatchEvent(pusherInit);
    }
    window.$company = JSON.parse(json);
    if (window.$company && window.$company.id) {
      document.cookie = `company-id=${window.$company.id}; path=/;`;
      // If the company has changed, then we need to update the permissions
      if (previousCompany && previousCompany.id !== window.$company.id) {
        updatePermissions();
      }
    }
    enableCompanySelection();

    let logo = document.querySelector(".company-drawer__logo.logo-outline");
    if (logo && window.$company && window.$company.url) {
      logo.style = `background-image: url('${window.$company.url}')`;
    }
  }
  setCompanyAndWorkspaceLabel();
};

const setWorkspaceToStorage = (obj) => {
  const json = translateJson(obj);
  const storage = window.sessionStorage;
  const previousWorkspace = getWorkspaceFromStorage();

  if (!json) {
    storage.removeItem("workspace");
    window.$workspace = null;
    document.cookie = `workspace-id=`;
  } else {
    storage.setItem("workspace", json);
    window.$workspace = JSON.parse(json);
    if (window.$workspace) {
      destroyWorkspaceCookies();
      document.cookie = `workspace-id=${window.$workspace.id}; path=/;`;
      document.cookie = `current-workspace-id=${window.$workspace.id}; path=/;`;
      if (previousWorkspace && previousWorkspace.id !== window.$workspace.id) {
        updatePermissions();
      }
    }
  }
  hideCompanyModal();
  setCompanyAndWorkspaceLabel();
  emitCompanyChange();
};

const emitCompanyChange = () => {
  const isHelpCenter = location.pathname.startsWith('/help_center');
  if (workspacesLoaded) {
    return;
  }
  if (window.$company && $permissions && (companyModule() != "HelpTicket" || window.$workspace) || isHelpCenter) {
    workspacesLoaded = true;
    const companyChange = new CustomEvent("company-change", { detail: {} });
    document.dispatchEvent(companyChange);
    return;
  }
  if (isHelpCenter) {
    return;
  }
  if (!window.$company && !companyTrigger) {
    loadCompanyFromStorage();
    return;
  }
  if (!window.$workspace && !workspaceTrigger) {
    loadWorkspaceFromStorage();
    workspaceTrigger = true;
    return;
  }
  if (!$permissions && !loadingPermissions) {
    updatePermissions();
    return;
  }
};

const getCompanyFromStorage = () => {
  const storage = window.sessionStorage;
  const jsonString = storage.getItem("company");
  let company = null;

  if (jsonString) {
    try {
      company = JSON.parse(jsonString);
      if (location.hostname.indexOf(company.subdomain) < 0) {
        return null;
      }
    } catch (e) {
      return null;
    }
  }
  return company;
};

const getWorkspaceFromStorage = () => {
  const storage = window.sessionStorage;
  const json = storage.getItem("workspace");
  let workspace = null;
  if (json) {
    try {
      workspace = JSON.parse(json);
    } catch (e) {
      return null;
    }
  }
  return workspace;
};

const loadCompanyFromCookie = () => {
  const cookies = document.cookie.split(";");
  cookies.forEach((cookie) => {
    if (cookie.startsWith("company-id=")) {
      const parts = cookie.split("=");
      const id = parseInt(parts[1]);
      if (Array.isArray(companyOptions)) {
        return companyOptions.find((o) => o.id === id);
      }
    }
  });
  return null;
};

const destroyhelpTicketsCookies = () => {
  const helpTicketCookies = ['workspace-id', 'current-workspace-id', 'company-id'];
  document.cookie.split(";").forEach((c) => {
    const cookieName = c.split("=")[0].trim();
    if (helpTicketCookies.includes(cookieName)) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    }
  });
};

const destroyWorkspaceCookies = () => {
  const paths = ["/", "/help_tickets", "/help_tickets/all"];
  document.cookie.split(";").forEach((c) => {
    if (c.trim().startsWith("current-workspace") || c.trim().startsWith("workspace")) {
      for (let i = 0; i < paths.length; i += 1) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + "Thu, 01 Jan 1970 00:00:01 GMT" + `;path=${paths[i]}`);
      }
    }
  });
};

const loadWorkspaceFromCookie = () => {
  const cookies = document.cookie.split(";");
  let workspace = null;
  cookies.forEach((cookie) => {
    if (cookie.trim().startsWith("workspace-id=")) {
      const parts = cookie.split("=");
      const id = parseInt(parts[1]);
      workspace = $workspaceOptions.find((o) => o.id === id);
    }
  });
  return workspace;
};

const loadWorkspaceFromStorage = () => {
  const xhr = new XMLHttpRequest();
  let workspace;
  let company = getCompanyFromStorage();
  window.$workspace = null;
  $workspaceOptions = null;

  xhr.onreadystatechange = function() {
    if (xhr.readyState == XMLHttpRequest.DONE) {
      const data = parseJsonString(xhr.responseText);
      if (xhr.status == 200 && data) {
        $workspaceOptions = data;
        if ($workspaceOptions.length === 1) {
          workspace = $workspaceOptions[0];
        } else if ($workspaceOptions.length > 1) {
          if (location.search && location.search.split('=')[0] == '?workspace_id') {
            workspace = $workspaceOptions.find((o) => o.id == location.search.split('=')[1]);
          }
          if (!workspace) {
            workspace = loadWorkspaceFromCookie();
          }
          if (!workspace) {
            $workspaceOptions.forEach(option => {
              if (option.default) {
                workspace = option;
              }
            })
          }
          const storage = window.sessionStorage;
          let workspaceInStorage = JSON.parse(storage.getItem('workspace'));
          if (!workspace) {
            workspace = workspaceInStorage && $workspaceOptions.some(option => option.id === workspaceInStorage.id) ? workspaceInStorage : $workspaceOptions[0];
          }
        }
        setWorkspaceToStorage(workspace);
        setCompanyAndWorkspaceLabel();
      }
    }
  };
  const isHelpCenter = location.pathname.startsWith('/help_center');
  const isPublicFaqs = location.pathname.includes('/faqs') && location.pathname !== '/help_tickets/faqs';
  const isReport = location.pathname.includes('/reports');
  if (companyModule() == 'HelpTicket' || isHelpCenter || isReport || isPublicFaqs) {
    let url = `/workspace_options.json`;
    url = `${url}?privilege_name=${companyModule()}`;
    xhr.open('GET', encodeURI(url), true);
    xhr.setRequestHeader('X-CSRF-Token', document.querySelector('meta[name="csrf-token"]').attributes['content'].value);
    xhr.setRequestHeader('X-Key-Inflection', 'camel');
    if (company) {
      xhr.setRequestHeader('X-Genuity-Company-Id', `${company.id}`);
    }
    if (workspace) {
      xhr.setRequestHeader('X-Genuity-Workspace-Id', `${workspace.id}`);
    }
    xhr.send();
  }
  else {
    updatePermissions();
  }
};

const setCompanyAndWorkspaceLabel = () => {
  if (!window.$company) {
    return;
  }

  const companyName = document.querySelector(".company-drawer__name");
  if (companyName) {
    companyName.innerText = window.$company.name;
  }

  if (window.$workspace && window.$company.workspaceIds) {
    const workspaceName = document.querySelector(".workspace-drawer__name");
    const workspaceDropdownArrow = document.querySelector(".workspace-drawer__dropdown-arrow");
    const dynamicIcon = document.querySelector('.workspace-icon');
    const dynamicIconBg = document.querySelector('.workspace-name-icon-bg');
  
    if (workspaceName) {
      workspaceName.innerText = window.$workspace.name;
    }

    if (dynamicIcon && dynamicIconBg) {
      const { iconClass, colorClass } = window.$workspace;
      
      dynamicIcon.className = `ml-2 mr-n1 small workspace-icon ${iconClass} text-${colorClass}`;
      dynamicIconBg.className = `workspace-name-icon-bg bg-${colorClass}`;
    }

    if (workspaceDropdownArrow) {
      workspaceDropdownArrow.classList.remove("d-none");
    }
  }

  hideAddWorkspace();
};

var debounceTimeout; // Var used intentionally, for safe global scoping
function debounce(func, wait) {
  return function executed() {
    const context = this;
    const args = arguments;
    const later = function () {
      debounceTimeout = null;
      func.apply(context, args);
    };
    clearTimeout(debounceTimeout);
    debounceTimeout = setTimeout(later, wait);
  }
}

const updateCompanySearch = () => {
  setCompanyLinksLoading();
  fetchCompanyOptions();
};

const updateManagingCompanySearch = () => {
  fetchUserCompanies();
};

const clearManagingSearch = () => {
  const searchValue = document.querySelector("#search_managing_company");
  searchValue.value = '';
  updateManagingCompanySearch();
};

const populateWorkspaceDropdown = (options) => {
  // When you first open the modal the selected company and active company are the same, so add both classes.
  if (options) {
    options.forEach((w) => {
      addWorkspaceLink(w);
    });

    // hide settingsMenus on workspaceMenu content click
    const workspaceMenu = document.querySelector(".workspace-select-menu");
    if (workspaceMenu) {
      workspaceMenu.addEventListener('click', (e) => {
        hideWorkspaceSettingsMenus();
      })
    };
  }
};

const setWorkspaceOptions = (options) => {
  $workspaceOptions = options;
  if (isCompanySelectOpen() || isWorkspaceSelectOpen()) {
    populateWorkspaceDropdown(options);
  }
  setCompanyAndWorkspaceLabel();
};

const fetchWorkspaceOptions = async () => {
  $workspaceOptions = null;
  clearWorkspaceDropdowns();
  const fetchWorkspaceOptions = await fetch("/workspace_options.json", { headers: getWorkspaceHeaders($selectedCompany) })
  const options = await fetchWorkspaceOptions.json();
  setWorkspaceOptions(options);
};

(() => {
  const setupWorkspaceListener = () => {
    const pusher = Vue.prototype.$pusher;
    const company = getCompanyFromStorage();
    if (pusher && company && !$workspacesUpdatedBind) {
      const userGuid = Vue.prototype.$currentUserGuid;
      const channel = pusher.subscribe(`user=${userGuid}`);
      channel.bind("workspaces-updated", () => {
        loadCompanyFromStorage();
        loadWorkspaceFromStorage();
      }, this);
      $workspacesUpdatedBind = true;
    }
  };

  if (Vue.prototype.$pusher) {
    setupWorkspaceListener();
  } else {
    document.addEventListener('pusher-init', () => {
      setupWorkspaceListener();
    });
  }
})();

const openWorkspaceInfoModal = (e) => {
  e.preventDefault();
  document.querySelector('.workspaces-info-modal-wrap').classList.remove('d-none');
};

const closeWorkspaceInfoModal = (e) => {
  const infoModal = document.querySelector('.workspaces-info-modal-wrap');
  e.preventDefault();
  e.stopPropagation();
  if ((e.target == e.currentTarget || e.type !== "click") && infoModal) {
    infoModal.classList.add('d-none');
  }
};

const closeWorkspaceInfoModalOnEsc = (e) => {
  const infoModal = document.querySelector('.workspaces-info-modal-wrap');
  if (infoModal && infoModal.classList.contains('d-none')) {
    return;
  }
  if (e.key === "Escape") {
    const isNotCombinedKey = !(e.ctrlKey || e.altKey || e.shiftKey);
    if (isNotCombinedKey) {
      closeWorkspaceInfoModal(e);
    }
  }
};

const bindWorkspaceInfoModal = () => {
  // The conditional statements are a bit superfluous, but a failure breaks the whole functionality
  // So using the conditional check to help future proof this.
  const helpLink = document.querySelector('.workspaces-help-link');
  if (helpLink) {
    helpLink.addEventListener('click', openWorkspaceInfoModal);
  }

  const modalCloseLink = document.querySelector('.close-workspace-info-modal')
  if (modalCloseLink) {
    modalCloseLink.addEventListener('click', closeWorkspaceInfoModal);
  }

  const infoModalWrap = document.querySelector('.workspaces-info-modal-wrap');
  if (infoModalWrap) {
    infoModalWrap.addEventListener('click', closeWorkspaceInfoModal);
  }

  document.addEventListener('keydown', closeWorkspaceInfoModalOnEsc, true);
};

document.addEventListener('DOMContentLoaded', bindWorkspaceInfoModal);


const hideAddWorkspace = () => {
  const workspace = getWorkspaceFromStorage();
  if ($permissions && $permissions.helpTicket && workspace && workspace.id) {
    Object.entries($permissions.helpTicket).forEach(([key, value]) => {
      if (workspace.id == parseInt(key)) {
        if (value[0] != 'write') {
          const element = document.getElementsByClassName('workspace-link--add');
          element[0].style.display = 'none';
        }
      }
    })
  }
}

const hasWriteAllPermission = (id) => {
  if (!$permissions) {
    return false;
  }
  const modPerms = $permissions.helpTicket[id];
  if (Array.isArray(modPerms)) {
    return modPerms.includes('write');
  } else {
    return false;
  }
};

const renderCompanyName = (fullName, targetElement) => {
  const compNameSpan = document.createElement("span");
  compNameSpan.classList.add("company-select__name");
  if (fullName.length > 50) {
    compNameSpan.innerText = `${fullName.slice(0, 20)}...${fullName.slice(-15)}`;
    const tooltip = document.createElement("div");
    tooltip.classList.add("company-name-tooltip");
    tooltip.textContent = fullName;
    document.body.appendChild(tooltip);
    compNameSpan.addEventListener("mouseenter", () => {
      const rect = compNameSpan.getBoundingClientRect();
      const tooltipHeight = tooltip.offsetHeight;
      let top = rect.top - tooltipHeight - 6;
      if (top < 10) {
        top = rect.bottom + 6;
      }
      tooltip.style.top = `${top}px`;
      tooltip.style.opacity = "1";
    });
    compNameSpan.addEventListener("mouseleave", () => {
      tooltip.style.opacity = "0";
    });
    const nameWrapper = document.createElement("span");
    nameWrapper.classList.add("company-name-wrapper");
    nameWrapper.appendChild(compNameSpan);
    targetElement.appendChild(nameWrapper);
  } else {
    compNameSpan.innerText = fullName;
    targetElement.appendChild(compNameSpan);
  }
};
