var LoadSessionImages = (function () {

  function LoadSessionImages(bgUrl, fgUrl) {
    this.bgUrl = bgUrl;
    this.fgUrl = fgUrl;
    this.getBackgroundImage();
    this.getForegroundImage();
  }

  LoadSessionImages.prototype.getBackgroundImage = function () {
    var bgImage = new Image();
    bgImage.onload = onBgLoad;
    bgImage.src = this.bgUrl;
  };

  LoadSessionImages.prototype.getForegroundImage = function () {
    var fgImage = new Image();
    fgImage.onload = onFgLoad;
    fgImage.src = this.fgUrl;
  };

  function onBgLoad() {
    var sessionsBg = document.querySelector('.sessions-bg');
    sessionsBg.style.backgroundImage = "url('" + this.src + "')";
    requestAnimationFrame(function () {
      sessionsBg.offsetWidth; // Trigger a reflow to make sure there's no pending paints.
      sessionsBg.classList.add('is-loaded');
    });
  }

  function onFgLoad() {
    var sessionsFg = document.querySelector('.sessions-fg');
    sessionsFg.style.backgroundImage = "url('" + this.src + "')";
    requestAnimationFrame(function () {
      sessionsFg.offsetWidth; // Trigger a reflow to make sure there's no pending paints.
      sessionsFg.classList.add('is-loaded');
    })
  }

  return LoadSessionImages;

}());
