
function deleteCompany(e) {
  e.preventDefault();
  e.stopPropagation();
  let id = parseInt(e.target.dataset.companyid);
  if (id) {
    ActiveAdmin.modal_dialog("Please write company's name to confirm deletion!", {
        'company name': 'text'
      }, function(inputs) {
        var xhttp = new XMLHttpRequest();

        xhttp.onreadystatechange = function() {
          if (this.readyState == 4) {
            var loc = window.location;
            window.location = loc.protocol + "//" + loc.host + "/admin/companies";
          }
        };

        let data = `company_name=${inputs['company name']}`;
        let url = `/admin/companies/${id}.json?${data}`;

        xhttp.open('DELETE', encodeURI(url), true);
        xhttp.setRequestHeader('X-CSRF-Token', document.querySelector('meta[name="csrf-token"]').attributes['content'].value);
        xhttp.setRequestHeader('X-Key-Inflection', 'camel');

        xhttp.send();
      }
    )
  }
}