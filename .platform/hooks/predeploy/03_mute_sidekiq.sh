#!/bin/sh
set -xe

# if this isn't a worker, then just exit out
if [ -z "$WORKER" ]; then
  exit
fi

WORKER_PROCESSES=(sidekiq_default sidekiq_critical sidekiq_import sidekiq_automated_tasks sidekiq_email sidekiq_integrations)

# Stop and kill all existing Sidekiq services
for i in "${WORKER_PROCESSES[@]}"; do
  if sudo systemctl is-active --quiet "sidekiq@$i.service"; then
    sudo systemctl stop "sidekiq@$i.service"
    sudo systemctl kill "sidekiq@$i.service"
  fi
done
