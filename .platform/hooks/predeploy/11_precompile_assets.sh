#!/usr/bin/env bash
set -xe

# if this is a worker, then just exit out
if [ $WORKER ]
then
  exit
fi
echo "Running $(basename $BASH_SOURCE)" 

EB_STAGING_DIR=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppStagingDir)
EB_USER=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppUser)

echo "App user is $EB_USER"

# First, make sure rails is executable
chmod +x bin/rails

# Ensure we are using the correct node
source ~/.nvm/nvm.sh

gem update --system --no-document

# mount the efs
#mkdir -p /mnt/efs/$RAILS_ENV/bundle
mkdir -p /mnt/efs/$RAILS_ENV/packs
mkdir -p /mnt/efs/$RAILS_ENV/assets

# ln -s /mnt/efs/$RAILS_ENV/bundle vendor/bundle
# ln -s /mnt/efs/$RAILS_ENV/packs public/packs
# ln -s /mnt/efs/$RAILS_ENV/assets public/assets

cd $EB_STAGING_DIR

yarn add -D webpack-cli@4.8.0 -y

export NODE_OPTIONS=--openssl-legacy-provider

yes | bin/rails assets:precompile
