#!/bin/sh

EB_STAGING_DIR=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppStagingDir)
EB_USER=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppUser)

cd $EB_STAGING_DIR

mkdir -p $EB_STAGING_DIR/tmp/pids
chown $EB_USER:$EB_USER $EB_STAGING_DIR/tmp
chown $EB_USER:$EB_USER $EB_STAGING_DIR/tmp/pids

mkdir -p $EB_STAGING_DIR/public/assets
chown $EB_USER:$EB_USER $EB_STAGING_DIR/public/assets

mkdir -p $EB_STAGING_DIR/.bundle
chown $EB_USER:$EB_USER $EB_STAGING_DIR/.bundle

#mkdir -p $EB_STAGING_DIR/tmp/cache
#chown $EB_USER:$EB_USER $EB_STAGING_DIR/tmp/cache
