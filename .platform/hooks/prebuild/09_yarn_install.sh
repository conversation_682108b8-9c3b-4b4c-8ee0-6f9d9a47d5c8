#!/usr/bin/env bash
#set -xe

echo "Running $(basename $BASH_SOURCE)"

# if this is a worker, then just exit out
if [ $WORKER ]
then
  exit
fi

EB_STAGING_DIR=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppStagingDir)
EB_USER=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppUser)

echo "App user is $EB_USER"

# Download the yarn repo
sudo wget https://dl.yarnpkg.com/rpm/yarn.repo -O /etc/yum.repos.d/yarn.repo
# Confirm that it downloaded
file /etc/yum.repos.d/yarn.repo

yum upgrade 
# If node is not detected, install it.
if [ `nvm --version` == '0.34.0' ]; then
  echo "Skipping installation of nvm -- nvm already installed."
  echo "nvm --version: `nvm --version`"
else
  echo "Installing NVM ..."
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.34.0/install.sh | bash
  source ~/.bash_profile
  echo "... and finished installing NVM and Node"
fi

# use node version 20
nvm install 20.18.0
nvm use 20.18.0
nvm alias default 20.18.0

# install yarn
npm install -g yarn
yarn --version

echo "... and finished installing yarn."

echo "Change directory to $EB_STAGING_DIR"
cd $EB_STAGING_DIR

# link to the shared efs
#ln -s /mnt/efs/$RAILS_ENV/node_modules node_modules

# yarn install
echo "Running yarn install."

# npm install -g webpack-cli
yarn install --production

chown -R $EB_USER:$EB_USER node_modules
