#!/bin/sh
set -xe

# if this isn't a worker, then just exit out
if [ -z "$WORKER" ]; then
  exit
fi


# This code is written to separate the integration worker from the default worker on production.
# This is because the integration worker is a special worker that is only used for integration related jobs.
if [ $INTEGRATION_WORKER -a $RAILS_ENV == "production" ]
then
  WORKER_PROCESSES=(sidekiq_bulk_operations sidekiq_integrations sidekiq_assets sidekiq_high_intensity sidekiq_backup)
elif [ $RAILS_ENV == "staging" ]
then
  WORKER_PROCESSES=(sidekiq_critical sidekiq_automated_tasks sidekiq_low_intensity sidekiq_bulk_operations sidekiq_integrations sidekiq_assets sidekiq_high_intensity sidekiq_critical_logs)
else
  WORKER_PROCESSES=(sidekiq_critical sidekiq_automated_tasks sidekiq_low_intensity sidekiq_critical_logs)
fi

EB_APP_DEPLOY_DIR=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppDeployDir)
EB_APP_USER=$(/opt/elasticbeanstalk/bin/get-config platformconfig -k AppUser)
EB_APP_LOGS_DIR="$EB_APP_DEPLOY_DIR/log"

# Stop and kill all existing Sidekiq services
for i in "${WORKER_PROCESSES[@]}"; do
  if sudo systemctl is-active --quiet "sidekiq@$i.service"; then
    sudo systemctl stop "sidekiq@$i.service"
    sudo systemctl kill "sidekiq@$i.service"
  fi
done

# Create new Sidekiq services for each process
for i in "${WORKER_PROCESSES[@]}"; do
  sudo tee "/etc/systemd/system/sidekiq@$i.service" << END
[Unit]
Description=Sidekiq worker $i
After=syslog.target network.target

[Service]
Type=simple
WorkingDirectory=$EB_APP_DEPLOY_DIR
Environment=RAILS_ENV=$RAILS_ENV
ExecStart=/bin/bash -lc '$EB_APP_DEPLOY_DIR/bin/bundle exec sidekiq -C $EB_APP_DEPLOY_DIR/config/$i.yml -e $RAILS_ENV > $EB_APP_LOGS_DIR/$i.log 2>&1'
User=$EB_APP_USER
Group=$EB_APP_USER
Restart=always
KillMode=control-group
KillSignal=SIGTERM
TimeoutStopSec=60

[Install]
WantedBy=multi-user.target
END
done

# Reload systemd configuration
sudo systemctl daemon-reload

# Enable and start new Sidekiq services
for i in "${WORKER_PROCESSES[@]}"; do
  sudo systemctl enable --now "sidekiq@$i.service"
done


#Script to manually kill and restart all the processes

# WORKER_PROCESSES=(sidekiq_integrations)
# WORKER_PROCESSES=(sidekiq_default sidekiq_critical sidekiq_import sidekiq_automated_tasks sidekiq_email)

# for i in "${WORKER_PROCESSES[@]}"; do
#   if sudo systemctl is-active --quiet "sidekiq@$i.service"; then
#     sudo systemctl stop "sidekiq@$i.service"
#     sudo systemctl kill "sidekiq@$i.service"
#   fi
# done

# sudo systemctl daemon-reload

# for i in "${WORKER_PROCESSES[@]}"; do
#   sudo systemctl enable --now "sidekiq@$i.service"
# done
