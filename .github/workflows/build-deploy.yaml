name: Build and Deploy Genuity

on:
  push:
    branches:
      - staging

jobs:

  build:

    runs-on: ubuntu-latest

    environment: ${{ github.ref == 'refs/heads/staging' && 'staging' || 'production' }}

    env:
      DEPLOY_ENV: ${{ github.ref == 'refs/heads/staging' && 'staging' || 'production' }}
      HUSKEY: 0

    permissions:
      id-token: write
      contents: read

    steps:

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create secrets file
        run: |
          echo "${{ secrets.SECRET_STAGING_KEY }}" >> config/credentials/staging.key

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ secrets.REPO_NAME }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            ENV=${{ env.DEPLOY_ENV }}

  deploy:
    runs-on: ubuntu-latest

    needs: build

    environment: ${{ github.ref == 'refs/heads/staging' && 'staging' || 'production' }}

    env:
      DEPLOY_ENV: ${{ github.ref == 'refs/heads/staging' && 'staging' || 'production' }}

    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Set up Helm
        run: |
          curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      
      - name: Login to ECR for Helm
        run: |
          aws ecr get-login-password --region ${{ secrets.AWS_REGION }} | helm registry login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com

      - name: Update Helm repository
        run: |
          helm pull oci://************.dkr.ecr.us-west-2.amazonaws.com/genuity-micro-service --version 0.1.0

      - name: Update EKS config
        run: |
          aws eks --region ${{ secrets.AWS_REGION }} update-kubeconfig --name genuity-${{ env.DEPLOY_ENV }}

      - name: Deploy to EKS using Helm
        run: |
          helm upgrade --install genuity genuity-micro-service-0.1.0.tgz  \
            --set image.repository=${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ secrets.REPO_NAME }} \
            --set image.tag=${{ github.sha }} \
            --namespace genuity-${{ env.DEPLOY_ENV }} \
            --create-namespace
