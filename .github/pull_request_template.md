### 🎫 Jira Story
[GN-](https://gogenuity.atlassian.net/browse/GN-)

### 📹 Video Demo
<!-- Attach video demo -->

### ✅ Code Quality Checklist
#### 🔧 General (If applicable)
- [ ] Meaningful commit messages provided
- [ ] Code is properly indented and formatted
- [ ] Followed proper naming conventions for variables, methods, and file names, etc.
- [ ] Code comments added for complex logic
- [ ] DRY principles followed
- [ ] No hardcoded values
- [ ] Feature flag added for new feature
- [ ] Environment variables updated/added
- [ ] No Preventive Checks for Bugsnags Fixes

#### 💎 Backend
- [ ] Relevant RSpecs added/updated
- [ ] Database migrations are reversible
- [ ] Rake tasks documented and shared with team (if added/modified)

#### 🎨 Frontend
- [ ] No inline styling

### 🧪 Testing
- [ ] RSpec tests related to your work are green (if backend changes)
- [ ] Have done manual testing after making changes
- [ ] Edge cases covered
- [ ] Have added QA testing notes in the Jira story

### 📚 Documentation
- [ ] Documentation(Confluence/Document360) updated. Attach the link in the additional notes if we have a separate story for this (if needed)
- [ ] README updated if needed

### 🔍 Additional Notes
<!-- Any additional information, concerns, or areas for review -->
